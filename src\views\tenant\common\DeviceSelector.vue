<template>
    <div class="device-selector">
        <!-- 设备选择表格 -->
        <el-container class="device-selection-container">
            <div class="header-container">
                <div class="entity-info" v-if="entityInfo.title || entityInfo.content">
                    <el-form-item :label="entityInfo.title + '：'">
                        <span>{{ entityInfo.content }}</span>
                    </el-form-item>
                </div>
                <!-- 搜索栏，根据showSearchBar属性控制显示 -->
                <div class="search-bar" v-if="showSearchBar">
                    <div class="right-panel-search">
                        <el-input
                            v-model="localSearchParams.keyword"
                            placeholder="设备名称 / 设备标识"
                            @input="handleSearch"
                            @clear="handleSearch"
                            clearable
                        >
                        </el-input>
                        <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="handleSearch"
                            >搜索
                        </el-button>
                    </div>
                </div>
            </div>
            <el-main class="table-container">
                <scTable
                    ref="deviceDataTable"
                    :apiObj="currentApiObj"
                    :params="localSearchParams"
                    remoteFilter
                    remoteSort
                    stripe
                    row-key="id"
                    @selection-change="handleSelectionChange"
                    @row-click="handleRowClick"
                    @load="handleTableLoad"
                    class="custom-list-table"
                >
                    <el-table-column type="selection" width="50" :reserve-selection="true" />
                    <el-table-column prop="name" label="设备名称" show-overflow-tooltip />
                    <el-table-column prop="code" label="设备标识" show-overflow-tooltip />
                    <el-table-column v-if="showDeviceStatus" prop="status" label="设备状态" show-overflow-tooltip>
                        <template #default="scope">
                            <el-tag :type="getDeviceStatusType(scope.row.status)">
                                {{ getDeviceStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="showOnlineStatus" label="在线状态" width="100">
                        <template #default="scope">
                            <div @click.stop>
                                <DeviceOnlineStatus
                                    :device-id="scope.row.id"
                                    :initial-online="scope.row.online"
                                    @online-status-change="handleOnlineStatusChange"
                                />
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="description" label="备注" show-overflow-tooltip />
                    <slot name="additional-columns"></slot>
                </scTable>
            </el-main>
        </el-container>
    </div>
</template>

<script>
import { debounce } from '@/utils/throttleDebounce';
import { deviceBusinessStatusUtils } from './constants';
import DeviceOnlineStatus from '@/views/tenant/common/DeviceOnlineStatus.vue';

export default {
    name: 'DeviceSelector',
    components: {
        DeviceOnlineStatus
    },
    props: {
        // 初始选中的设备ID列表
        initialSelection: {
            type: Array,
            default: () => []
        },
        // 接口地址
        apiObj: {
            type: Object,
            default: () => ({})
        },
        // 搜索参数（包含keyword和其他特定参数）
        searchParams: {
            type: Object,
            default: () => ({
                keyword: ''
            })
        },
        // 是否允许行点击选择
        rowClickSelect: {
            type: Boolean,
            default: true
        },
        // 是否显示搜索栏
        showSearchBar: {
            type: Boolean,
            default: true
        },
        // 是否显示设备状态
        showDeviceStatus: {
            type: Boolean,
            default: false
        },
        // 是否显示设备在线状态
        showOnlineStatus: {
            type: Boolean,
            default: true
        },
        // 实体信息配置
        entityInfo: {
            type: Object,
            default: () => ({
                title: '',
                content: ''
            })
        },
        // 是否由DeviceListStatusManager管理
        managedByStatusManager: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        /**
         * 计算当前使用的接口地址
         * @returns {Object} 接口地址对象
         */
        currentApiObj() {
            return Object.keys(this.apiObj).length > 0 ? this.apiObj : this.$API.tenant.device.list;
        }
    },
    data() {
        return {
            statusOptions: deviceBusinessStatusUtils.getDeviceStatusOptions(),
            selection: [...this.initialSelection], // 当前选中的所有设备ID
            rowSelectFlag: false, // 禁止toggleRowSelection默认触发selection-change事件
            currentPageIds: [], // 当前页面所有设备的ID
            localSearchParams: { ...this.searchParams }
        };
    },
    watch: {
        initialSelection: {
            handler(newVal) {
                this.selection = [...newVal];
                // 刷新表格选中状态
                this.$nextTick(() => {
                    if (this.$refs.deviceDataTable) {
                        const table = this.$refs.deviceDataTable.$refs.scTable;
                        if (table && table.tableData) {
                            this.handleTableLoad(table.tableData);
                        }
                    }
                });
            },
            deep: true
        },
        searchParams: {
            handler(newVal) {
                this.localSearchParams = { ...newVal };
                // 当外部搜索参数变化时，自动刷新表格数据
                this.$nextTick(() => {
                    this.loadTableData();
                });
            },
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.setupTableMethodsOverride();
        });
    },
    methods: {
        /**
         * 获取设备状态文本
         * @param {Number} status 设备状态值
         * @returns {String} 状态文本
         */
        getDeviceStatusText(status) {
            return deviceBusinessStatusUtils.getDeviceStatusText(status);
        },

        /**
         * 获取设备状态标签类型
         * @param {Number} status 设备状态值
         * @returns {String} 标签类型
         */
        getDeviceStatusType(status) {
            return deviceBusinessStatusUtils.getDeviceStatusType(status);
        },

        /**
         * 表格数据加载完成
         * @param {Array} data 表格数据
         */
        handleTableLoad(data) {
            // 保存当前页面ID，用于选择处理
            this.currentPageIds = data.map(row => Number(row.id));

            // 设置选中状态
            this.$nextTick(() => {
                this.rowSelectFlag = true;

                const table = this.$refs.deviceDataTable.$refs.scTable;
                table.clearSelection();

                // 根据selection选中对应行
                data.forEach(row => {
                    if (this.selection.includes(Number(row.id))) {
                        table.toggleRowSelection(row, true);
                    }
                });

                // 恢复标志位并触发选择变更事件
                this.$nextTick(() => {
                    this.rowSelectFlag = false;

                    // 保证每次加载数据后都能向父组件重新发送一次选择状态
                    this.$emit('selection-change', [...this.selection]);

                    // 发送表格加载事件
                    this.$emit('table-load', data);

                    // 设备在线状态现在由 DeviceOnlineStatus 组件和 Pinia store 自动管理
                    // 不再需要手动检查设备状态
                });
            });
        },

        /**
         * 设备选择状态变更
         * @param {Array} selection 当前选中的行
         */
        handleSelectionChange(selection) {
            if (this.rowSelectFlag) return;

            // 获取当前页面选中ID
            const currentSelectedIds = selection.map(row => Number(row.id));

            // 更新选中状态：保留其他页的选择，更新当前页的选择
            this.selection = [...this.selection.filter(id => !this.currentPageIds.includes(id)), ...currentSelectedIds];

            // 向父组件传递选择变更事件
            this.$emit('selection-change', this.selection);
        },

        /**
         * 处理搜索操作
         */
        handleSearch: debounce(function () {
            this.loadTableData();
        }, 300),

        /**
         * 点击选择数据
         * @param {Object} row 行数据
         */
        handleRowClick(row) {
            if (this.rowClickSelect) {
                this.$refs.deviceDataTable.$refs.scTable.toggleRowSelection(row);
            }
        },

        /**
         * 加载表格数据
         */
        loadTableData() {
            // 加载表格数据
            if (!this.$refs.deviceDataTable) return;

            // 保存当前选中状态
            const currentSelection = [...this.selection];

            // 调用原始数据加载方法
            this.$refs.deviceDataTable.upData(this.localSearchParams);

            // 手动恢复选中状态
            this.$nextTick(() => {
                // 延迟设置选中状态，确保表格数据已加载完成
                setTimeout(() => {
                    this.selection = currentSelection;

                    // 如果表格有数据，设置选中状态
                    if (
                        this.$refs.deviceDataTable &&
                        this.$refs.deviceDataTable.$refs.scTable &&
                        this.$refs.deviceDataTable.$refs.scTable.tableData
                    ) {
                        this.handleTableLoad(this.$refs.deviceDataTable.$refs.scTable.tableData);
                    } else {
                        // 即使表格没有数据，也要确保向父组件发送选择状态
                        this.$emit('selection-change', [...this.selection]);
                    }
                }, 300);
            });
        },

        /**
         * 设置表格方法覆盖，确保刷新时保持选中状态
         */
        setupTableMethodsOverride() {
            const table = this.$refs.deviceDataTable;
            if (!table || table._methodsOverridden) return;

            table._methodsOverridden = true;

            // 创建保护选中状态的方法包装器
            const protectSelection = (target, _methodName, originalMethod) => {
                return function (...args) {
                    // 保存原始选中状态
                    const originalSelectedIds = [...this.selection];

                    // 调用原始方法
                    const result = originalMethod.apply(target, args);

                    // 恢复选中状态
                    this.$nextTick(() => {
                        this.selection = originalSelectedIds;
                    });

                    return result;
                }.bind(this);
            };

            // 劫持clearSelection、refresh和upData方法
            if (table.$refs.scTable) {
                table.$refs.scTable.clearSelection = protectSelection(
                    table.$refs.scTable,
                    'clearSelection',
                    table.$refs.scTable.clearSelection
                );
            }

            table.refresh = protectSelection(table, 'refresh', table.refresh);
            table.upData = protectSelection(table, 'upData', table.upData);
        },

        /**
         * 刷新表格
         */
        refresh() {
            if (this.$refs.deviceDataTable) {
                // 保存当前选中状态
                const currentSelection = [...this.selection];

                // 调用原始刷新方法
                this.$refs.deviceDataTable.refresh();

                // 手动恢复选中状态
                this.$nextTick(() => {
                    // 延迟设置选中状态，确保表格数据已加载完成
                    setTimeout(() => {
                        this.selection = currentSelection;

                        // 如果表格有数据，设置选中状态
                        if (
                            this.$refs.deviceDataTable &&
                            this.$refs.deviceDataTable.$refs.scTable &&
                            this.$refs.deviceDataTable.$refs.scTable.tableData
                        ) {
                            this.handleTableLoad(this.$refs.deviceDataTable.$refs.scTable.tableData);
                        } else {
                            // 即使表格没有数据，也要确保向父组件发送选择状态
                            this.$emit('selection-change', [...this.selection]);
                        }
                    }, 300);
                });
            }
        },

        /**
         * 获取当前选中的设备ID
         * @returns {Array} 选中的设备ID数组
         */
        getSelection() {
            return [...this.selection];
        },

        /**
         * 设置选中的设备ID
         * @param {Array} ids 设备ID数组
         */
        setSelection(ids) {
            this.selection = [...ids];
            this.$nextTick(() => {
                if (this.$refs.deviceDataTable && this.$refs.deviceDataTable.tableData) {
                    this.handleTableLoad(this.$refs.deviceDataTable.tableData);
                }
            });
        },

        /**
         * 清空选中
         */
        clearSelection() {
            this.selection = [];
            this.$nextTick(() => {
                if (this.$refs.deviceDataTable && this.$refs.deviceDataTable.$refs.scTable) {
                    this.$refs.deviceDataTable.$refs.scTable.clearSelection();
                }
            });
        },

        /**
         * 处理设备状态变化事件
         * @param {Object} status 设备状态对象 {deviceId, online, lastHeartbeat}
         */
        handleOnlineStatusChange(status) {
            // 直接触发设备状态变化事件，不需要再存储状态
            this.$emit('online-status-change', status);
        }
    }
};
</script>

<style lang="scss" scoped>
.device-selector {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.device-selection-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.header-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.entity-info {
    margin-right: auto;

    .el-form-item {
        margin-bottom: 0;

        :deep(.el-form-item__label) {
            font-weight: 600;
        }
    }
}

.search-bar {
    display: flex;
    justify-content: flex-end;
}

.right-panel-search {
    display: flex;
    align-items: center;
}

.table-container {
    padding: 0;
}

.selected-row {
    background-color: var(--el-color-primary-light-9);
}

.el-table {
    cursor: pointer;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
}

.empty-data {
    padding: 30px 0;
    text-align: center;
    color: #909399;

    .empty-tip {
        font-size: 12px;
        margin-top: 8px;
        color: #c0c4cc;
    }
}

.el-dialog {
    .header-container {
        margin-bottom: 12px;
    }

    .table-container {
        border: 1px solid #efefef;
        height: 300px;
    }
}
</style>
