/**
 * 设备在线状态 Pinia Store
 * 管理设备的在线状态、监听器和相关操作
 */
import { defineStore } from 'pinia'
import loggerFactory from '@/utils/logger'

// 创建日志记录器
const log = loggerFactory.createLogger('DeviceOnlineStore')

// 设备心跳超时时间（毫秒）
const HEARTBEAT_TIMEOUT = 30000 // 30秒

export const useDeviceOnlineStore = defineStore('deviceOnline', {
  state: () => ({
    // 设备状态缓存 Map<string, DeviceStatus>
    deviceStatuses: new Map(),
    
    // 状态监听器 Map<string, Set<ListenerInfo>>
    listeners: new Map(),
    
    // MQTT连接状态
    mqttConnected: false
  }),

  getters: {
    /**
     * 检查设备是否在线（同步方法）
     * @param {Object} state 
     * @returns {Function} 
     */
    isDeviceOnline: (state) => (deviceId) => {
      const deviceIdStr = String(deviceId)
      const status = state.deviceStatuses.get(deviceIdStr)
      return status ? status.online : false
    },

    /**
     * 获取设备状态对象
     * @param {Object} state 
     * @returns {Function}
     */
    getDeviceStatus: (state) => (deviceId) => {
      const deviceIdStr = String(deviceId)
      const status = state.deviceStatuses.get(deviceIdStr)
      
      if (!status) {
        return {
          deviceId: deviceIdStr,
          online: false,
          lastHeartbeat: null,
          lastUpdate: null
        }
      }
      
      return status
    },

    /**
     * 获取多个设备的在线状态
     * @param {Object} state 
     * @returns {Function}
     */
    getDevicesOnlineStatus: (state) => (deviceIds) => {
      if (!deviceIds?.length) return []
      
      return deviceIds.map(deviceId => {
        const deviceIdStr = String(deviceId)
        const isOnline = state.deviceStatuses.get(deviceIdStr)?.online || false
        return {
          deviceId: deviceIdStr,
          online: isOnline
        }
      })
    },

    /**
     * 获取所有在线设备数量
     * @param {Object} state 
     * @returns {number}
     */
    onlineDeviceCount: (state) => {
      let count = 0
      for (const status of state.deviceStatuses.values()) {
        if (status.online) count++
      }
      return count
    }
  },

  actions: {
    /**
     * 初始化设备状态（从后端API获取）
     * @param {string|number} deviceId 设备ID
     * @param {boolean} online 在线状态
     */
    initDeviceStatus(deviceId, online) {
      const deviceIdStr = String(deviceId)
      const now = Date.now()
      
      // 只有当缓存中没有状态时才初始化
      if (!this.deviceStatuses.has(deviceIdStr)) {
        const status = {
          deviceId: deviceIdStr,
          online: !!online,
          lastHeartbeat: online ? now : null,
          lastUpdate: now
        }
        
        this.deviceStatuses.set(deviceIdStr, status)
        log.debug(`初始化设备 ${deviceIdStr} 状态: ${online ? '在线' : '离线'}`)
      }
    },

    /**
     * 更新设备在线状态（从MQTT消息）
     * @param {string|number} deviceId 设备ID
     * @param {boolean} online 在线状态
     * @param {number} heartbeatTime 心跳时间戳（可选）
     */
    updateDeviceStatus(deviceId, online, heartbeatTime = null) {
      const deviceIdStr = String(deviceId)
      const now = Date.now()
      
      const status = {
        deviceId: deviceIdStr,
        online,
        lastHeartbeat: online ? (heartbeatTime || now) : null,
        lastUpdate: now
      }
      
      this.deviceStatuses.set(deviceIdStr, status)
      
      // 通知监听器
      this.notifyStatusChange(deviceIdStr)
    },

    /**
     * 批量初始化设备状态
     * @param {Array} devices 设备数组，每个元素包含 {id, online}
     */
    initMultipleDeviceStatus(devices) {
      if (!Array.isArray(devices)) return
      
      devices.forEach(device => {
        if (device && typeof device.id !== 'undefined') {
          this.initDeviceStatus(device.id, device.online)
        }
      })
    },

    /**
     * 添加设备状态监听器
     * @param {string} deviceId 设备ID
     * @param {Function} listener 监听器函数
     * @param {Object} context 上下文对象
     */
    addStatusListener(deviceId, listener, context) {
      const deviceIdStr = String(deviceId)
      
      if (!this.listeners.has(deviceIdStr)) {
        this.listeners.set(deviceIdStr, new Set())
      }

      const boundListener = context ? listener.bind(context) : listener
      const listenerInfo = { 
        original: listener, 
        bound: boundListener, 
        context 
      }

      this.listeners.get(deviceIdStr).add(listenerInfo)

      // 立即通知当前状态
      const status = this.getDeviceStatus(deviceIdStr)
      boundListener(status)
      
      return boundListener
    },

    /**
     * 移除设备状态监听器
     * @param {string} deviceId 设备ID
     * @param {Function} listener 监听器函数
     * @param {Object} context 上下文对象
     */
    removeStatusListener(deviceId, listener, context) {
      const deviceIdStr = String(deviceId)
      
      if (this.listeners.has(deviceIdStr)) {
        const listeners = this.listeners.get(deviceIdStr)
        const listenerInfo = Array.from(listeners).find(info =>
          info.original === listener && info.context === context
        )

        if (listenerInfo) {
          listeners.delete(listenerInfo)
          if (listeners.size === 0) {
            this.listeners.delete(deviceIdStr)
          }
        }
      }
    },

    /**
     * 通知设备状态变更
     * @param {string} deviceId 设备ID
     */
    notifyStatusChange(deviceId) {
      const deviceIdStr = String(deviceId)
      
      if (!this.listeners.has(deviceIdStr)) {
        return
      }

      const status = this.getDeviceStatus(deviceIdStr)
      const listeners = this.listeners.get(deviceIdStr)

      listeners.forEach(listenerInfo => {
        try {
          listenerInfo.bound(status)
        } catch (error) {
          log.error(`设备状态监听器执行错误:`, error)
        }
      })
    },

    /**
     * 设置MQTT连接状态
     * @param {boolean} connected 是否连接
     */
    setMqttConnected(connected) {
      this.mqttConnected = connected
    },

    /**
     * 清理过期的设备状态
     */
    cleanupExpiredStatus() {
      const now = Date.now()
      const SEVEN_DAYS = 7 * 24 * 60 * 60 * 1000
      let cleanedCount = 0

      for (const [deviceId, status] of this.deviceStatuses.entries()) {
        if (status.lastUpdate && now - status.lastUpdate > SEVEN_DAYS) {
          this.deviceStatuses.delete(deviceId)
          cleanedCount++
        }
      }

      if (cleanedCount > 0) {
        log.group('清理长期不活跃设备状态')
        log.info(`清理了 ${cleanedCount} 个长期不活跃设备状态`)
        log.info(`清理时间: ${new Date().toLocaleString()}`)
        log.groupEnd()
      }
    },

    /**
     * 清除所有设备状态
     */
    clearAllStatus() {
      this.deviceStatuses.clear()
      this.listeners.clear()
    }
  }
})
