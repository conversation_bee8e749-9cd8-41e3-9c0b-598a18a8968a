/**
 * 设备在线状态 Pinia Store
 * 管理设备的在线状态、监听器和相关操作
 */
import { defineStore } from 'pinia'
import loggerFactory from '@/utils/logger'

// 创建日志记录器
const log = loggerFactory.createLogger('DeviceOnlineStore')

// 设备心跳超时时间（毫秒）
const HEARTBEAT_TIMEOUT = 30000 // 30秒

export const useDeviceOnlineStore = defineStore('deviceOnline', {
  state: () => ({
    // 设备状态缓存 Map<string, DeviceStatus>
    deviceStatuses: new Map(),

    // MQTT连接状态
    mqttConnected: false
  }),

  getters: {
    /**
     * 检查设备是否在线（同步方法）
     * @param {Object} state
     * @returns {Function}
     */
    isDeviceOnline: (state) => (deviceId) => {
      const deviceIdStr = String(deviceId)
      const status = state.deviceStatuses.get(deviceIdStr)
      return status ? status.online : false
    },

    /**
     * 获取设备状态对象
     * @param {Object} state
     * @returns {Function}
     */
    getDeviceStatus: (state) => (deviceId) => {
      const deviceIdStr = String(deviceId)
      const status = state.deviceStatuses.get(deviceIdStr)

      if (!status) {
        return {
          deviceId: deviceIdStr,
          online: false,
          lastHeartbeat: null,
          lastUpdate: null
        }
      }

      return status
    },

    /**
     * 获取多个设备的在线状态
     * @param {Object} state
     * @returns {Function}
     */
    getDevicesOnlineStatus: (state) => (deviceIds) => {
      if (!deviceIds?.length) return []

      return deviceIds.map(deviceId => {
        const deviceIdStr = String(deviceId)
        const isOnline = state.deviceStatuses.get(deviceIdStr)?.online || false
        return {
          deviceId: deviceIdStr,
          online: isOnline
        }
      })
    },

    /**
     * 获取所有在线设备数量
     * @param {Object} state
     * @returns {number}
     */
    onlineDeviceCount: (state) => {
      let count = 0
      for (const status of state.deviceStatuses.values()) {
        if (status.online) count++
      }
      return count
    }
  },

  actions: {
    /**
     * 初始化设备状态（从后端API获取）
     * @param {string|number} deviceId 设备ID
     * @param {boolean} online 在线状态
     */
    initDeviceStatus(deviceId, online) {
      const deviceIdStr = String(deviceId)
      const now = Date.now()

      // 只有当缓存中没有状态时才初始化
      if (!this.deviceStatuses.has(deviceIdStr)) {
        const status = {
          deviceId: deviceIdStr,
          online: !!online,
          lastHeartbeat: online ? now : null,
          lastUpdate: now
        }

        this.deviceStatuses.set(deviceIdStr, status)
        log.debug(`初始化设备 ${deviceIdStr} 状态: ${online ? '在线' : '离线'}`)
      }
    },

    /**
     * 更新设备在线状态（从MQTT消息）
     * @param {string|number} deviceId 设备ID
     * @param {boolean} online 在线状态
     * @param {number} heartbeatTime 心跳时间戳（可选）
     */
    updateDeviceStatus(deviceId, online, heartbeatTime = null) {
      const deviceIdStr = String(deviceId)
      const now = Date.now()

      // 获取现有状态以保留最后心跳时间
      const existingStatus = this.deviceStatuses.get(deviceIdStr)

      const status = {
        deviceId: deviceIdStr,
        online,
        // 心跳时间逻辑：
        // 1. 如果设备在线且提供了心跳时间，使用提供的时间
        // 2. 如果设备在线但没有提供心跳时间，使用当前时间
        // 3. 如果设备离线，保留最后的心跳时间（不更新）
        lastHeartbeat: online
          ? (heartbeatTime || now)
          : (existingStatus?.lastHeartbeat || null),
        lastUpdate: now
      }

      this.deviceStatuses.set(deviceIdStr, status)
    },

    /**
     * 批量初始化设备状态
     * @param {Array} devices 设备数组，每个元素包含 {id, online}
     */
    initMultipleDeviceStatus(devices) {
      if (!Array.isArray(devices)) return

      devices.forEach(device => {
        if (device && typeof device.id !== 'undefined') {
          this.initDeviceStatus(device.id, device.online)
        }
      })
    },

    /**
     * 设置MQTT连接状态
     * @param {boolean} connected 是否连接
     */
    setMqttConnected(connected) {
      this.mqttConnected = connected
    },

    /**
     * 清理过期的设备状态
     */
    cleanupExpiredStatus() {
      const now = Date.now()
      const SEVEN_DAYS = 7 * 24 * 60 * 60 * 1000
      let cleanedCount = 0

      for (const [deviceId, status] of this.deviceStatuses.entries()) {
        if (status.lastUpdate && now - status.lastUpdate > SEVEN_DAYS) {
          this.deviceStatuses.delete(deviceId)
          cleanedCount++
        }
      }

      if (cleanedCount > 0) {
        log.group('清理长期不活跃设备状态')
        log.info(`清理了 ${cleanedCount} 个长期不活跃设备状态`)
        log.info(`清理时间: ${new Date().toLocaleString()}`)
        log.groupEnd()
      }
    },

    /**
     * 清除所有设备状态
     */
    clearAllStatus() {
      this.deviceStatuses.clear()
    }
  }
})
