<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button
                    v-auths="['tenant.device.add']"
                    icon="el-icon-plus"
                    type="primary"
                    @click="add"
                    color="#1C409A"
                    >添加设备</el-button
                >
                <el-button
                    v-auths="['tenant.device.delete']"
                    :disabled="selection.length === 0"
                    icon="el-icon-delete"
                    plain
                    type="danger"
                    @click="batch_remove"
                    >删除</el-button
                >
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        v-model="searchParams.keyword"
                        clearable
                        placeholder="设备名称 / 设备标识"
                        @keyup.enter="handleSearch"
                        @clear="handleSearch"
                    ></el-input>
                    <el-button
                        icon="el-icon-search"
                        type="primary"
                        color="#1C409A"
                        @click="handleSearch"
                    ></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="p0">
            <device-selector
                ref="deviceSelector"
                :search-params="searchParams"
                :showSearchBar="false"
                :row-click-select="false"
                @selection-change="handleSelectionChange"
            >
                <template #additional-columns>
                    <el-table-column label="操作" width="150" align="center" fixed="right">
                        <template #default="scope">
                            <el-button
                                v-auths="['tenant.device.edit']"
                                size="small"
                                type="primary"
                                @click="edit(scope.row)"
                                color="#1C409A"
                                >编辑</el-button
                            >
                            <el-button
                                v-auths="['tenant.device.view']"
                                size="small"
                                type="info"
                                @click="view(scope.row)"
                                color="#32aa66"
                                >查看</el-button
                            >
                            <el-popconfirm title="确定删除该设备吗？" @confirm="remove(scope.row)">
                                <template #reference>
                                    <el-button
                                        v-auths="['tenant.device.delete']"
                                        size="small"
                                        type="danger"
                                        >删除</el-button
                                    >
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </template>
            </device-selector>
        </el-main>
    </el-container>

    <save-dialog
        v-if="dialog.save"
        ref="saveDialog"
        @saveDialogClosedEmit="saveDialogClosedHandle"
        @saveSuccessEmit="saveSuccessHandle"
    ></save-dialog>
</template>

<script>
import saveDialog from './save';
import DeviceSelector from '../common/DeviceSelector.vue';
import tableConfig from '@/config/table.js';

export default {
    name: 'tenant.device.list',
    components: {
        saveDialog,
        DeviceSelector
    },
    data() {
        return {
            dialog: {
                save: false
            },
            searchParams: {
                keyword: ''
            },
            selection: []
        };
    },
    methods: {
        /**
         * 处理搜索
         */
        handleSearch() {
            this.$refs.deviceSelector.refresh();
        },

        /**
         * 处理选择变更
         * @param {Array} selection 选中的设备ID数组
         */
        handleSelectionChange(selection) {
            this.selection = selection;
        },

        /**
         * 添加设备
         */
        add() {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show();
            });
        },

        /**
         * 设备编辑
         * @param {Object} row 行数据
         */
        edit(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('edit').setData(row);
            });
        },

        /**
         * 设备查看
         * @param {Object} row 行数据
         */
        view(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('show').setData(row);
            });
        },

        /**
         * 设备删除
         * @param {Object} row 行数据
         */
        async remove(row) {
            const reqData = { id: row.id };
            const res = await this.$API.tenant.device.delete.post(reqData);
            if (res.code === tableConfig.successCode) {
                this.$refs.deviceSelector.refresh();
                this.$message.success(res.message);
            } else {
                await this.$alert(res.message, '提示', { type: 'error' });
            }
        },

        /**
         * 批量删除
         */
        async batch_remove() {
            this.$confirm(`确定删除选中的 ${this.selection.length} 个设备吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    if (this.selection.length <= 0) {
                        this.$message.error("请选择要删除的设备");
                        return;
                    }

                    try {
                        const loading = this.$loading();
                        const res = await this.$API.tenant.device.batchDelete.post({
                            ids: this.selection
                        });

                        if (res.code === tableConfig.successCode) {
                            this.$message.success('批量删除成功');
                            this.$refs.deviceSelector.refresh();
                            this.$refs.deviceSelector.clearSelection();
                        } else {
                            this.$message.error(res.message || '批量删除失败');
                        }
                        loading.close();
                    } catch (error) {
                        this.$message.error('操作失败: ' + (error.message || '未知错误'));
                    }
                })
                .catch(() => {});
        },

        /**
         * 保存对话框关闭事件
         */
        saveDialogClosedHandle() {
            this.dialog.save = false;
        },

        /**
         * 保存成功事件
         */
        saveSuccessHandle() {
            this.$refs.deviceSelector.refresh();
        }
    }
};
</script>

<style lang="scss" scoped>
.el-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.left-panel {
    display: flex;
    gap: 10px;
}

.right-panel-search {
    display: flex;
    align-items: center;
}

.p0 {
    padding: 0;
}
</style>
