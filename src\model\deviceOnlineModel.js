/**
 * 设备在线状态数据库模型
 * 负责设备在线状态的持久化存储和查询
 */
import { db } from '@/utils/indexedDB';
import storage from '@/config/storage';

// 存储对象名称
const STORE_NAME = storage.indexedDB.storeConfigs.onlineDevice.name;

// 设备心跳超时时间（毫秒）
const HEARTBEAT_TIMEOUT = 30000; // 30秒

/**
 * 设备在线状态数据库模型
 */
class DeviceOnlineModel {
    // 初始化 Promise
    initPromise = null;

    /**
     * 构造函数
     */
    constructor() {
        console.log('初始化设备在线状态数据库模型');
        // 初始化 Promise
        this.initPromise = Promise.resolve();
    }

    /**
     * 获取设备在线状态
     * @param {number} deviceId 设备ID
     * @returns {Promise<Object|null>} 设备状态对象 {deviceId, online, lastHeartbeat, lastUpdate}
     */
    async getDeviceOnlineStatus(deviceId) {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 获取设备状态
            const status = await db.get(STORE_NAME, deviceId);

            if (!status) {
                return null;
            }

            // 不再检查心跳超时，直接使用存储的在线状态
            // 设备状态完全依赖MQTT消息中的status字段
            const online = status.online;

            // 返回状态对象
            return {
                ...status,
                online
            };
        } catch (error) {
            console.error(`获取设备 ${deviceId} 在线状态失败:`, error);
            return null;
        }
    }

    /**
     * 批量获取设备在线状态
     * @param {Array<number>} deviceIds 设备ID数组
     * @returns {Promise<Object>} 设备状态映射 {deviceId: {online, lastHeartbeat, lastUpdate}}
     */
    async getDevicesOnlineStatus(deviceIds) {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 获取所有设备状态
            const result = {};
            const now = Date.now();

            // 并行获取所有设备状态
            const statusPromises = deviceIds.map(async (deviceId) => {
                const status = await db.get(STORE_NAME, deviceId);

                if (status) {
                    // 不再检查心跳超时，直接使用存储的在线状态
                    // 设备状态完全依赖MQTT消息中的status字段
                    const online = status.online;

                    result[deviceId] = {
                        ...status,
                        online
                    };
                } else {
                    // 设备没有状态记录，默认为离线
                    result[deviceId] = {
                        deviceId,
                        online: false,
                        lastHeartbeat: null,
                        lastUpdate: null
                    };
                }
            });

            // 等待所有状态获取完成
            await Promise.all(statusPromises);

            return result;
        } catch (error) {
            console.error('批量获取设备在线状态失败:', error);
            return {};
        }
    }

    /**
     * 更新设备在线状态
     * @param {number} deviceId 设备ID
     * @param {boolean} online 在线状态
     * @param {number} heartbeatTime 心跳时间戳（可选，默认为当前时间）
     * @returns {Promise<boolean>} 是否成功
     */
    async updateDeviceOnlineStatus(deviceId, online, heartbeatTime = null) {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            const now = Date.now();
            const currentStatus = await db.get(STORE_NAME, deviceId) || {};

            // 构建新的状态对象
            const newStatus = {
                deviceId,
                online,
                lastUpdate: now
            };

            // 如果在线，更新心跳时间
            if (online) {
                newStatus.lastHeartbeat = heartbeatTime || now;
            } else {
                // 如果离线，保留上次心跳时间
                newStatus.lastHeartbeat = currentStatus.lastHeartbeat || null;
            }

            // 保存到数据库
            await db.put(STORE_NAME, newStatus);
            return true;
        } catch (error) {
            console.error(`更新设备 ${deviceId} 在线状态失败:`, error);
            return false;
        }
    }

    /**
     * 清理过期的设备状态
     * @param {number} expirationTime 过期时间（毫秒），默认为7天
     * @returns {Promise<number>} 清理的设备数量
     */
    async cleanupExpiredStatus(expirationTime = 7 * 24 * 60 * 60 * 1000) {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 获取所有设备状态
            const allDevices = await db.getAll(STORE_NAME);
            const now = Date.now();
            let cleanedCount = 0;

            // 并行清理过期设备
            const cleanupPromises = allDevices
                .filter(device => now - device.lastUpdate > expirationTime)
                .map(async (device) => {
                    await db.delete(STORE_NAME, device.deviceId);
                    cleanedCount++;
                });

            // 等待所有清理操作完成
            await Promise.all(cleanupPromises);

            console.log(`清理了 ${cleanedCount} 个过期设备状态`);
            return cleanedCount;
        } catch (error) {
            console.error('清理过期设备状态失败:', error);
            return 0;
        }
    }
}

// 创建数据库实例
const deviceOnlineModel = new DeviceOnlineModel();

// 导出数据库实例
export default deviceOnlineModel;
