// 默认样式重置
// <AUTHOR> <<PERSON>yu<PERSON>@live.com>
// @Datetime 2024-12-13

@use 'mixins' as *;

*,
*::before,
*::after {
    margin: 0;
    outline: none;
    box-sizing: border-box;
}

body {
    font-size: 14px;
    font-variant: tabular-nums;
    -webkit-text-size-adjust: none !important;
    -webkit-font-smoothing: antialiased;
    -webkit-font-feature-settings: 'tnum';
    font-feature-settings: 'tnum';
    font-family:
        Lantinghei SC,
        Open Sans,
        'Hiragino Sans GB',
        'Microsoft YaHei',
        '微软雅黑',
        STXihei,
        '华文细黑',
        'Droid Sans',
        'WenQuanYi Micro Hei',
        'PingFang SC',
        'Helvetica Neue',
        'Helvetica',
        Tahoma,
        Arial,
        SimSun,
        '宋体',
        Heiti,
        '黑体',
        sans-serif !important;
    text-rendering: optimizeLegibility;
    background-color: var(--color-background);
    color: var(--color-text);
}

#app,
body,
html {
    width: 100%;
    height: 100%;
    min-height: 100vh;
    overflow: hidden;
}

a {
    color: var(--color-text);
    text-decoration: none;

    &:hover,
    &:focus {
        color: #000000;
        text-decoration: none;
    }

    &:link {
        text-decoration: none;
    }

    &:-webkit-any-link {
        text-decoration: none;
    }
}

/* 取消自动填充 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
    -webkit-text-fill-color: #555 !important; /* 设置文本颜色 */
}

/* 全局滚动条样式 */
.scrollable {
    -webkit-overflow-scrolling: touch;
}

// 使用mixin替换原有滚动条样式
@include scrollbar(12px);
