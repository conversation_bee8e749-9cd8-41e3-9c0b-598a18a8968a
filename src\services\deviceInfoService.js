/**
 * 设备信息服务
 * 统一管理设备信息的获取和处理
 */
import logger from '@/utils/logger';
import { ElMessage } from 'element-plus';
import API from '@/api';

// 创建设备信息服务专用日志记录器
const log = logger.createLogger('DeviceInfoService');

/**
 * 设备信息服务
 */
const deviceInfoService = {
    /**
     * 获取设备详细信息
     * @param {string|number} deviceId 设备ID
     * @param {Object} options 可选参数
     * @param {number} options.layer 层级（可选，用于分层查询）
     * @param {boolean} options.silent 是否静默模式（不显示错误提示）
     * @returns {Promise<Object|null>} 设备信息对象，失败时返回null
     */
    async getDeviceInfo(deviceId, options = {}) {
        const { layer, silent = false } = options;

        try {
            log.group(`获取设备 ${deviceId} 信息`);
            log.info(`设备ID: ${deviceId}${layer ? `, 层级: ${layer}` : ''}`);

            // 构建请求参数
            const params = { id: deviceId };
            if (layer !== undefined) {
                params.layer = layer;
            }

            // 调用API获取设备信息
            const response = await API.tenant.device.info.get(params);

            // 检查返回状态
            if (response && response.status === 1 && response.data) {
                log.info('设备信息获取成功:', response.data);
                log.groupEnd();
                return response.data;
            } else {
                const errorMsg = response?.message || '获取设备信息失败';
                log.error('API返回错误:', errorMsg);

                if (!silent) {
                    ElMessage.error(errorMsg);
                }

                log.groupEnd();
                return null;
            }
        } catch (error) {
            log.error('获取设备信息异常:', error);

            if (!silent) {
                ElMessage.error('无法连接到服务器，请检查网络连接');
            }

            log.groupEnd();
            return null;
        }
    },

    /**
     * 获取设备在线状态
     * @param {string|number} deviceId 设备ID
     * @param {boolean} silent 是否静默模式（不显示错误提示）
     * @returns {Promise<boolean|null>} 设备在线状态，失败时返回null
     */
    async getDeviceOnlineStatus(deviceId, silent = true) {
        try {
            log.group(`获取设备 ${deviceId} 在线状态`);

            const deviceInfo = await this.getDeviceInfo(deviceId, { silent: true });

            if (deviceInfo && typeof deviceInfo.online === 'boolean') {
                const isOnline = deviceInfo.online;
                log.info(`设备在线状态: ${isOnline ? '在线' : '离线'}`);
                log.groupEnd();
                return isOnline;
            } else {
                log.warn('设备信息中缺少在线状态字段');

                if (!silent) {
                    ElMessage.warning('无法获取设备在线状态');
                }

                log.groupEnd();
                return null;
            }
        } catch (error) {
            log.error('获取设备在线状态异常:', error);

            if (!silent) {
                ElMessage.error('获取设备状态失败');
            }

            log.groupEnd();
            return null;
        }
    },

    /**
     * 刷新设备状态并更新本地缓存
     * @param {string|number} deviceId 设备ID
     * @param {Object} deviceStore Pinia设备状态store实例
     * @param {boolean} silent 是否静默模式（不显示提示）
     * @returns {Promise<boolean|null>} 设备在线状态，失败时返回null
     */
    async refreshDeviceStatus(deviceId, deviceStore, silent = false) {
        try {
            log.group(`刷新设备 ${deviceId} 状态`);

            const deviceInfo = await this.getDeviceInfo(deviceId, { silent: true });

            if (deviceInfo && typeof deviceInfo.online === 'boolean') {
                const isOnline = deviceInfo.online;

                // 更新本地状态
                if (deviceStore && deviceStore.updateDeviceStatus) {
                    deviceStore.updateDeviceStatus(deviceId, isOnline);
                    log.info(`已更新本地状态: ${isOnline ? '在线' : '离线'}`);
                }

                if (!silent) {
                    if (isOnline) {
                        ElMessage.success('设备当前在线');
                    } else {
                        ElMessage.warning('设备仍然离线');
                    }
                }

                log.groupEnd();
                return isOnline;
            } else {
                log.warn('无法获取有效的设备状态');

                if (!silent) {
                    ElMessage.error('获取设备状态失败');
                }

                log.groupEnd();
                return null;
            }
        } catch (error) {
            log.error('刷新设备状态异常:', error);

            if (!silent) {
                ElMessage.error('刷新设备状态失败');
            }

            log.groupEnd();
            return null;
        }
    }
};

export default deviceInfoService;
