<template>
    <div class="panel-item mqtt-status" @click="handleMqttStatus">
        <el-tooltip effect="dark" :content="mqttStatus ? 'MQTT已连接' : (mqttRetrying ? 'MQTT重试连接中...' : 'MQTT未连接')" placement="bottom">
            <div class="status-icon">
                <el-icon :class="['mqtt-icon', mqttStatus ? 'connected' : (mqttRetrying ? 'retrying' : 'disconnected')]">
                    <sc-icon-mqtt />
                </el-icon>
            </div>
        </el-tooltip>
        <span>MQHub</span>
    </div>
</template>

<script>
import mqttService from '@/services/mqttService';
import account from '@/utils/account';

export default {
    name: 'mqttStatus',
    data() {
        return {
            mqttRetrying: false,
            mqttStatus: false
        };
    },
    mounted() {
        mqttService.addStateListener(this.updateStatus);
    },
    beforeUnmount() {
        mqttService.removeStateListener(this.updateStatus);
    },
    methods: {
        updateStatus(status, retrying) {
            this.mqttStatus = status;
            this.mqttRetrying = retrying;
        },
        handleMqttStatus() {
            // 打开mqtt-hub
            this.$router.push({ path: '/mqtt-hub' });

            if (!this.mqttStatus) {
                try {
                    account.connectMqtt();
                } catch (error) {
                    // 连接失败的处理已经在mqttService中完成
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.mqtt-status {
    .status-icon {
        margin-right: 5px;
        .mqtt-icon {
            font-size: 14px;
            position: relative;
            top: 2px;
            &.connected {
                color: var(--el-color-success);
                animation: breath 1.5s ease-in-out infinite;
            }
            &.disconnected:not(.retrying) {
                color: var(--el-color-danger);
                //animation: breath 1.5s ease-in-out infinite;
            }
            &.retrying {
                color: var(--el-color-warning);
                animation: blink 1.5s ease-in-out infinite;
            }
        }
    }

    &:has(.mqtt-icon.disconnected:not(.retrying)) {
        //color: var(--el-color-danger);
    }
    &:has(.mqtt-icon.connected) {
        color: var(--el-color-success);
    }
    &:has(.mqtt-icon.retrying) {
        animation: blink 1.5s ease-in-out infinite;
    }
}

@keyframes breath {
    0% {
        opacity: 0.75;
        transform: scale(0.9);
    }
    50% {
        opacity: 1;
        transform: scale(1.0);
    }
    100% {
        opacity: 0.75;
        transform: scale(0.9);
    }
}

@keyframes blink {
    0% {
        opacity: 0.4;
        color: var(--el-color-warning);
    }
    50% {
        opacity: 1;
        color: var(--el-color-danger);
    }
    100% {
        opacity: 0.4;
        color: var(--el-color-warning);
    }
}
</style>
