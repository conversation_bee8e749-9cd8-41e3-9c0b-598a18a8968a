<template>
    <!-- 最外层的点击事件改为只在图标区域有效 -->
    <div class="panel-item notification">
        <!-- 只在这里添加点击事件 -->
        <el-badge
            :hidden="unreadCount === 0"
            :value="unreadCount"
            class="badge notification-trigger"
            type="danger"
            v-debounce="{ fn: showNotify, delay: 0 }"
        >
            <el-icon class="el-icon--left" style="margin-right: 3px; top: 2px"><sc-icon-bell /></el-icon>
            <span>消息通知</span>
        </el-badge>
        <el-drawer title="消息通知" v-model="notifyVisible" :size="400" append-to-body destroy-on-close>
            <el-container>
                <el-main class="p0">
                    <el-scrollbar ref="scrollbarRef" height="calc(100vh - 130px)" @scroll="handleScroll">
                        <ul class="msg-list">
                            <li v-for="item in messageList" :key="item.id">
                                <a @click.prevent="viewMessage(item)" class="message-item">
                                    <div class="msg-list__icon">
                                        <el-badge :is-dot="!item.is_read" type="danger">
                                            <el-tag :type="getTypeTag(item.type)" size="small">
                                                {{ getTypeName(item.type) }}
                                            </el-tag>
                                        </el-badge>
                                    </div>
                                    <div class="msg-list__main">
                                        <h2 class="msg-title">{{ item.title }}</h2>
                                        <p class="msg-content">{{ item.content }}</p>
                                        <div class="msg-time">{{ item.create_at }}</div>
                                    </div>
                                </a>
                            </li>
                            <el-empty
                                v-if="!loading && messageList.length === 0"
                                description="暂无新消息"
                                :image-size="100"
                            />
                        </ul>
                        <div v-if="loading" class="loading-more">
                            <el-icon class="is-loading"><el-icon-loading /></el-icon>
                            <span>正在加载，请稍候...</span>
                        </div>
                        <!-- 添加底部提示 -->
                        <div v-if="!hasMore && messageList.length > 0" class="no-more"> -- 我也是有底线的 -- </div>
                    </el-scrollbar>
                </el-main>
                <el-footer>
                    <el-button
                        color="#cc8cb7"
                        style="
                            --el-button-text-color: var(--el-color-white);
                            --el-button-hover-text-color: var(--el-color-white);
                        "
                        icon="el-icon-chat-dot-square"
                        @click="toList"
                        >消息中心</el-button
                    >
                    <el-button
                        color="#1C409A"
                        @click="markAllRead"
                        icon="el-icon-circle-check"
                        :disabled="messageList.length === 0"
                        >全部设为已读</el-button
                    >
                </el-footer>
            </el-container>
        </el-drawer>

        <el-dialog
            v-model="detailVisible"
            title="消息详情"
            width="800px"
            :close-on-click-modal="false"
            destroy-on-close
            draggable
            align-center
            :modal-class="'message-detail-dialog'"
        >
            <receive-detail v-if="detailVisible" :message="currentMessage" :update-list-data="handleDataUpdate" />
        </el-dialog>
    </div>
</template>

<script>
import { getTypeTag, getTypeName } from '@/constants/notification';
import { useNotificationStore } from '@/store/notification';
import { storeToRefs } from 'pinia';
import ReceiveDetail from '@/views/notification/components/receive-detail.vue';
import NotificationUtils from '@/utils/notification';
import { throttle } from '@/utils/throttleDebounce';

export default {
    name: 'Notification',
    components: {
        ReceiveDetail
    },
    setup() {
        const notificationStore = useNotificationStore();
        const { unreadCount } = storeToRefs(notificationStore);
        return { unreadCount };
    },
    data() {
        return {
            notifyVisible: false,
            messageList: [],
            loading: false,
            page: 1,
            pageSize: 10, // 每页10条
            total: 0,
            hasMore: true,
            updateTimer: null,
            detailVisible: false,
            currentMessage: null
        };
    },
    watch: {
        notifyVisible(val) {
            if (val) {
                this.page = 1;
                this.messageList = [];
                this.hasMore = true;
                this.loadMessages();
            }
        }
    },
    computed: {},
    mounted() {
        // 组件加载时获取未读数
        useNotificationStore().getUnreadCount();
        // 初始化时开始自动更新未读数
        this.startAutoUpdate();
    },
    beforeDestroy() {
        // 组件销毁时清除定时器
        this.stopAutoUpdate();
    },
    methods: {
        getTypeTag,
        getTypeName,

        async loadMessages() {
            if (this.loading || !this.hasMore) return;

            this.loading = true;
            const result = await NotificationUtils.loadMessageList({
                page: this.page,
                pageSize: this.pageSize,
                readStatus: 0
            });

            if (result.success) {
                this.total = result.total;
                this.messageList.push(...result.data);
                this.page++;
                this.hasMore = result.data.length === this.pageSize && this.messageList.length < this.total;
            }

            this.loading = false;
        },

        handleScroll(e) {
            // 获取滚动容器
            const scrollbarWrap = this.$refs.scrollbarRef.$refs.wrapRef;
            if (!scrollbarWrap) return;

            const { scrollHeight, scrollTop, clientHeight } = scrollbarWrap;
            const scrollDistance = scrollHeight - scrollTop - clientHeight;

            // 距离底部50px时触发加载
            if (scrollDistance < 50 && !this.loading && this.hasMore) {
                this.loadMessages();
            }
        },

        async viewMessage(message) {
            // 先关闭消息列表抽屉
            this.notifyVisible = false;

            // 等待抽屉关闭动画完成后再显示详情对话框
            setTimeout(() => {
                // 保存当前消息数据
                this.currentMessage = { ...message };
                this.detailVisible = true;
            }, 300);
        },

        showNotify() {
            this.notifyVisible = true;
            this.resetList(); // 打开时重置列表状态
        },

        // 打开消息中心
        toList() {
            this.notifyVisible = false; // 先关闭抽屉
            this.$nextTick(() => {
                this.$router.push({ path: '/notification/index' }); // 再跳转页面
            });
        },

        async markAllRead() {
            this.messageList = [];

            // 请求接口标记全部已读
            const { success } = await NotificationUtils.markAllRead();
            if (success) {
                // 更新未读数
                await useNotificationStore().getUnreadCount();
                // 重新加载消息列表
                this.resetList();
                await this.loadMessages();
            }
        },

        // 使用节流进行自动更新
        startAutoUpdate() {
            const notificationStore = useNotificationStore();
            const throttledUpdate = throttle(() => notificationStore.getUnreadCount(), 30000, {
                leading: true,
                trailing: true
            });

            throttledUpdate(); // 立即执行一次
            this.updateTimer = setInterval(throttledUpdate, 30000);
        },

        // 停止自动更新
        stopAutoUpdate() {
            if (this.updateTimer) {
                clearInterval(this.updateTimer);
                this.updateTimer = null;
            }
        },

        // 重置列表状态
        resetList() {
            this.page = 1;
            this.messageList = [];
            this.hasMore = true;
            this.total = 0;
            this.loading = false; // 确保重置加载状态
        },

        // 添加统一的数据更新方法
        async handleDataUpdate() {
            // 更新未读数
            const notificationStore = useNotificationStore();
            await notificationStore.getUnreadCount();

            // 重新加载消息列表
            this.resetList();
            await this.loadMessages();
        }
    }
};
</script>

<style lang="scss" scoped>
.msg-list {
    padding: 0;
    border-top: 1px dotted var(--el-border-color-light);
    li {
        list-style: none;
        border-bottom: 1px solid var(--el-border-color-lighter);

        &:last-child {
            border-bottom: none;
        }

        .message-item {
            display: flex;
            padding: 12px 16px;
            text-decoration: none;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: var(--el-fill-color-light);
            }
        }
    }
    .msg-list__icon {
        flex-shrink: 0;
        margin-right: 12px;
        padding-top: 2px;

        .el-tag {
            font-size: 12px;
            height: 22px;
            line-height: 20px;
        }
    }

    .msg-list__main {
        flex: 1;
        min-width: 0; // 防止文本溢出

        .msg-title {
            margin: 0 0 6px;
            font-size: 14px;
            font-weight: 500;
            color: var(--el-text-color-primary);
            line-height: 1.4;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .msg-content {
            margin: 0 0 6px;
            font-size: 13px;
            color: var(--el-text-color-secondary);
            line-height: 1.5;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            max-height: 40px;
        }

        .msg-time {
            opacity: 0.5;
            font-size: 12px;
            color: var(--el-text-color-secondary);
            line-height: 1;
        }
    }
}

.load-more {
    text-align: center;
    padding: 10px 0 10px 0;
    .el-button {
        font-size: 14px;
    }
}

.loading-more {
    display: flex;
    justify-content: center;
    align-items: center; /* 添加垂直居中对齐 */
    min-height: 200px; /* 设置最小高度确保有足够空间居中 */
    text-align: center;
    padding: 10px 0;
    color: var(--el-text-color-secondary);
    .el-icon {
        margin-right: 5px;
    }
}

.no-more {
    text-align: center;
    padding: 10px 0 15px 0;
    border-top: 1px solid var(--el-border-color-lighter);
    color: var(--el-text-color-secondary);
    font-size: 14px;
}

// 调整对话框内容样式
:deep(.el-dialog) {
    .el-dialog__body {
        padding: 0;
    }
}

// 确保对话框内容可以点击
:deep(.message-detail-dialog) {
    .el-dialog__body {
        padding: 0;
        pointer-events: auto; // 确保内容可点击
    }
}

// 移除之前的 .notify-trigger 样式
// 更精确地控制可点击区域
.notification {
    &-trigger {
        cursor: pointer;
    }
}

// 确保对话框区域使用默认光标
:deep(.el-overlay) {
    cursor: default;
}

:deep(.el-dialog) {
    cursor: default;

    .el-dialog__body {
        padding: 0;
    }
}

.msg-list__icon {
    width: 66px;
    text-align: left;
    display: inline-block;
}
</style>
