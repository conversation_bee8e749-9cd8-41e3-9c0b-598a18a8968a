<template>
    <div class="message-detail">
        <!-- 消息头部 -->
        <div class="message-header">
            <h2 class="message-title">{{ message.title }}</h2>
            <div class="message-meta">
                <div class="meta-items">
                    <el-tag size="small" :type="getTypeTag(message.type)" class="meta-tag">
                        {{ getTypeName(message.type) }}
                    </el-tag>
                    <el-tag size="small" :type="getRangeType(message.receive_range)" class="meta-tag">
                        {{ getRangeName(message.receive_range) }}
                    </el-tag>
                    <span class="meta-time">{{ message.create_at }}</span>
                </div>
                <div class="meta-stats">
                    <div class="stat-item">
                        <el-icon><el-icon-view /></el-icon>
                        <span>已读: {{ message.counter?.read_count || 0 }}</span>
                    </div>
                    <div class="stat-item">
                        <el-icon><el-icon-pointer /></el-icon>
                        <span>点赞: {{ message.counter?.like_count || 0 }}</span>
                    </div>
                </div>
            </div>
        </div>

        <el-divider />

        <!-- 消息内容 -->
        <div class="message-content" v-html="message.content"></div>

        <el-divider />

        <!-- 接收者信息 -->
        <div class="message-footer">
            <div class="target-info">
                <h3 class="target-title">接收对象</h3>
                <div class="target-content">
                    <template v-if="message.receive_range_text">
                        {{ message.receive_range_text }}
                    </template>
                    <template v-else>
                        {{ getRangeName(message.receive_range) }}
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getTypeTag, getTypeName, getRangeTag, getRangeName } from '@/constants/notification';

export default {
    name: 'SentMessageDetail',
    props: {
        message: {
            type: Object,
            required: true
        }
    },
    methods: {
        getTypeTag,
        getTypeName,
        getRangeType: getRangeTag,
        getRangeName,
        formatTargetIds(ids) {
            try {
                if (!ids || ids === '[]') return '';
                const parsed = JSON.parse(ids);
                return Array.isArray(parsed) ? parsed.flat().join(', ') : ids;
            } catch {
                return ids;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.message-detail {
    padding: 24px;

    .message-header {
        margin-bottom: 24px;

        .message-title {
            margin: 0 0 16px;
            font-size: 22px;
            font-weight: bold;
            color: var(--el-text-color-primary);
            line-height: 1.4;
        }

        .message-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;

            .meta-items {
                display: flex;
                align-items: center;
                gap: 12px;

                .meta-tag {
                    font-weight: normal;
                }

                .meta-time {
                    color: var(--el-text-color-secondary);
                    font-size: 14px;
                }
            }

            .meta-stats {
                display: flex;
                align-items: center;
                gap: 16px;

                .stat-item {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    color: var(--el-text-color-secondary);
                    font-size: 14px;

                    .el-icon {
                        font-size: 16px;
                    }
                }
            }
        }
    }

    .message-content {
        padding: 24px 0;
        line-height: 1.8;
        font-size: 15px;
        color: var(--el-text-color-primary);

        :deep(img) {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }

        :deep(p) {
            margin: 1em 0;
        }

        :deep(a) {
            color: var(--el-color-primary);
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    .message-footer {
        .target-info {
            background-color: var(--el-fill-color-lighter);
            border-radius: 8px;
            padding: 16px;

            .target-title {
                margin: 0 0 12px;
                font-size: 16px;
                font-weight: bold;
                color: var(--el-text-color-primary);
            }

            .target-content {
                color: var(--el-text-color-regular);
                font-size: 14px;
                line-height: 1.6;
            }
        }
    }
}
</style>
