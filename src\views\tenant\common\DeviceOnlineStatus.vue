<template>
    <div class="device-online-status">
        <el-tooltip :content="tooltipContent" placement="top">
            <el-tag
                :type="getStatusType"
                :effect="effect"
                size="small"
                class="status-tag"
            >
                {{ getStatusText }}
                <i v-if="!mqttConnected" class="el-icon-warning-outline" style="margin-left: 2px;"></i>
            </el-tag>
        </el-tooltip>
    </div>
</template>

<script>
import deviceOnlineService from '@/services/deviceOnlineService';
import { formatTimestamp } from '@/utils/date';
import loggerFactory from '@/utils/logger';
import mqttConnectionMixin from '@/mixins/mqttConnectionMixin';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('DeviceStatus');

export default {
    name: 'DeviceOnlineStatus',
    mixins: [mqttConnectionMixin],
    props: {
        deviceId: {
            type: [Number, String],
            required: true
        },
        effect: {
            type: String,
            default: 'light',
            validator: value => ['light', 'dark', 'plain'].includes(value)
        },
        // 后端返回的初始在线状态
        initialOnline: {
            type: Boolean,
            default: null
        }
    },
    data() {
        return {
            online: false,
            lastHeartbeat: null,
            lastUpdate: null,
            statusListener: null // 存储状态监听器引用
        };
    },
    computed: {
        tooltipContent() {
            if (!this.mqttConnected) {
                return '网络已断开，设备状态可能不准确';
            }

            if (!this.lastHeartbeat) {
                return '设备未连接';
            }

            const lastHeartbeatTime = formatTimestamp(this.lastHeartbeat);
            const timeSinceLastHeartbeat = Math.floor((Date.now() - this.lastHeartbeat) / 1000);

            let timeText = '';
            if (timeSinceLastHeartbeat < 60) {
                timeText = `${timeSinceLastHeartbeat}秒前`;
            } else if (timeSinceLastHeartbeat < 3600) {
                timeText = `${Math.floor(timeSinceLastHeartbeat / 60)}分钟前`;
            } else if (timeSinceLastHeartbeat < 86400) {
                timeText = `${Math.floor(timeSinceLastHeartbeat / 3600)}小时前`;
            } else {
                timeText = `${Math.floor(timeSinceLastHeartbeat / 86400)}天前`;
            }

            return `最后心跳: ${lastHeartbeatTime} (${timeText})`;
        },

        // 获取状态类型
        getStatusType() {
            if (!this.mqttConnected) {
                return 'warning'; // 断网时使用警告色
            }
            return this.online ? 'success' : 'info';
        },

        // 获取状态文本
        getStatusText() {
            if (!this.mqttConnected) {
                return this.online ? '可能在线' : '可能离线';
            }
            return this.online ? '在线' : '离线';
        }
    },
    methods: {
        // 状态监听器
        handleStatusChange(newStatus) {
            const oldOnline = this.online;
            this.online = newStatus.online;
            this.lastHeartbeat = newStatus.lastHeartbeat;
            this.lastUpdate = newStatus.lastUpdate;

            // 如果在线状态发生变化，通知父组件
            if (oldOnline !== this.online) {
                logger.debug(`设备 ${this.deviceId} 状态变化: ${this.online ? '在线' : '离线'}`);

                this.$emit('online-status-change', {
                    deviceId: this.deviceId,
                    online: this.online,
                    lastHeartbeat: this.lastHeartbeat
                });
            }
        }
    },
    mounted() {
        const deviceIdStr = String(this.deviceId);

        // 如果有后端返回的初始在线状态，初始化到缓存中
        if (this.initialOnline !== null) {
            this.online = this.initialOnline;
            deviceOnlineService.initDeviceStatus(deviceIdStr, this.initialOnline);
            logger.debug(`设备 ${this.deviceId} 使用后端返回的初始在线状态: ${this.online ? '在线' : '离线'}`);
        }

        // 添加状态监听器
        this.statusListener = this.handleStatusChange;
        deviceOnlineService.addStatusListener(deviceIdStr, this.statusListener, this);
    },
    beforeDestroy() {
        // 移除状态监听器
        if (this.statusListener) {
            deviceOnlineService.removeStatusListener(String(this.deviceId), this.statusListener, this);
            this.statusListener = null;
        }
    }
};
</script>

<style scoped>
.device-online-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.status-tag {
    min-width: 42px;
    text-align: center;
}
</style>
