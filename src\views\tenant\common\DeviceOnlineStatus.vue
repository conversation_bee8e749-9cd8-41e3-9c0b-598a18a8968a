<template>
    <div class="device-online-status">
        <el-tooltip :content="tooltipContent" placement="top">
            <el-tag
                :type="getStatusType"
                :effect="effect"
                size="small"
                class="status-tag"
            >
                {{ getStatusText }}
                <i v-if="!mqttConnected" class="el-icon-warning-outline" style="margin-left: 2px;"></i>
            </el-tag>
        </el-tooltip>
    </div>
</template>

<script>
import { formatTimestamp } from '@/utils/date';
import loggerFactory from '@/utils/logger';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('DeviceStatus');

export default {
    name: 'DeviceOnlineStatus',
    props: {
        deviceId: {
            type: [Number, String],
            required: true
        },
        effect: {
            type: String,
            default: 'light',
            validator: value => ['light', 'dark', 'plain'].includes(value)
        },
        // 后端返回的初始在线状态
        initialOnline: {
            type: Boolean,
            default: null
        }
    },
    setup() {
        // 获取 Pinia store
        const deviceStore = useDeviceOnlineStore();

        return {
            deviceStore
        };
    },
    data() {
        return {
            // 保留一些本地状态用于事件发射
            lastEmittedOnlineStatus: null
        };
    },
    computed: {
        // 从 Pinia store 获取设备状态
        deviceStatus() {
            return this.deviceStore.getDeviceStatus(String(this.deviceId));
        },

        // 设备是否在线
        online() {
            return this.deviceStatus.online;
        },

        // 最后心跳时间
        lastHeartbeat() {
            return this.deviceStatus.lastHeartbeat;
        },

        // 最后更新时间
        lastUpdate() {
            return this.deviceStatus.lastUpdate;
        },

        // MQTT连接状态
        mqttConnected() {
            return this.deviceStore.mqttConnected;
        },

        tooltipContent() {
            if (!this.mqttConnected) {
                return '网络已断开，设备状态可能不准确';
            }

            if (!this.lastHeartbeat) {
                return '设备未连接';
            }

            const lastHeartbeatTime = formatTimestamp(this.lastHeartbeat);
            const timeSinceLastHeartbeat = Math.floor((Date.now() - this.lastHeartbeat) / 1000);

            let timeText = '';
            if (timeSinceLastHeartbeat < 60) {
                timeText = `${timeSinceLastHeartbeat}秒前`;
            } else if (timeSinceLastHeartbeat < 3600) {
                timeText = `${Math.floor(timeSinceLastHeartbeat / 60)}分钟前`;
            } else if (timeSinceLastHeartbeat < 86400) {
                timeText = `${Math.floor(timeSinceLastHeartbeat / 3600)}小时前`;
            } else {
                timeText = `${Math.floor(timeSinceLastHeartbeat / 86400)}天前`;
            }

            return `最后心跳: ${lastHeartbeatTime} (${timeText})`;
        },

        // 获取状态类型
        getStatusType() {
            if (!this.mqttConnected) {
                return 'warning'; // 断网时使用警告色
            }
            return this.online ? 'success' : 'info';
        },

        // 获取状态文本
        getStatusText() {
            if (!this.mqttConnected) {
                return this.online ? '可能在线' : '可能离线';
            }
            return this.online ? '在线' : '离线';
        }
    },
    watch: {
        // 监听在线状态变化，发射事件给父组件
        online(newOnline, oldOnline) {
            if (newOnline !== oldOnline && this.lastEmittedOnlineStatus !== newOnline) {
                this.lastEmittedOnlineStatus = newOnline;
                logger.debug(`设备 ${this.deviceId} 状态变化: ${newOnline ? '在线' : '离线'}`);

                this.$emit('online-status-change', {
                    deviceId: this.deviceId,
                    online: newOnline,
                    lastHeartbeat: this.lastHeartbeat
                });
            }
        }
    },
    mounted() {
        const deviceIdStr = String(this.deviceId);

        // 如果有后端返回的初始在线状态，初始化到 Pinia store 中
        if (this.initialOnline !== null) {
            this.deviceStore.initDeviceStatus(deviceIdStr, this.initialOnline);
            logger.debug(`设备 ${this.deviceId} 使用后端返回的初始在线状态: ${this.initialOnline ? '在线' : '离线'}`);
        }

        // 设置初始发射状态
        this.lastEmittedOnlineStatus = this.online;
    }
};
</script>

<style scoped>
.device-online-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.status-tag {
    min-width: 42px;
    text-align: center;
}
</style>
