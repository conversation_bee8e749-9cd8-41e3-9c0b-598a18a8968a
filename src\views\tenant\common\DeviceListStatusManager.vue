<template>
    <div class="device-list-status-manager">
        <slot></slot>
    </div>
</template>

<script>
import loggerFactory from '@/utils/logger';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('DeviceListStatusManager');

export default {
    name: 'DeviceListStatusManager',
    props: {
        // 设备ID列表
        deviceIds: {
            type: Array,
            default: () => []
        }
    },
    setup() {
        // 获取 Pinia store
        const deviceStore = useDeviceOnlineStore();

        return {
            deviceStore
        };
    },
    data() {
        return {
            // 当前设备ID列表
            currentDeviceIds: []
        };
    },
    watch: {
        // 监听设备ID列表变化
        deviceIds: {
            async handler(newIds) {
                // 更新当前设备ID列表
                this.currentDeviceIds = [...newIds];

                // 仅当新的设备ID列表不为空时才通知状态变更
                if (newIds && newIds.length > 0) {
                    this.notifyStatusChange();
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        // 获取所有设备状态
        getAllDevicesOnlineStatus() {
            if (this.deviceIds.length === 0) return {};

            const result = {};

            // 直接从 Pinia store 获取设备状态
            this.deviceIds.forEach(deviceId => {
                result[deviceId] = this.deviceStore.getDeviceStatus(deviceId);
            });

            return result;
        },

        // 通知状态变更
        notifyStatusChange() {
            const statuses = this.getAllDevicesOnlineStatus();
            this.$emit('online-status-change', statuses);
        },

        // 设置子组件
        setupChildComponents() {
            // 递归查找所有子组件
            const findDeviceSelector = vnode => {
                if (!vnode) return null;

                // 检查当前节点
                if (vnode.$options && vnode.$options.name === 'DeviceSelector') {
                    return vnode;
                }

                // 检查子节点
                if (vnode.$children && vnode.$children.length > 0) {
                    for (const child of vnode.$children) {
                        const result = findDeviceSelector(child);
                        if (result) return result;
                    }
                }

                return null;
            };

            // 查找DeviceSelector组件
            const deviceSelector = findDeviceSelector(this);

            // 如果找到DeviceSelector组件，设置managedByStatusManager属性
            if (deviceSelector) {
                logger.debug('找到DeviceSelector组件，设置managedByStatusManager属性');
                deviceSelector.managedByStatusManager = true;
            }
        }
    },
    async mounted() {
        // 查找子组件中的DeviceSelector，并设置managedByStatusManager属性
        await this.$nextTick(() => {
            this.setupChildComponents();
        });

        // watch 中 immediate: true 会在 deviceIds 初始化或变更时调用 notifyStatusChange
    },
    beforeDestroy() {
        this.currentDeviceIds = [];
    }
};
</script>
