<template>
    <div class="well-header">
        <div class="header-left">
            <!-- 标签页切换 -->
            <el-radio-group v-model="currentTab" @change="handleTabChange" size="small">
                <el-radio-button label="monitor">数据监控</el-radio-button>
                <el-radio-button label="history">历史数据</el-radio-button>
            </el-radio-group>

            <!-- 分页控制（仅在数据监控模式下显示） -->
            <div v-if="currentTab === 'monitor'" class="page-controls">
                <span>翻屏显示：</span>
                <el-button-group>
                    <el-tooltip effect="dark" :content="currentPage <= 1 ? '没有了' : '上一屏'" placement="bottom">
                        <el-button
                            type="primary"
                            color="#662f8f"
                            round
                            size="small"
                            :disabled="currentPage <= 1"
                            @click="prevPage"
                            icon="el-icon-d-arrow-left"
                        >
                        </el-button>
                    </el-tooltip>
                    <el-tooltip effect="dark" :content="currentPage >= pageTotal ? '没有了' : '下一屏'" placement="bottom">
                        <el-button
                            type="primary"
                            color="#662f8f"
                            round
                            size="small"
                            :disabled="currentPage >= pageTotal"
                            @click="nextPage"
                            icon="el-icon-d-arrow-right"
                        >
                        </el-button>
                    </el-tooltip>
                </el-button-group>
            </div>
        </div>

        <div class="header-right">
            <wellInfo :wellInfo="wellInfo"></wellInfo>
        </div>
    </div>
</template>

<script>
import wellInfo from "./wellInfo.vue";

export default {
    name: 'wellHeader',
    components: {
        wellInfo
    },
    props: {
        // 当前油井信息
        wellInfo: {
            type: Object,
            required: true,
            default: () => ({
                id: 0,
                name: '',
                level: 0
            })
        },
        // 当前油井屏幕分页信息
        pageInfo: {
            type: Object,
            required: true,
            default: () => ({
                currentPage: 1,
                pageShow: 4,
                pageTotal: 1
            })
        },
        // 当前激活的标签页
        activeTab: {
            type: String,
            default: 'monitor'
        }
    },
    data() {
        return {
            currentPage: this.pageInfo.currentPage || 1,
            pageTotal: this.pageInfo.pageTotal || 1,
            currentTab: this.activeTab
        };
    },
    watch: {
        pageInfo: {
            handler(val) {
                this.currentPage = val.currentPage || 1;
                this.pageTotal = val.pageTotal || 1;
            },
            immediate: true,
            deep: true
        },
        activeTab: {
            handler(val) {
                this.currentTab = val;
            },
            immediate: true
        }
    },
    methods: {
        /**
         * 翻页上一屏
         */
        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.updatePageInfo();
            }
        },
        /**
         * 翻页下一屏
         */
        nextPage() {
            if (this.currentPage < this.pageTotal) {
                this.currentPage++;
                this.updatePageInfo();
            }
        },
        /**
         * 更新分页信息
         */
        updatePageInfo() {
            this.$router.push({
                query: {
                    ...this.$route.query,
                    page: this.currentPage.toString()
                }
            });
        },

        /**
         * 处理标签页切换
         * @param {string} tab 标签页名称
         */
        handleTabChange(tab) {
            this.$emit('tab-change', tab);
        }
    }
}
</script>

<style lang="scss" scoped>
.well-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;

    .header-left {
        display: flex;
        align-items: center;
        gap: 20px;

        .page-controls {
            display: flex;
            align-items: center;
            gap: 10px;

            span {
                font-size: 14px;
                color: #666;
            }
        }
    }

    .header-right {
        display: flex;
        align-items: center;
        color: #666;
    }
}
</style>
