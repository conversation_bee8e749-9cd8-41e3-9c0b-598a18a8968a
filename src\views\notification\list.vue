<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <el-button-group>
                    <!-- 根据选中行的状态来决定显示"设为已读"还是"设为未读" -->
                    <el-button
                        v-if="!areAllSelectedRead"
                        type="primary"
                        icon="el-icon-finished"
                        color="#1C409A"
                        :disabled="!hasUnreadInSelection"
                        @click="handleBatchRead"
                        >设为已读</el-button
                    >
                    <el-button
                        v-else
                        type="warning"
                        icon="el-icon-notification"
                        color="#6a2ea1"
                        :disabled="selectedRows.length === 0"
                        @click="handleBatchUnread"
                        >设为未读</el-button
                    >
                    <el-button
                        type="danger"
                        icon="el-icon-circle-close"
                        color="#cc8cb7"
                        style="
                            --el-button-text-color: var(--el-color-white);
                            --el-button-hover-text-color: var(--el-color-white);
                        "
                        :disabled="selectedRows.length === 0"
                        @click="handleBatchDelete"
                        >批量删除</el-button
                    >
                </el-button-group>
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <!-- 在所有消息列表时显示类型筛选 -->
                    <el-select
                        v-if="!type"
                        v-model="searchParams.type"
                        placeholder="全部类型"
                        style="width: 120px"
                        @change="handleSearch"
                    >
                        <el-option
                            v-for="item in MESSAGE_TYPE_OPTIONS"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    <el-select
                        v-model="searchParams.readStatus"
                        placeholder="阅读状态"
                        style="width: 120px"
                        @change="handleSearch"
                    >
                        <el-option
                            v-for="item in readStatusOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                    <el-input
                        v-model="searchParams.keyword"
                        clearable
                        placeholder="搜索消息内容"
                        style="width: 200px"
                        @keyup.enter="handleSearch"
                        @clear="handleSearch"
                    ></el-input>
                    <el-button type="primary" @click="handleSearch" icon="el-icon-search" color="#1C409A"
                        >搜索</el-button
                    >
                </div>
            </div>
        </el-header>
        <el-main class="p0">
            <scTable
                ref="messageTable"
                :apiObj="messageDataObj"
                :params="searchParams"
                row-key="id"
                @selection-change="handleSelectionChange"
                class="custom-list-table"
            >
                <el-table-column type="selection" width="55" />
                <el-table-column label="标题" prop="title" min-width="200" show-overflow-tooltip>
                    <template #default="scope">
                        <span :class="{ 'unread-message': !scope.row.is_read }">
                            {{ scope.row.title }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column label="类型" prop="type" width="100">
                    <template #default="scope">
                        <el-tag :type="getTypeTag(scope.row.type)">
                            {{ getTypeName(scope.row.type) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="发送时间" prop="create_at" width="180" style="color: gray" />
                <el-table-column label="状态" width="100">
                    <template #default="scope">
                        <el-tag
                            effect="plain"
                            :type="scope.row.is_read ? 'warning' : 'success'"
                            :color="scope.row.is_read ? 'rgba(255,71,71,0.05)' : 'rgba(0,131,25,0.05)'"
                            size="small"
                        >
                            {{ scope.row.is_read ? '已读' : '未读' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="180">
                    <template #default="scope">
                        <el-button @click="handleView(scope.row)" type="info" size="small" color="#28a745"
                            >查看</el-button
                        >
                        <el-button
                            v-if="!scope.row.is_read"
                            @click="handleMarkRead(scope.row.id)"
                            type="primary"
                            size="small"
                            color="#1C409A"
                            >标记已读</el-button
                        >
                        <el-button
                            v-else
                            @click="handleMarkUnRead(scope.row.id)"
                            type="primary"
                            size="small"
                            color="#6a2ea1"
                            >标记未读</el-button
                        >
                        <!-- 新增删除按钮 -->
                        <el-button @click="handleDelete(scope.row.id)" type="danger" size="small">删除</el-button>
                    </template>
                </el-table-column>
            </scTable>
        </el-main>
    </el-container>

    <el-drawer v-model="detailVisible" :size="600" title="消息详情" destroy-on-close>
        <receive-detail v-if="detailVisible" :message="currentMessage" :update-list-data="handleSearch" />
    </el-drawer>
</template>

<script>
import { getTypeTag, getTypeName, MESSAGE_TYPE_OPTIONS, READ_STATUS_OPTIONS } from '@/constants/notification';
import ReceiveDetail from './components/receive-detail.vue';
import NotificationUtils from '@/utils/notification';
import { searchNotification } from '@/utils/notification';

export default {
    name: 'notification-list',
    components: { ReceiveDetail },
    props: {
        type: {
            type: [Number, String],
            default: null
        }
    },
    data() {
        return {
            MESSAGE_TYPE_OPTIONS,
            readStatusOptions: READ_STATUS_OPTIONS,
            messageDataObj: this.$API.notification.list, // 使用完整的 API 对象
            searchParams: {
                type: this.type || null,
                keyword: '',
                readStatus: null
            },
            detailVisible: false,
            currentMessageId: null,
            selectedRows: [],
            currentMessage: null
        };
    },
    computed: {
        // 判断选中项中是否有未读消息
        hasUnreadInSelection() {
            return this.selectedRows.some(row => !row.is_read);
        },
        // 判断选中的消息是否全部已读
        areAllSelectedRead() {
            return this.selectedRows.length > 0 && this.selectedRows.every(row => row.is_read);
        }
    },
    watch: {
        type: {
            handler(newType) {
                // 更新搜索类型参数
                this.searchParams.type = newType || null;
                this.searchParams.keyword = null; // 重置关键词
                this.searchParams.readStatus = null; // 切换类型时也保持全部消息
                this.handleSearch();
            },
            immediate: true // 立即执行一次
        }
    },
    methods: {
        getTypeTag,
        getTypeName,
        /**
         * 搜索消息列表
         */
        handleSearch() {
            if (!this.$refs.messageTable) return;
            searchNotification(this.$refs.messageTable, this.searchParams);
        },

        /**
         * 查看消息详情
         * @param {Object} row - 消息数据行
         */
        async handleView(row) {
            this.currentMessage = { ...row };
            this.detailVisible = true;
        },

        /**
         * 标记消息为已读
         * @param {number} id - 消息ID
         */
        async handleMarkRead(id) {
            const success = await NotificationUtils.markStatus(id, true);
            if (success) {
                this.handleSearch();
            }
        },

        /**
         * 标记消息为未读
         * @param {number} id - 消息ID
         */
        async handleMarkUnRead(id) {
            const success = await NotificationUtils.markStatus(id, false);
            if (success) {
                this.handleSearch();
            }
        },

        /**
         * 表格选择项变化处理
         * @param {Array} rows - 选中的行数据
         */
        handleSelectionChange(rows) {
            this.selectedRows = rows;
        },

        /**
         * 批量标记已读
         */
        async handleBatchRead() {
            if (!this.hasUnreadInSelection) return;

            const unreadIds = this.selectedRows.filter(row => !row.is_read).map(row => row.id);

            const success = await NotificationUtils.markBatchStatus(unreadIds);
            if (success) {
                this.handleSearch();
                this.selectedRows = [];
            }
        },

        /**
         * 批量删除
         */
        async handleBatchDelete() {
            try {
                await this.$confirm('确定要删除选中的消息吗？', '提示', {
                    type: 'warning'
                });

                const ids = this.selectedRows.map(row => row.id);
                const success = await NotificationUtils.batchDelete(ids);
                if (success) {
                    this.handleSearch();
                    this.selectedRows = [];
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('批量删除失败:', error);
                }
            }
        },

        /**
         * 删除消息
         * @param {number} id - 消息ID
         */
        async handleDelete(id) {
            try {
                await this.$confirm('确定要删除该消息吗？', '提示', { type: 'warning' });
                const result = await NotificationUtils.deleteMessage(id);
                if (result.success) {
                    this.handleSearch();
                }
            } catch (error) {
                if (error !== 'cancel') console.error('删除失败:', error);
            }
        },

        /**
         * 批量设置未读
         */
        async handleBatchUnread() {
            try {
                await this.$confirm('确定要将选中的消息设为未读吗？', '提示', {
                    type: 'warning'
                });

                // 获取已读消息的ID列表
                const ids = this.selectedRows.filter(row => row.is_read).map(row => row.id);

                const success = await NotificationUtils.markBatchStatus(ids, false);
                if (success) {
                    this.handleSearch();
                    this.selectedRows = [];
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('批量设置未读失败:', error);
                }
            }
        }
    }
};
</script>

<style scoped>
.unread-message {
    font-weight: bold;
}
.right-panel-search .el-select {
    margin-right: 10px;
}
</style>
