/**
 * 消息类型配置（和后端保持统一）
 */
export const MESSAGE_TYPES = {
    TYPE_SYSTEM: 1, // 系统消息
    TYPE_NOTICE: 2, // 通知公告
    TYPE_MESSAGE: 3 // 私信
};

export const MESSAGE_TYPE_CONFIG = {
    [MESSAGE_TYPES.TYPE_SYSTEM]: { name: '系统消息', tag: 'warning' },
    [MESSAGE_TYPES.TYPE_NOTICE]: { name: '公告通知', tag: 'danger' },
    [MESSAGE_TYPES.TYPE_MESSAGE]: { name: '　私信　', tag: 'success' }
};

/**
 * 消息类型配置项（用于下拉选择）
 */
export const MESSAGE_TYPE_OPTIONS = [
    { label: '全部类型', value: null },
    { label: MESSAGE_TYPE_CONFIG[MESSAGE_TYPES.TYPE_SYSTEM].name, value: MESSAGE_TYPES.TYPE_SYSTEM },
    { label: MESSAGE_TYPE_CONFIG[MESSAGE_TYPES.TYPE_NOTICE].name, value: MESSAGE_TYPES.TYPE_NOTICE },
    { label: MESSAGE_TYPE_CONFIG[MESSAGE_TYPES.TYPE_MESSAGE].name, value: MESSAGE_TYPES.TYPE_MESSAGE }
];

/**
 * 获取消息类型标签样式
 */
export const getTypeTag = type => {
    return MESSAGE_TYPE_CONFIG[type]?.tag || 'default';
};

/**
 * 获取消息类型名称
 */
export const getTypeName = type => {
    return MESSAGE_TYPE_CONFIG[type]?.name || '未知类型';
};

/**
 * 接收范围配置
 */
export const RECEIVE_RANGES = {
    RECEIVE_ALL: 1, // 全体用户
    RECEIVE_DEPARTMENT: 2, // 按部门
    RECEIVE_USER: 3 // 按用户
};

export const RECEIVE_RANGE_CONFIG = {
    [RECEIVE_RANGES.RECEIVE_ALL]: { name: '全体用户', tag: 'danger' },
    [RECEIVE_RANGES.RECEIVE_DEPARTMENT]: { name: '按部门', tag: 'warning' },
    [RECEIVE_RANGES.RECEIVE_USER]: { name: '按用户', tag: 'info' }
};

/**
 * 接收范围配置项（用于下拉选择）
 */
export const RECEIVE_RANGE_OPTIONS = [
    { label: RECEIVE_RANGE_CONFIG[RECEIVE_RANGES.RECEIVE_ALL].name, value: RECEIVE_RANGES.RECEIVE_ALL },
    { label: RECEIVE_RANGE_CONFIG[RECEIVE_RANGES.RECEIVE_DEPARTMENT].name, value: RECEIVE_RANGES.RECEIVE_DEPARTMENT },
    { label: RECEIVE_RANGE_CONFIG[RECEIVE_RANGES.RECEIVE_USER].name, value: RECEIVE_RANGES.RECEIVE_USER }
];

/**
 * 获取接收范围标签样式
 */
export const getRangeTag = range => {
    return RECEIVE_RANGE_CONFIG[range]?.tag || 'default';
};

/**
 * 获取接收范围名称
 */
export const getRangeName = range => {
    return RECEIVE_RANGE_CONFIG[range]?.name || '未知范围';
};

/**
 * 互动类型配置
 */
export const INTERACTION_TYPES = {
    INTERACTION_CANCEL: 0, // 取消
    INTERACTION_LIKE: 1, // 点赞
    INTERACTION_DISLIKE: -1 // 踩
};

/**
 * 菜单项配置
 */
export const MENU_ITEMS = [
    {
        name: 'all',
        title: '所有消息', // 保留自定义标题
        icon: 'el-icon-chat-line-square',
        type: null,
        component: 'list'
    },
    {
        name: 'system',
        title: MESSAGE_TYPE_CONFIG[MESSAGE_TYPES.TYPE_SYSTEM].name, // 从配置中读取标题
        icon: 'el-icon-bell',
        type: MESSAGE_TYPES.TYPE_SYSTEM,
        component: 'list'
    },
    {
        name: 'task',
        title: MESSAGE_TYPE_CONFIG[MESSAGE_TYPES.TYPE_NOTICE].name, // 从配置中读取标题
        icon: 'el-icon-chat-dot-round',
        type: MESSAGE_TYPES.TYPE_NOTICE,
        component: 'list'
    },
    {
        name: 'message',
        title: MESSAGE_TYPE_CONFIG[MESSAGE_TYPES.TYPE_MESSAGE].name, // 从配置中读取标题
        icon: 'el-icon-message',
        type: MESSAGE_TYPES.TYPE_MESSAGE,
        component: 'list'
    },
    {
        name: 'sent',
        title: '已发送', // 保留自定义标题
        icon: 'el-icon-position',
        type: null,
        component: 'sent'
    }
];

/**
 * 阅读状态配置项（用于下拉选择）
 */
export const READ_STATUS_OPTIONS = [
    { label: '全部消息', value: null },
    { label: '未读消息', value: 0 },
    { label: '已读消息', value: 1 }
];
