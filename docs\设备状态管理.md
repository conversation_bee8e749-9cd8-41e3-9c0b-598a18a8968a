## 设备在线状态管理系统总结

我们设计并实现了一个高效简洁的设备在线状态管理系统，包括以下几个部分：

### 1. 设备在线状态服务 ( deviceOnlineService.js)

这是系统的核心，负责管理设备的在线状态：

心跳监听：监听设备心跳消息，自动更新设备状态
状态探测：通过发送 trace 消息探测设备是否在线
状态管理：维护设备在线状态，处理心跳超时
事件通知：通过监听器机制通知组件设备状态变更

### 2. 设备在线状态组件 ( DeviceOnlineStatus.vue)

一个可复用的组件，用于显示单个设备的在线状态：

状态显示：使用标签显示设备的在线/离线状态
状态刷新：提供刷新按钮，手动探测设备状态
状态提示：显示设备最后心跳时间的提示信息

### 3. 设备选择器集成 ( DeviceSelector.vue)

将设备在线状态功能集成到设备选择器组件中：

可配置显示：通过 showOnlineStatus 属性控制是否显示设备在线状态
自动探测：表格数据加载完成后自动探测设备状态
组件复用：使用 DeviceOnlineStatus 组件显示设备状态

### 使用方法

#### 1、显示设备在线状态：

```
<DeviceSelector :showOnlineStatus="true" />
```

#### 2、单独使用设备在线状态组件：

```
<DeviceOnlineStatus :device-id="deviceId" />
```

#### 4、监听设备状态变更：

```
import deviceOnlineService from '@/services/deviceOnlineService';

// 添加状态监听器
deviceOnlineService.addStatusListener(deviceId, (status) => {
  console.log(`设备 ${deviceId} 状态: ${status.online ? '在线' : '离线'}`);
});

// 移除状态监听器
deviceOnlineService.removeStatusListener(deviceId, listener);
```

### 技术特点

实时监控：通过 MQTT 消息实时监控设备状态
高效探测：使用 trace 消息高效探测设备状态
状态持久化：设备状态在内存中持久化，避免重复探测
组件化设计：使用可复用组件，方便集成到不同页面
事件驱动：使用事件驱动架构，降低组件间耦合
自动清理：自动清理过期的设备状态，避免内存泄漏
这个设计简洁高效，满足了您的所有要求，并且易于集成到现有系统中。
