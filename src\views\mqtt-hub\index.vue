<template>
    <el-container>
        <el-aside v-loading="false" width="360px">
            <el-container>
                <el-header style="height:80px;">
                    <div class="connection-header">
                        <h3>EMQX WebSocket 实时通信</h3>
                        <span>ClientId: {{ clientId }}</span>
                    </div>
                </el-header>
                <el-main class="aside-main">
                    <mqtt-aside-main
                        :is-connected="isConnected"
                        @topics-change="handleTopicsChange"
                        @message-received="handleMessageReceived"
                    />
                </el-main>
                <el-footer style="padding:0;">
                    <div class="connection-info" v-if="isConnected">
                        <el-alert :title="`MQTT连接已建立`" type="success" :closable="false" show-icon />
                    </div>
                    <div class="connection-info" v-else>
                        <el-alert title="MQTT连接未建立" type="info" :closable="false" show-icon />
                    </div>
                </el-footer>
            </el-container>
        </el-aside>
        <el-container>
            <el-main class="p0">
                <div class="message-container">
                    <received-messages :messages="receivedMessages" />
                    <sent-messages :messages="sentMessages" />
                </div>
            </el-main>
            <el-footer class="message-send" style="height:180px;padding:0;background-color:#fff">
                <mqtt-message-send
                    :is-connected="isConnected"
                    :topics="subscribedTopics"
                    @message-sent="handleMessageSent"
                />
            </el-footer>
        </el-container>
    </el-container>
</template>

<script>
import MqttAsideMain from './components/aside-main.vue';
import MqttMessageSend from './components/message-send.vue';
import ReceivedMessages from './components/received-messages.vue';
import SentMessages from './components/sent-messages.vue';
import mqttService from '@/services/mqttService';
import mqttMessageService from '@/services/mqttMessageService';

export default {
    name: 'MqttTestLayout',
    components: {
        MqttAsideMain,
        MqttMessageSend,
        ReceivedMessages,
        SentMessages
    },
    data() {
        return {
            isConnected: mqttService.isConnected,
            subscribedTopics: [],
            receivedMessages: [],
            sentMessages: [],
            clientId: mqttService.client?.options?.clientId || '-'
        };
    },

    async created() {
        // 加载历史消息
        this.receivedMessages = await mqttMessageService.getReceivedMessages();
        this.sentMessages = await mqttMessageService.getSentMessages();

        // 添加事件监听
        mqttService.addStateListener(this.handleMqttStateChange);
        window.addEventListener('mqtt-message-received', this.handleMessageReceived);
        window.addEventListener('mqtt-message-sent', this.handleMessageSent);
    },

    beforeDestroy() {
        mqttService.removeStateListener(this.handleMqttStateChange);
        window.removeEventListener('mqtt-message-received', this.handleMessageReceived);
        window.removeEventListener('mqtt-message-sent', this.handleMessageSent);
    },
    methods: {
        handleMqttStateChange(status) {
            this.isConnected = status;
            this.clientId = mqttService.client?.options?.clientId || '-';
        },

        handleConnectionChange(status) {
            this.isConnected = status;
            if (status) {
                this.clientId = mqttService.client?.options?.clientId || '-';
            } else {
                this.clientId = '-';
            }
        },

        handleTopicsChange(topics) {
            this.subscribedTopics = topics;
        },

        async handleMessageReceived(event) {
            this.receivedMessages = await mqttMessageService.getReceivedMessages();
        },

        async handleMessageSent(event) {
            this.sentMessages = await mqttMessageService.getSentMessages();
        }
    }
};
</script>

<style scoped lang="scss">
.el-main.p0 {
    display: flex;
    flex-direction: column;
    padding: 0;
}

.connection-header {
    h3 {
        font-size: 20px;
        padding: 0 0 5px 0;
    }

    span {
        font-size: 12px;
        color: #999;
    }
}

.message-container {
    margin-top: 0;
    display: flex;
    gap: 1px;
    flex: 1;
    height: 100%;
    background-color: #e4e7ed;

    > * {
        flex: 1;
        min-width: 0; // 防止flex子项溢出
        background-color: #fff;
    }
}

.aside-main {
    padding: 20px;
    background-color: #fff;
}

.el-alert {
    height: 59px;
}
</style>
