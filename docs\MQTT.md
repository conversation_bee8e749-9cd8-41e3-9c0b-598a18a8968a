# MQTT服务使用说明

## 概述

本服务提供了与EMQX服务器进行WebSocket连接的MQTT客户端功能，用于实现设备实时通信。通过该服务，前端可以订阅特定主题，接收后端推送的消息，也可以向特定主题发布消息。

## 配置说明

MQTT连接配置位于 `src/config/mqtt.js` 文件中，包含以下主要配置项：

- `serverUrl`: MQTT服务器WebSocket地址，默认为 `ws://localhost:8083/mqtt`
- `options`: 连接选项，包括超时时间、重连间隔等
- `topics`: 预定义的主题模板，用于不同类型的消息通信

## 使用方法

### 1. 初始化连接

```javascript
import mqttService from '@/services/mqttService';

// 初始化MQTT客户端连接
mqttService.init({
  url: 'ws://your-emqx-server:8083/mqtt', // EMQX WebSocket地址
  clientId: 'your-client-id', // 可选，默认随机生成
  username: 'your-username', // 可选，如需认证
  password: 'your-password'  // 可选，如需认证
});
```

### 2. 订阅主题

#### 2.1 手动订阅主题

```javascript
// 订阅主题并处理接收到的消息
mqttService.subscribe('tenant/device/status', (message, topic) => {
  console.log(`收到主题 ${topic} 的消息:`, message);
  // 处理接收到的消息...
});
```

#### 2.2 自动订阅主题

系统支持在MQTT连接成功后自动订阅配置的主题列表。主题配置位于 `src/config/mqttTopics.js` 文件中。

```javascript
// 配置示例
export default {
  // 是否启用自动订阅功能
  enableAutoSubscribe: true,

  // 自动订阅的主题列表
  // 支持通配符：+ (单层通配符) 和 # (多层通配符)
  // 支持变量替换：使用 {变量名} 格式，目前支持 {$tenantCode} 变量
  subscribeTopics: [
    'helio/tenant/{$tenantCode}/device/status',
    'helio/tenant/{$tenantCode}/device/+/pong',
    'helio/tenant/{$tenantCode}/system/notice'
  ]
};
```

系统会在MQTT连接成功后自动订阅这些主题，并使用当前登录用户的租户编码替换 `{$tenantCode}` 变量。

如果需要手动触发自动订阅，可以调用：

```javascript
// 手动触发自动订阅配置的主题
mqttService.autoSubscribeTopics();
```

### 3. 发布消息

```javascript
// 发布消息到指定主题
mqttService.publish('tenant/device/control', {
  deviceId: 'device-001',
  action: 'restart'
});

// 也可以发送字符串消息
mqttService.publish('tenant/device/control', 'restart-device-001');
```

### 4. 断开连接

```javascript
// 断开MQTT连接
mqttService.disconnect();
```

## 示例组件

项目中提供了一个示例组件 `MqttTest.vue`，展示了如何在Vue组件中使用MQTT服务进行实时通信。您可以参考该组件的实现，了解MQTT服务的完整使用流程。

## 注意事项

1. 确保EMQX服务器已正确配置并启动
2. WebSocket连接地址应与EMQX服务器配置一致
3. 如果EMQX服务器启用了认证，需要提供正确的用户名和密码
4. 在组件销毁前应调用 `disconnect()` 方法断开连接，避免资源泄漏
5. 对于生产环境，建议使用环境变量或配置文件动态获取MQTT服务器地址
