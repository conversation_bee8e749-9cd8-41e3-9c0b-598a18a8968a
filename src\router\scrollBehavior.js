import { nextTick } from 'vue';
import { useViewTabsStore } from '@/stores/viewTabs';

export function beforeEach(to, from) {
    const layoutMain = document.querySelector('#layout-main');

    if (!layoutMain) {
        return false;
    }

    const viewTabsStore = useViewTabsStore();

    viewTabsStore.updateViewTabs({
        fullPath: from.fullPath,
        scrollTop: layoutMain.scrollTop
    });
}

export function afterEach(to) {
    const layoutMain = document.querySelector('#layout-main');

    if (!layoutMain) {
        return false;
    }

    const viewTabsStore = useViewTabsStore();

    nextTick(() => {
        const beforeRoute = viewTabsStore.viewTabs.filter(v => v.fullPath == to.fullPath)[0];
        if (beforeRoute) {
            layoutMain.scrollTop = beforeRoute.scrollTop || 0;
        }
    });
}
