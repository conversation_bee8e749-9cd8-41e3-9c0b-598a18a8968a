/**
 * 日志工具类
 * 提供统一的日志打印接口，支持分组、折叠和不同级别的日志
 * 支持显示调用位置的行号
 */

// 是否启用日志
const isLogEnabled = process.env.NODE_ENV !== 'production';

// 日志级别
const LOG_LEVELS = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
    NONE: 4
};

// 当前日志级别，根据环境设置
let currentLogLevel = process.env.NODE_ENV === 'production' ? LOG_LEVELS.WARN : LOG_LEVELS.DEBUG;

/**
 * 获取调用堆栈信息
 * @returns {string} 调用位置信息，格式为 "文件名:行号"
 */
function getCallerInfo() {
    try {
        // 创建一个错误对象以获取堆栈信息
        const err = new Error();
        const stack = err.stack.split('\n');

        // 堆栈的第一行是 "Error"
        // 第二行是当前函数 getCallerInfo
        // 第三行是日志方法 (debug, info, warn, error)
        // 第四行是实际调用日志方法的位置
        if (stack.length >= 4) {
            // 提取第四行的文件名和行号
            const callerLine = stack[3].trim();

            // 匹配文件路径和行号
            // 格式可能是 "at functionName (file:line:column)" 或 "at file:line:column"
            const match = callerLine.match(/\(([^)]+)\)/) || callerLine.match(/at\s+([^\s]+)/);

            if (match && match[1]) {
                const fileInfo = match[1];

                // 提取文件名和行号
                const fileMatch = fileInfo.match(/([^\/\\]+):(\d+):/);
                if (fileMatch) {
                    // 提取文件名，去掉可能的参数部分（如 file.js?t=1234567890）
                    let fileName = fileMatch[1];
                    // 如果文件名包含参数，只保留问号前面的部分
                    if (fileName.includes('?')) {
                        fileName = fileName.split('?')[0];
                    }
                    const lineNumber = fileMatch[2];
                    return `${fileName}:${lineNumber}`;
                }
            }
        }
    } catch (e) {
        // 如果解析堆栈失败，返回空字符串
        console.error('获取调用堆栈信息失败:', e);
    }

    return '';
}

// 日志分组计数器
let groupCounter = 0;

/**
 * 设置日志级别
 * @param {number} level 日志级别
 */
function setLogLevel(level) {
    if (level >= LOG_LEVELS.DEBUG && level <= LOG_LEVELS.NONE) {
        currentLogLevel = level;
    }
}

/**
 * 获取当前日志级别
 * @returns {number} 当前日志级别
 */
function getLogLevel() {
    return currentLogLevel;
}

/**
 * 创建带有前缀的日志函数
 * @param {string} prefix 日志前缀
 * @returns {Object} 日志函数对象
 */
function createLogger(prefix) {
    const logPrefix = prefix ? `[${prefix}]` : '';

    return {
        /**
         * 打印调试日志
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        debug(message, ...args) {
            if (!isLogEnabled || currentLogLevel > LOG_LEVELS.DEBUG) return;
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo ? `${logPrefix} [${callerInfo}] ${message}` : `${logPrefix} ${message}`;
            // 使用 console.log 代替 console.debug，确保在所有浏览器中都能显示
            console.debug('%c[DEBUG]', 'color:#ffa600;', logMessage, ...args);
        },

        /**
         * 打印信息日志
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        info(message, ...args) {
            if (!isLogEnabled || currentLogLevel > LOG_LEVELS.INFO) return;
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo ? `${logPrefix} [${callerInfo}] ${message}` : `${logPrefix} ${message}`;
            console.info(logMessage, ...args);
        },

        /**
         * 打印警告日志
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        warn(message, ...args) {
            if (!isLogEnabled || currentLogLevel > LOG_LEVELS.WARN) return;
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo ? `${logPrefix} [${callerInfo}] ${message}` : `${logPrefix} ${message}`;
            console.warn(logMessage, ...args);
        },

        /**
         * 打印错误日志
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        error(message, ...args) {
            if (!isLogEnabled || currentLogLevel > LOG_LEVELS.ERROR) return;
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo ? `${logPrefix} [${callerInfo}] ${message}` : `${logPrefix} ${message}`;
            console.error(logMessage, ...args);
        },

        /**
         * 开始一个折叠的日志组
         * @param {string} label 组标签
         * @param {boolean} collapsed 是否折叠，默认为true
         */
        group(label, collapsed = true) {
            if (!isLogEnabled) return;

            groupCounter++;

            // 根据日志级别决定是否显示组标签
            const showLabel = currentLogLevel <= LOG_LEVELS.INFO;

            // 获取调用位置信息
            const callerInfo = getCallerInfo();
            const groupLabel = callerInfo && showLabel
                ? `${logPrefix} [${callerInfo}] ${label}`
                : showLabel ? `${logPrefix} ${label}` : '';

            if (collapsed) {
                console.groupCollapsed(groupLabel);
            } else {
                console.group(groupLabel);
            }
        },

        /**
         * 结束当前日志组
         */
        groupEnd() {
            if (!isLogEnabled || groupCounter <= 0) return;

            console.groupEnd();
            groupCounter--;
        },

        /**
         * 打印带有时间戳的日志
         * @param {string} message 日志消息
         * @param {...any} args 其他参数
         */
        time(message, ...args) {
            if (!isLogEnabled || currentLogLevel > LOG_LEVELS.INFO) return;

            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN', { hour12: false }) + '.' + now.getMilliseconds().toString().padStart(3, '0');

            // 获取调用位置信息
            const callerInfo = getCallerInfo();
            const logMessage = callerInfo
                ? `${logPrefix} [${timeStr}] [${callerInfo}] ${message}`
                : `${logPrefix} [${timeStr}] ${message}`;

            console.info(logMessage, ...args);
        },

        /**
         * 打印对象，自动折叠
         * @param {string} label 标签
         * @param {Object} obj 要打印的对象
         */
        object(label, obj) {
            if (!isLogEnabled || currentLogLevel > LOG_LEVELS.DEBUG) return;

            // 获取调用位置信息并添加到标签中
            const callerInfo = getCallerInfo();
            const groupLabel = callerInfo ? `${label} [${callerInfo}]` : label;

            // 使用自定义前缀
            console.log('%c[DEBUG]', 'color: #6c757d;', `${logPrefix} 对象: ${groupLabel}`);
            console.dir(obj);
        }
    };
}

// 创建默认日志记录器
const defaultLogger = createLogger('App');

// 导出日志工具
export default {
    createLogger,
    setLogLevel,
    getLogLevel,
    LOG_LEVELS,
    ...defaultLogger
};
