<template>
    <el-empty :image-size="280" image="img/empty.png">
        <template #description>
            <div class="error-message">
                <h3>找不到该页面组件</h3>
                <p>当前路由对应的组件不存在，请检查组件路径是否正确</p>
            </div>
        </template>
        <el-button round size="large" type="primary" @click="$router.push('/')">返回首页</el-button>
    </el-empty>
</template>

<script>
export default {
    data() {
        return {};
    }
};
</script>

<style scoped lang="scss">
.error-message {
    line-height: 2.5;

    p {
        color: #aaa;
    }
}
</style>
