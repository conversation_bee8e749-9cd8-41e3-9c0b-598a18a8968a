@charset "utf-8";

// element重置
// <AUTHOR> <<PERSON>yu<PERSON>@live.com>
// @Datetime 2024-12-13

@use '../global/mixins' as *;

// 变量定义
$border-dark: 1px solid var(--el-border-color);
$title-font-size: 17px;
$title-font-weight: 600;

// Mixins
@mixin panel-common {
    @include flex-center;
    > * + * {
        margin-left: 10px;
    }
}

// 重置菜单样式
.el-menu {
    border: none !important;
    background: transparent;

    // {.el-menu-item}
    &-item {
        a {
            color: inherit;
            text-decoration: none;
            display: block;
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            left: 0;
        }

        &.is-active {
            background-color: var(--el-fill-color-light);
        }
    }

    .menu-tag {
        position: absolute;
        height: 18px;
        line-height: 18px;
        background: var(--el-color-danger);
        color: #ffffff;
        right: 20px;
        border-radius: 18px;
        padding: 0 6px;
    }

    .el-sub-menu__title {
        .menu-tag {
            right: 40px;
        }
    }
}

.el-menu--horizontal > li .menu-tag {
    display: none;
}

.el-form-item-msg {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    clear: both;
    width: 100%;

    .el-icon {
        top: 1px;
        margin-right: 5px;
    }
}

// 布局容器优化
.el-container {
    height: 100%;

    .el-aside {
        border-right: $border-dark;
    }
}

.el-header {
    @include flex-between;
    border-bottom: $border-dark;
    padding: 13px 15px;

    .left-panel,
    .right-panel {
        @include panel-common;
    }
}

.el-footer {
    border-top: $border-dark;
    padding: 13px 15px;
}

.el-main {
    padding: 25px;

    &.p0 {
        padding: 0;
    }
}

// 抽屉标题
.el-drawer__header {
    color: var(--el-text-color-primary);
}

// 抽屉内容区
.el-drawer__body {
    overflow: auto;
    padding: 0;
}

.el-tree {
    &.menu {
        .el-tree-node__content {
            height: 36px;
        }
    }
}

.el-table {
    font-size: 13px;
}

// 功能坞顶部菜单样式
.el-menu--horizontal {
    --el-menu-bg-color: transparent;
    --el-menu-hover-bg-color: transparent;
    --el-menu-text-color: var(--el-color-white);
    --el-menu-hover-text-color: var(--el-color-white);
    --el-menu-active-color: var(--el-color-white);

    > .el-menu-item {
        &.is-active {
            background-color: var(--el-color-primary-light-1);
        }
    }

    .el-menu {
        --el-menu-hover-text-color: var(--el-text-color-primary);
        --el-menu-text-color: var(--el-text-color-primary);
        --el-menu-active-color: var(--el-text-color-secondary);
    }
}

// 修复锁屏时下面有表格时，表格边框出现的问题
.el-table__inner-wrapper:before {
    z-index: 1;
}

// 多主题下的顶部菜单字体颜色
.layout-header .panel-item {
    color: var(--el-color-white);
}

// (权限)菜单树优化
.el-tree-node__label {
    @include flex-between;
    flex: 1;
    font-size: 14px;
    padding-right: 24px;

    .handle {
        display: none;

        i {
            top: 2px;
            left: 5px;
            color: #999999;

            &:hover {
                color: #333333;
            }
        }
    }

    &:hover {
        .handle {
            display: inline-block;
        }
    }

    .label {
        @include flex-center;
        height: 100%;

        i {
            font-size: 14px;
            width: 18px;
            margin-right: 0;
            text-align: center;
            vertical-align: middle;
        }
    }
}

// 修复锁屏后刷新页面遮住弹出框的问题
.el-loading-mask {
    z-index: 10;
}
