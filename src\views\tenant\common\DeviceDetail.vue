<template>
    <el-dialog
        title="设备详情"
        v-model="visible"
        width="60%"
        :before-close="handleClose"
        append-to-body
    >
        <el-descriptions :column="2" border>
            <el-descriptions-item label="设备名称">{{ device.name }}</el-descriptions-item>
            <el-descriptions-item label="设备标识">{{ device.code }}</el-descriptions-item>
            <el-descriptions-item label="设备状态">
                <el-tag :type="getStatusType(device.status)">{{ getStatusText(device.status) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ device.create_at }}</el-descriptions-item>
            <el-descriptions-item label="设备描述" :span="2">{{ device.description || '-' }}</el-descriptions-item>
        </el-descriptions>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">关闭</el-button>
        </span>
    </el-dialog>
</template>

<script>
import { deviceBusinessStatusUtils } from './constants';

export default {
    name: 'DeviceDetail',
    props: {
        modelValue: {
            type: Boolean,
            default: false
        },
        device: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        visible: {
            get() {
                return this.modelValue;
            },
            set(value) {
                this.$emit('update:modelValue', value);
            }
        }
    },
    methods: {
        handleClose() {
            this.$emit('update:modelValue', false);
        },
        getStatusType(status) {
            return deviceBusinessStatusUtils.getDeviceStatusType(status);
        },
        getStatusText(status) {
            return deviceBusinessStatusUtils.getDeviceStatusText(status);
        }
    }
};
</script>

<style lang="scss" scoped>
.el-descriptions {
    margin: 20px 0;
}
</style>
