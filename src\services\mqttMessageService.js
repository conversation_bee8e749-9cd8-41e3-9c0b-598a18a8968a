import mqttMessageModel from '@/model/mqttMessageModel';
import loggerFactory from '@/utils/logger';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('MQTTMsgService');

/**
 * MQTT消息服务
 * 负责管理MQTT消息的存储和检索
 * 全局持久化MQTT消息，无论mqtt-test组件是否打开
 */
class MqttMessageService {
    constructor() {
        this.initialized = false;
        this.messageCache = {
            received: [],
            sent: []
        };

        // 立即初始化，确保全局消息捕获
        this.init().then(() => {
            // 绑定消息处理事件
            window.addEventListener('mqtt-message-received', this.handleMessageReceived.bind(this));
            window.addEventListener('mqtt-message-sent', this.handleMessageSent.bind(this));
        });
    }

    /**
     * 初始化服务
     * 确保数据库初始化并预加载消息
     */
    async init() {
        if (this.initialized) return;

        logger.group('初始化MQTT消息服务');
        logger.info('开始初始化MQTT消息服务');

        try {
            // 数据库已经在模型构造函数中初始化，这里只需等待初始化完成
            logger.info('等待数据库初始化完成');
            await mqttMessageModel.initPromise;
            this.initialized = true;

            // 预加载消息到缓存
            logger.info('开始预加载消息到缓存');
            await this.preloadMessages();

            logger.info('MQTT消息服务初始化成功，消息持久化已启用');
            logger.groupEnd();
        } catch (error) {
            logger.error('MQTT消息服务初始化失败:', error);
            logger.groupEnd();
        }
    }

    /**
     * 预加载消息到缓存
     * 确保即使在组件未加载时也能访问历史消息
     */
    async preloadMessages() {
        logger.group('预加载MQTT消息');
        try {
            logger.info('加载接收消息');
            this.messageCache.received = await mqttMessageModel.getReceivedMessages();
            logger.info(`已加载 ${this.messageCache.received.length} 条接收消息`);

            logger.info('加载发送消息');
            this.messageCache.sent = await mqttMessageModel.getSentMessages();
            logger.info(`已加载 ${this.messageCache.sent.length} 条发送消息`);

            logger.info('预加载MQTT消息完成');
            logger.groupEnd();
        } catch (error) {
            logger.error('预加载MQTT消息失败:', error);
            logger.groupEnd();
        }
    }

    /**
     * 处理接收到的消息
     * @param {CustomEvent} event
     */
    async handleMessageReceived(event) {
        if (!this.initialized) {
            // 如果服务尚未初始化，先初始化
            await this.init();
        }

        try {
            const message = event.detail;
            // 保存到数据库
            await mqttMessageModel.addReceivedMessage(message);
            // 更新缓存
            this.messageCache.received = await mqttMessageModel.getReceivedMessages();

            // 使用debug级别记录日志，避免日志过多
            logger.debug(`已保存接收消息: ${message.topic}`);
        } catch (error) {
            logger.error('保存接收消息失败:', error);
        }
    }

    /**
     * 处理发送的消息
     * @param {CustomEvent} event
     */
    async handleMessageSent(event) {
        if (!this.initialized) {
            // 如果服务尚未初始化，先初始化
            await this.init();
        }

        try {
            const message = event.detail;
            // 保存到数据库
            await mqttMessageModel.addSentMessage(message);
            // 更新缓存
            this.messageCache.sent = await mqttMessageModel.getSentMessages();

            // 使用debug级别记录日志，避免日志过多
            logger.debug(`已保存发送消息: ${message.topic}`);
        } catch (error) {
            logger.error('保存发送消息失败:', error);
        }
    }

    /**
     * 获取接收的消息列表
     * @returns {Promise<Array>}
     */
    async getReceivedMessages() {
        if (!this.initialized) await this.init();

        // 刷新缓存并返回最新数据
        logger.debug('获取接收消息列表');
        this.messageCache.received = await mqttMessageModel.getReceivedMessages();
        logger.debug(`获取到 ${this.messageCache.received.length} 条接收消息`);

        return this.messageCache.received;
    }

    /**
     * 获取发送的消息列表
     * @returns {Promise<Array>}
     */
    async getSentMessages() {
        if (!this.initialized) await this.init();

        // 刷新缓存并返回最新数据
        logger.debug('获取发送消息列表');
        this.messageCache.sent = await mqttMessageModel.getSentMessages();
        logger.debug(`获取到 ${this.messageCache.sent.length} 条发送消息`);

        return this.messageCache.sent;
    }

    /**
     * 清理服务
     */
    destroy() {
        logger.group('销毁MQTT消息服务');

        logger.info('移除事件监听器');
        window.removeEventListener('mqtt-message-received', this.handleMessageReceived);
        window.removeEventListener('mqtt-message-sent', this.handleMessageSent);

        // 关闭数据库连接
        if (this.initialized && mqttMessageModel.close) {
            logger.info('关闭数据库连接');
            mqttMessageModel.close();
            this.initialized = false;
        }

        logger.info('MQTT消息服务已销毁');
        logger.groupEnd();
    }
}

const mqttMessageService = new MqttMessageService();
export default mqttMessageService;
