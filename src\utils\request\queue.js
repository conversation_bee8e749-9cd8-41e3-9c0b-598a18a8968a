class RequestQueue {
    constructor() {
        this.queue = new Map(); // 使用 Map 存储请求，key 为请求的唯一标识
    }

    // 生成请求的唯一标识
    generateRequestKey(config) {
        const { url, params = {}, data = {} } = config;
        // 将 URL、请求参数和请求体序列化后组合成唯一标识
        return `${url}|${JSON.stringify(params)}|${JSON.stringify(data)}`;
    }

    // 添加请求到队列，同一个请求只保留最新的
    enqueue(callback, config) {
        const requestKey = this.generateRequestKey(config);
        this.queue.set(requestKey, callback);
    }

    // 执行并清空队列
    flush() {
        // 只执行每个唯一请求的最新回调
        for (const callback of this.queue.values()) {
            callback();
        }
        this.clear();
    }

    // 清空队列
    clear() {
        this.queue.clear();
    }

    // 获取队列大小
    size() {
        return this.queue.size;
    }
}

export const requestQueue = new RequestQueue();
