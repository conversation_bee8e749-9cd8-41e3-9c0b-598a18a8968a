## Project Summary

This is a multi-tenant management system frontend built with Vue 3. 

### Key characteristics:

The project follows a "make complex things simple" philosophy and includes various custom components, business templates, and a configurable dashboard. It's designed for enterprise-level backend management systems with multi-tenant capabilities.

**Framework:** Vue 3 with Vite, Element Plus, and Pinia

**Architecture:** Single-page application (SPA) with component-based structure

### Features:
- Multiple exclusive components and business templates
- Complete authentication system with high-precision permission control
- Multiple layout modes to meet various visual needs
- Comprehensive API management with real network mocking
- Unified global and component configurations with hot updates after build
- Performance optimizations to reduce bandwidth requests and frontend processing
- Other features include multi-functional view tabs, dynamic permission menus, console configuration, and unified exception handling
- MQTT service integration for real-time device communication
- Dynamic permission menus and console configuration
- Unified exception handling
- Multi-functional view tabs
- Dynamic permission menus
- Console configuration

- Complete authentication system with fine-grained permissions
- Multiple layout options (default, header, menu, dock)
- API management with real network mocking
- Global and component configurations with hot updates
- Performance optimizations for bandwidth and frontend processing
- MQTT service integration for real-time device communication
