import { closeDatabase, db } from '@/utils/indexedDB';
import storage from '@/config/storage';

// 存储对象名称
const RECEIVED_STORE_NAME = storage.indexedDB.storeConfigs.mqttReceived.name;
const SENT_STORE_NAME = storage.indexedDB.storeConfigs.mqttSent.name;

// 保留的消息条数
const RETAINED_MESSAGES_COUNT = 10000;

/**
 * MQTT消息数据库模型
 */
class MqttMessageModel {
    // 初始化 Promise
    initPromise = null;

    /**
     * 构造函数
     */
    constructor() {
        console.log('初始化MQTT数据库模型');
        // 初始化 Promise
        this.initPromise = Promise.resolve();
    }

    /**
     * 获取接收的消息列表
     * @returns {Promise<Array>} 接收的消息列表
     */
    async getReceivedMessages() {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 使用 db 的 getAll 方法获取所有接收消息
            return await db.getAll(RECEIVED_STORE_NAME);
        } catch (error) {
            console.error('获取接收消息失败:', error);
            return [];
        }
    }

    /**
     * 获取发送的消息列表
     * @returns {Promise<Array>} 发送的消息列表
     */
    async getSentMessages() {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 使用 db 的 getAll 方法获取所有发送消息
            return await db.getAll(SENT_STORE_NAME);
        } catch (error) {
            console.error('获取发送消息失败:', error);
            return [];
        }
    }

    /**
     * 添加接收的消息
     * @param {Object} message 消息对象
     * @returns {Promise<IDBValidKey>} 添加的消息的键
     */
    async addReceivedMessage(message) {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            //console.log(`添加接收消息:`, message);

            // 使用 db 的 add 方法添加接收消息
            const result = await db.add(RECEIVED_STORE_NAME, message);

            // 获取所有接收消息
            const allMessages = await this.getReceivedMessages();

            // 增加存储容量，从50条增加到200条，确保即使组件未打开时也能保存更多消息
            if (allMessages.length >= RETAINED_MESSAGES_COUNT) {
                //console.log(`接收消息数量超过${RETAINED_MESSAGES_COUNT}条，删除旧消息`);

                // 按时间排序
                allMessages.sort((a, b) => b.id - a.id);

                // 获取要删除的消息ID
                const idsToDelete = allMessages.slice(RETAINED_MESSAGES_COUNT).map(msg => msg.id);

                // 删除旧消息
                for (const id of idsToDelete) {
                    await db.delete(RECEIVED_STORE_NAME, id);
                }
            }

            return result;
        } catch (error) {
            console.error('添加接收消息失败:', error);
            return null;
        }
    }

    /**
     * 添加发送的消息
     * @param {Object} message 消息对象
     * @returns {Promise<IDBValidKey>} 添加的消息的键
     */
    async addSentMessage(message) {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            //console.log(`添加发送消息:`, message);

            // 使用 db 的 add 方法添加发送消息
            const result = await db.add(SENT_STORE_NAME, message);

            // 获取所有发送消息
            const allMessages = await this.getSentMessages();

            // 增加存储容量，从50条增加到200条，确保即使组件未打开时也能保存更多消息
            if (allMessages.length >= RETAINED_MESSAGES_COUNT) {
                //console.log(`发送消息数量超过${RETAINED_MESSAGES_COUNT}条，删除旧消息`);

                // 按时间排序
                allMessages.sort((a, b) => b.id - a.id);

                // 获取要删除的消息ID
                const idsToDelete = allMessages.slice(RETAINED_MESSAGES_COUNT).map(msg => msg.id);

                // 删除旧消息
                for (const id of idsToDelete) {
                    await db.delete(SENT_STORE_NAME, id);
                }
            }

            return result;
        } catch (error) {
            console.error('添加发送消息失败:', error);
            return null;
        }
    }

    /**
     * 关闭数据库连接
     */
    close() {
        closeDatabase();
    }
}

// 创建数据库实例
const mqttMessageModel = new MqttMessageModel();

// 导出数据库实例
export default mqttMessageModel;
