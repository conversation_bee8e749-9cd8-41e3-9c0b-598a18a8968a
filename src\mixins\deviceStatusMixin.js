import deviceOnlineService from '@/services/deviceOnlineService';
import loggerFactory from '@/utils/logger';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('DeviceStatusMixin');

export default {
    data() {
        return {
            // 移除本地缓存，使用 Pinia store
        };
    },
    computed: {
        // 获取 Pinia store 实例
        deviceStore() {
            return useDeviceOnlineStore();
        }
    },
    methods: {
        /**
         * 检查设备是否在线
         * @param {number} deviceId 设备ID
         * @returns {boolean} 是否在线
         */
        isDeviceOnline(deviceId) {
            if (!deviceId) {
                logger.warn('无效的设备ID:', deviceId);
                return false;
            }

            // 直接从 Pinia store 获取最新状态
            return this.deviceStore.isDeviceOnline(deviceId);
        },

        /**
         * 检查设备是否可以操作
         * @param {Object} device 设备信息
         * @returns {boolean} 是否可以操作
         */
        canOperateDevice(device) {
            if (!device || typeof device !== 'object' || typeof device.id === 'undefined' || typeof device.status === 'undefined') {
                return false;
            }

            const isOnline = this.isDeviceOnline(device.id);
            const isEnabled = device.status === 1;

            return isEnabled && isOnline;
        },

        /**
         * 检查所有设备状态（已废弃，状态由 Pinia store 自动管理）
         * @param {Array<string>} deviceIds 设备ID列表
         * @deprecated 设备状态现在由 Pinia store 自动管理，不需要手动检查
         */
        async checkDevicesOnlineStatus(deviceIds) {
            // 设备状态现在由 Pinia store 和 MQTT 消息自动管理
            // 这个方法保留是为了向后兼容，但实际上不执行任何操作
            logger.debug('checkDevicesOnlineStatus 已废弃，设备状态由 Pinia store 自动管理');

            // 如果需要触发状态更新事件，可以从 store 获取当前状态
            if (deviceIds && deviceIds.length > 0) {
                const statuses = this.deviceStore.getDevicesOnlineStatus(deviceIds);
                this.$emit('online-status-change', statuses);
            }
        },

        /**
         * 处理设备状态变更（已废弃）
         * @deprecated 设备状态现在由 Pinia store 自动管理，不需要手动处理
         */
        handleDeviceOnlineStatusChange() {
            // 设备状态现在由 Pinia store 自动管理，不需要手动清除缓存
            logger.debug('handleDeviceOnlineStatusChange 已废弃，设备状态由 Pinia store 自动管理');
        }
    }
};