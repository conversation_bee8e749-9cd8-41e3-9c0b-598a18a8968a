import deviceOnlineService from '@/services/deviceOnlineService';
import loggerFactory from '@/utils/logger';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('DeviceStatusMixin');

export default {
    data() {
        return {
            deviceStatusCache: new Map(), // 设备状态缓存
            isCheckingStatus: false // 状态检查锁
        };
    },
    methods: {
        /**
         * 检查设备是否在线
         * @param {number} deviceId 设备ID
         * @returns {boolean} 是否在线
         */
        isDeviceOnline(deviceId) {
            if (!deviceId) {
                logger.warn('无效的设备ID:', deviceId);
                return false;
            }

            const deviceIdStr = String(deviceId);
            // 优先使用缓存的状态
            const cachedStatus = this.deviceStatusCache.get(deviceIdStr);
            if (cachedStatus !== undefined) {
                return cachedStatus;
            }

            // 如果缓存中没有，则从service获取
            const status = deviceOnlineService.isDeviceOnlineSync(deviceIdStr);
            this.deviceStatusCache.set(deviceIdStr, status);
            return status;
        },

        /**
         * 检查设备是否可以操作
         * @param {Object} device 设备信息
         * @returns {boolean} 是否可以操作
         */
        canOperateDevice(device) {
            if (!device || typeof device !== 'object' || typeof device.id === 'undefined' || typeof device.status === 'undefined') {
                return false;
            }

            const deviceIdStr = String(device.id);
            const isOnline = this.isDeviceOnline(device.id);
            const isEnabled = device.status === 1;

            return isEnabled && isOnline;
        },

        /**
         * 检查所有设备状态
         * @param {Array<string>} deviceIds 设备ID列表
         */
        async checkDevicesOnlineStatus(deviceIds) {
            if (!deviceIds || deviceIds.length === 0 || this.isCheckingStatus) return;

            try {
                this.isCheckingStatus = true;
                const statuses = await deviceOnlineService.getDevicesOnlineStatus(deviceIds);
                logger.debug('获取到的设备状态:', statuses);

                // 更新状态缓存
                statuses.forEach(status => {
                    if (status && typeof status.deviceId !== 'undefined' && typeof status.online === 'boolean') {
                        this.deviceStatusCache.set(status.deviceId, status.online);
                    }
                });

                // 触发状态更新
                this.$emit('online-status-change', statuses);
            } catch (error) {
                logger.error('检查设备状态时出错:', error);
            } finally {
                this.isCheckingStatus = false;
            }
        },

        /**
         * 处理设备状态变更
         */
        handleDeviceOnlineStatusChange() {
            // 清除状态缓存，强制重新获取状态
            this.deviceStatusCache.clear();
        }
    }
}; 