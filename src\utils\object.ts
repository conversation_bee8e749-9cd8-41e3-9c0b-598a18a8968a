/**
 * 深度合并对象
 * @param target 目标对象
 * @param source 源对象
 * @returns 合并后的对象
 */
export function deepMerge<T>(target: T, source: Partial<T>): T {
    if (!source) {
        return target;
    }

    const result = { ...target };

    Object.keys(source).forEach(key => {
        const targetValue = result[key];
        const sourceValue = source[key];

        if (Array.isArray(sourceValue) && Array.isArray(targetValue)) {
            result[key] = targetValue.concat(sourceValue);
        } else if (isObject(sourceValue) && isObject(targetValue)) {
            result[key] = deepMerge(targetValue, sourceValue);
        } else if (sourceValue !== undefined) {
            result[key] = sourceValue;
        }
    });

    return result;
}

/**
 * 判断是否为对象
 * @param item 
 * @returns boolean
 */
function isObject(item: any): boolean {
    return item && typeof item === 'object' && !Array.isArray(item);
}