<template>
    <div class="history-data-container">
        <!-- 查询条件 -->
        <el-card class="query-card" shadow="never">
            <template #header>
                <div class="card-header">
                    <span>历史数据查询</span>
                    <div class="header-info">
                        <el-tag type="info" size="small">历史数据查询不受设备在线状态影响</el-tag>
                        <el-tag v-if="$route.params.id" :type="getDeviceStatusType()" size="small">
                            当前设备：{{ getDeviceStatusText() }}
                        </el-tag>
                        <el-button type="primary" @click="resetQuery">重置</el-button>
                    </div>
                </div>
            </template>

            <el-form :model="queryForm" :rules="queryRules" ref="queryFormRef" label-width="100px" class="query-form">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="设备选择" prop="deviceId">
                            <el-select
                                v-model="queryForm.deviceId"
                                placeholder="请选择设备"
                                filterable
                                remote
                                :remote-method="searchDevices"
                                :loading="deviceLoading"
                                @change="handleDeviceChange"
                                style="width: 100%"
                            >
                                <el-option
                                    v-for="device in deviceOptions"
                                    :key="device.id"
                                    :label="`${device.name} (${device.code})`"
                                    :value="device.id"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                        <el-form-item label="分层选择" prop="layer">
                            <el-select
                                v-model="queryForm.layer"
                                placeholder="请选择分层"
                                :disabled="!selectedDevice || !selectedDevice.level"
                                style="width: 100%"
                            >
                                <el-option label="全部分层" :value="null" />
                                <el-option
                                    v-for="layer in layerOptions"
                                    :key="layer"
                                    :label="`第${chineseNumber(layer)}层`"
                                    :value="layer"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="时间范围" prop="timeRange">
                            <el-date-picker
                                v-model="queryForm.timeRange"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                                style="width: 100%"
                            />
                        </el-form-item>
                    </el-col>

                    <el-col :span="4">
                        <el-form-item>
                            <el-button type="primary" @click="queryData" :loading="queryLoading">
                                <el-icon><Search /></el-icon>
                                查询
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="数据类型" prop="dataTypes">
                            <el-checkbox-group v-model="queryForm.dataTypes">
                                <el-checkbox label="flow">流量 (m³/d)</el-checkbox>
                                <el-checkbox label="innerPressure">内压 (MPa)</el-checkbox>
                                <el-checkbox label="outerPressure">外压 (MPa)</el-checkbox>
                                <el-checkbox label="temperature">温度 (℃)</el-checkbox>
                                <el-checkbox label="valveOpening">水嘴开度 (%)</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                        <el-form-item label="数据间隔">
                            <el-select v-model="queryForm.interval" placeholder="请选择数据间隔">
                                <el-option label="1分钟" value="1m" />
                                <el-option label="5分钟" value="5m" />
                                <el-option label="10分钟" value="10m" />
                                <el-option label="30分钟" value="30m" />
                                <el-option label="1小时" value="1h" />
                                <el-option label="6小时" value="6h" />
                                <el-option label="1天" value="1d" />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                        <el-form-item>
                            <el-button @click="exportData" :disabled="!hasData" :loading="exportLoading">
                                <el-icon><Download /></el-icon>
                                导出数据
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-card>

        <!-- 数据展示 -->
        <el-card class="data-card" shadow="never" v-if="hasData">
            <template #header>
                <div class="card-header">
                    <span>数据展示</span>
                    <div class="header-actions">
                        <el-radio-group v-model="displayMode" size="small">
                            <el-radio-button label="chart">图表</el-radio-button>
                            <el-radio-button label="table">表格</el-radio-button>
                        </el-radio-group>
                    </div>
                </div>
            </template>

            <!-- 图表展示 -->
            <div v-if="displayMode === 'chart'" class="chart-container">
                <div ref="chartContainer" style="width: 100%; height: 500px;"></div>
            </div>

            <!-- 表格展示 -->
            <div v-if="displayMode === 'table'" class="table-container">
                <el-table
                    :data="tableData"
                    border
                    stripe
                    height="500"
                    v-loading="queryLoading"
                >
                    <el-table-column prop="time" label="时间" width="180" fixed="left" />
                    <el-table-column prop="layer" label="分层" width="80" v-if="queryForm.layer === null" />
                    <el-table-column
                        v-for="dataType in queryForm.dataTypes"
                        :key="dataType"
                        :prop="dataType"
                        :label="getDataTypeLabel(dataType)"
                        :formatter="(_, __, cellValue) => formatValue(cellValue, dataType)"
                    />
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.currentPage"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[20, 50, 100, 200]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </el-card>

        <!-- 无数据提示 -->
        <el-empty v-if="!hasData && !queryLoading" description="请选择查询条件并点击查询按钮" />
    </div>
</template>

<script>
import { Search, Download } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import * as echarts from 'echarts';
import API from '@/api';
import { formatTimestamp } from '@/utils/date';
import { chineseLayer } from './well/chartUtils';
import historyDataService from '@/services/historyDataService';
import logger from '@/utils/logger';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';
import deviceOnlineService from '@/services/deviceOnlineService';

export default {
    name: 'HistoryData',
    components: {
        Search,
        Download
    },
    setup() {
        // 获取设备在线状态 store
        const deviceOnlineStore = useDeviceOnlineStore();

        return {
            deviceOnlineStore
        };
    },
    props: {
        // 设备ID（从父组件传入）
        deviceId: {
            type: [String, Number],
            default: null
        },
        // 设备信息（从父组件传入）
        deviceInfo: {
            type: Object,
            default: null
        }
    },
    data() {
        return {
            // 查询表单
            queryForm: {
                deviceId: null,
                layer: null,
                timeRange: [],
                dataTypes: ['flow', 'innerPressure', 'outerPressure'],
                interval: '10m'
            },

            // 表单验证规则
            queryRules: {
                deviceId: [
                    { required: true, message: '请选择设备', trigger: 'change' }
                ],
                timeRange: [
                    { required: true, message: '请选择时间范围', trigger: 'change' }
                ],
                dataTypes: [
                    { required: true, message: '请至少选择一种数据类型', trigger: 'change' }
                ]
            },

            // 设备选项
            deviceOptions: [],
            deviceLoading: false,
            selectedDevice: null,

            // 查询状态
            queryLoading: false,
            exportLoading: false,

            // 数据展示
            displayMode: 'chart',
            chartData: [],
            tableData: [],
            chartInstance: null,

            // 分页
            pagination: {
                currentPage: 1,
                pageSize: 50,
                total: 0
            }
        };
    },
    computed: {
        // 分层选项
        layerOptions() {
            if (!this.selectedDevice || !this.selectedDevice.level) {
                return [];
            }
            return Array.from({ length: this.selectedDevice.level }, (_, i) => i + 1);
        },

        // 是否有数据
        hasData() {
            return this.chartData.length > 0 || this.tableData.length > 0;
        }
    },
    async mounted() {
        // 初始化设备列表
        this.searchDevices('');

        // 设置默认时间范围（最近24小时）
        const now = new Date();
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        this.queryForm.timeRange = [
            formatTimestamp(yesterday, 'YYYY-MM-DD HH:mm:ss'),
            formatTimestamp(now, 'YYYY-MM-DD HH:mm:ss')
        ];

        // 如果传入了设备信息，自动设置
        this.initializeFromProps();

        // 如果没有传入设备信息，尝试从路由参数获取
        if (!this.deviceId && this.$route.params.id) {
            await this.initializeFromRoute();
        }
    },
    beforeUnmount() {
        if (this.chartInstance) {
            this.chartInstance.dispose();
        }
    },
    watch: {
        // 监听设备信息变化
        deviceInfo: {
            handler() {
                this.initializeFromProps();
            },
            deep: true
        },

        // 监听设备ID变化
        deviceId() {
            this.initializeFromProps();
        },

        // 监听显示模式变化
        displayMode(newMode) {
            if (newMode === 'chart' && this.hasData) {
                this.$nextTick(() => {
                    this.renderChart();
                });
            }
        }
    },
    methods: {
        // 中文数字转换
        chineseNumber(num) {
            return chineseLayer(num);
        },

        // 从props初始化设备信息
        initializeFromProps() {
            if (this.deviceId && this.deviceInfo) {
                // 设置设备选择
                this.queryForm.deviceId = this.deviceId;

                // 设置设备选项（确保下拉框能正确显示）
                const deviceOption = {
                    id: this.deviceInfo.id,
                    name: this.deviceInfo.name,
                    code: this.deviceInfo.code || this.deviceInfo.id,
                    level: this.deviceInfo.level
                };

                // 如果设备选项中没有当前设备，添加进去
                const existingDevice = this.deviceOptions.find(device => device.id === this.deviceId);
                if (!existingDevice) {
                    this.deviceOptions.unshift(deviceOption);
                }

                // 设置选中的设备
                this.selectedDevice = deviceOption;

                // 重置分层选择
                this.queryForm.layer = null;

                logger.info('从props初始化设备信息:', {
                    deviceId: this.deviceId,
                    deviceName: this.deviceInfo.name,
                    level: this.deviceInfo.level
                });
            }
        },

        // 从路由参数初始化设备信息
        async initializeFromRoute() {
            const deviceId = this.$route.params.id;
            if (!deviceId) return;

            try {
                logger.info('从路由参数初始化设备信息:', deviceId);

                // 使用设备在线服务获取设备信息
                const deviceInfo = await deviceOnlineService.getDeviceInfo(deviceId, { silent: true });

                if (deviceInfo) {
                    // 设置设备选择
                    this.queryForm.deviceId = deviceId;

                    // 设置设备选项
                    const deviceOption = {
                        id: deviceInfo.id,
                        name: deviceInfo.name,
                        code: deviceInfo.code || deviceInfo.id,
                        level: deviceInfo.level,
                        online: deviceInfo.online
                    };

                    // 添加到设备选项中
                    const existingDevice = this.deviceOptions.find(device => device.id == deviceId);
                    if (!existingDevice) {
                        this.deviceOptions.unshift(deviceOption);
                    }

                    // 设置选中的设备
                    this.selectedDevice = deviceOption;

                    // 重置分层选择
                    this.queryForm.layer = null;

                    logger.info('从路由初始化设备信息成功:', {
                        deviceId: deviceId,
                        deviceName: deviceInfo.name,
                        level: deviceInfo.level,
                        online: deviceInfo.online
                    });
                } else {
                    logger.warn('获取设备信息失败');
                    // 即使获取设备信息失败，也设置基本的设备ID，让用户可以手动选择设备
                    this.queryForm.deviceId = deviceId;
                }
            } catch (error) {
                logger.error('从路由初始化设备信息失败:', error);
                // 设置基本的设备ID，让用户可以手动选择设备
                this.queryForm.deviceId = deviceId;
            }
        },

        // 搜索设备
        async searchDevices(keyword) {
            this.deviceLoading = true;
            try {
                const response = await API.tenant.device.list.get({
                    page: 1,
                    pageSize: 50,
                    keyword: keyword || ''
                });

                if (response && response.status === 1 && response.data) {
                    this.deviceOptions = response.data.rows || [];
                }
            } catch (error) {
                console.error('搜索设备失败:', error);
                ElMessage.error('搜索设备失败');
            } finally {
                this.deviceLoading = false;
            }
        },

        // 设备变更处理
        handleDeviceChange(deviceId) {
            this.selectedDevice = this.deviceOptions.find(device => device.id === deviceId);
            this.queryForm.layer = null; // 重置分层选择
        },

        // 查询数据
        async queryData() {
            // 表单验证
            const valid = await this.$refs.queryFormRef.validate().catch(() => false);
            if (!valid) return;

            // 使用服务验证参数
            const validation = historyDataService.validateQueryParams(this.queryForm);
            if (!validation.valid) {
                ElMessage.error(validation.message);
                return;
            }

            this.queryLoading = true;
            try {
                // 格式化查询参数
                const params = historyDataService.formatQueryParams({
                    ...this.queryForm,
                    page: this.pagination.currentPage,
                    pageSize: this.pagination.pageSize
                });

                // 调用服务查询数据
                const result = await historyDataService.queryHistoryData(params);

                if (result) {
                    this.chartData = result.chartData || [];
                    this.tableData = result.tableData || [];
                    this.pagination.total = result.total || 0;

                    // 更新图表
                    if (this.displayMode === 'chart') {
                        this.$nextTick(() => {
                            this.renderChart();
                        });
                    }

                    ElMessage.success(`查询成功，共找到 ${this.pagination.total} 条数据`);
                }
            } catch (error) {
                console.error('查询历史数据失败:', error);
                ElMessage.error('查询历史数据失败');
            } finally {
                this.queryLoading = false;
            }
        },

        // 重置查询
        resetQuery() {
            this.$refs.queryFormRef.resetFields();
            this.selectedDevice = null;
            this.chartData = [];
            this.tableData = [];
            this.pagination.currentPage = 1;
            this.pagination.total = 0;

            // 重新设置默认时间范围
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            this.queryForm.timeRange = [
                formatTimestamp(yesterday, 'YYYY-MM-DD HH:mm:ss'),
                formatTimestamp(now, 'YYYY-MM-DD HH:mm:ss')
            ];
        },

        // 导出数据
        async exportData() {
            if (!this.hasData) {
                ElMessage.warning('没有可导出的数据');
                return;
            }

            // 验证查询参数
            const validation = historyDataService.validateQueryParams(this.queryForm);
            if (!validation.valid) {
                ElMessage.error(validation.message);
                return;
            }

            try {
                await ElMessageBox.confirm('确定要导出当前查询条件下的所有数据吗？', '导出确认', {
                    type: 'warning'
                });

                this.exportLoading = true;

                // 格式化导出参数
                const params = historyDataService.formatQueryParams(this.queryForm);

                // 调用服务导出数据
                const blob = await historyDataService.exportHistoryData(params);

                if (blob) {
                    // 生成文件名
                    const fileName = historyDataService.generateFileName({
                        deviceName: this.selectedDevice?.name,
                        startTime: this.queryForm.timeRange[0],
                        endTime: this.queryForm.timeRange[1],
                        dataTypes: this.queryForm.dataTypes,
                        layer: this.queryForm.layer
                    });

                    // 下载文件
                    historyDataService.downloadFile(blob, fileName);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error('导出数据失败:', error);
                    ElMessage.error('导出数据失败');
                }
            } finally {
                this.exportLoading = false;
            }
        },

        // 渲染图表
        renderChart() {
            if (!this.chartData.length) return;

            const container = this.$refs.chartContainer;
            if (!container) return;

            // 销毁旧图表
            if (this.chartInstance) {
                this.chartInstance.dispose();
            }

            // 创建新图表
            this.chartInstance = echarts.init(container);

            const option = this.buildChartOption();
            this.chartInstance.setOption(option);

            // 监听窗口大小变化
            window.addEventListener('resize', () => {
                if (this.chartInstance) {
                    this.chartInstance.resize();
                }
            });
        },

        // 构建图表配置
        buildChartOption() {
            const times = this.chartData.map(item => item.time);
            const series = [];
            const yAxes = [];

            // 获取数据类型配置
            const dataTypeConfig = historyDataService.getDataTypeConfig();

            // 构建系列数据
            this.queryForm.dataTypes.forEach((dataType, index) => {
                const config = dataTypeConfig[dataType];
                if (!config) return;

                const data = this.chartData.map(item => item[dataType]);

                series.push({
                    name: config.label,
                    type: 'line',
                    data: data,
                    smooth: true,
                    symbol: 'none',
                    lineStyle: {
                        color: config.color,
                        width: 2
                    },
                    yAxisIndex: index
                });

                // 添加Y轴
                yAxes.push({
                    type: 'value',
                    name: config.unit,
                    position: index % 2 === 0 ? 'left' : 'right',
                    axisLabel: {
                        formatter: `{value} ${config.unit}`
                    }
                });
            });

            return {
                title: {
                    text: `${this.selectedDevice?.name || '设备'} 历史数据`,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: series.map(s => s.name),
                    top: 30
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '15%'
                },
                xAxis: {
                    type: 'category',
                    data: times,
                    axisLabel: {
                        formatter: function(value) {
                            return value.split(' ')[1]; // 只显示时间部分
                        }
                    }
                },
                yAxis: yAxes,
                series: series,
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    },
                    {
                        start: 0,
                        end: 100,
                        height: 30
                    }
                ]
            };
        },

        // 获取数据类型标签
        getDataTypeLabel(dataType) {
            const config = historyDataService.getDataTypeConfig()[dataType];
            return config ? `${config.label} (${config.unit})` : dataType;
        },

        // 格式化数值
        formatValue(value, dataType) {
            if (value === null || value === undefined) return '-';

            const config = historyDataService.getDataTypeConfig()[dataType];
            const decimals = config ? config.decimals : 2;

            return Number(value).toFixed(decimals);
        },

        // 分页处理
        handleSizeChange(size) {
            this.pagination.pageSize = size;
            this.pagination.currentPage = 1;
            this.queryData();
        },

        handleCurrentChange(page) {
            this.pagination.currentPage = page;
            this.queryData();
        },

        // 获取设备状态类型（用于标签颜色）
        getDeviceStatusType() {
            const deviceId = this.deviceId || this.$route.params.id;
            if (!deviceId) return 'info';

            // 从 Pinia store 获取实时在线状态
            const isOnline = this.deviceOnlineStore.isDeviceOnline(deviceId);
            return isOnline ? 'success' : 'warning';
        },

        // 获取设备状态文本
        getDeviceStatusText() {
            const deviceId = this.deviceId || this.$route.params.id;
            if (!deviceId) return '未知';

            // 从 Pinia store 获取实时在线状态
            const isOnline = this.deviceOnlineStore.isDeviceOnline(deviceId);
            return isOnline ? '在线' : '离线';
        }
    }
};
</script>

<style lang="scss" scoped>
.history-data-container {
    padding: 20px;

    .query-card {
        margin-bottom: 20px;

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .header-info {
                display: flex;
                align-items: center;
                gap: 10px;
            }
        }

        .query-form {
            .el-form-item {
                margin-bottom: 16px;
            }
        }
    }

    .data-card {
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chart-container {
            padding: 10px 0;
        }

        .table-container {
            .pagination-container {
                margin-top: 20px;
                text-align: right;
            }
        }
    }
}
</style>
