<template>
    <el-main>
        <div v-if="pageLoading">
            <el-card shadow="never">
                <el-skeleton :rows="1"></el-skeleton>
            </el-card>
            <el-card shadow="never" style="margin-top: 15px">
                <el-skeleton :rows="1"></el-skeleton>
            </el-card>
            <el-card shadow="never" style="margin-top: 15px">
                <el-skeleton></el-skeleton>
            </el-card>
        </div>
        <!--欢迎语组件-->
        <welcome @on-mounted="onMounted" :userInfoObj="userInfo"></welcome>
        <!--自定义应用-->
        <work @on-mounted="onMounted"></work>
    </el-main>
</template>

<script>
import { defineAsyncComponent } from 'vue';
import account from '@/utils/account';
const welcome = defineAsyncComponent(() => import('./widgets/welcome'));
const work = defineAsyncComponent(() => import('./work'));

export default {
    name: 'system.dashboard',
    components: {
        welcome,
        work
    },
    data() {
        return {
            userInfo: {},
            pageLoading: true,
            dashboard: 0
        };
    },
    created() {
        // 从缓存中取出用户信息
        this.userInfo = account.userInfo.get() || {};
        this.dashboard = this.userInfo.dashboard || 0;
    },
    mounted() {},
    methods: {
        onMounted() {
            this.pageLoading = false;
        }
    }
};
</script>

<style lang="scss" scoped></style>
