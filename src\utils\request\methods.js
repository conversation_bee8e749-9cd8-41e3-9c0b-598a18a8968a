import service from './service';
import { useGlobalStore } from '@/stores/global';

export function createRequest(method) {
    return (url, data = {}, config = {}) => {
        return new Promise((resolve, reject) => {
            const globalStore = useGlobalStore();
            const finalUrl = config.external ? url : globalStore.apiDomain + url;
            const requestConfig = {
                method,
                url: finalUrl,
                ...(method === 'get' ? { params: data } : { data }),
                ...config
            };

            service(requestConfig)
                .then(response => resolve(response.data))
                .catch(error => reject(error));
        });
    };
}

export function createJsonp(url, name = 'jsonp') {
    return new Promise(resolve => {
        const script = document.createElement('script');
        const _id = `jsonp${Math.ceil(Math.random() * 1000000)}`;
        script.id = _id;
        script.type = 'text/javascript';
        script.src = url;

        window[name] = response => {
            resolve(response);
            document.getElementsByTagName('head')[0].removeChild(script);
            try {
                delete window[name];
            } catch (e) {
                window[name] = undefined;
            }
        };

        document.getElementsByTagName('head')[0].appendChild(script);
    });
}
