<template>
    <el-container class="main">
        <el-header>
            <mainHeader :active-tab="activeTab" @tab-change="handleTabChange" />
        </el-header>
        <el-container>
            <!-- 数据监控 -->
            <router-view v-if="activeTab === 'monitor'" @device-offline="handleDeviceOffline" @device-online="handleDeviceOnline" />

            <!-- 历史数据 -->
            <HistoryData v-if="activeTab === 'history'" :device-id="currentDeviceId" :device-info="currentDeviceInfo" />
        </el-container>
        <el-footer>
            <mainFooter />
        </el-footer>

        <!-- 统一的设备在线状态监控组件 -->
        <DeviceOnlineMonitor
            v-if="currentDeviceId"
            ref="deviceMonitor"
            :device-id="currentDeviceId"
            :device-name="currentDeviceName"
            :initial-online="currentDeviceOnline"
            @offline="handleDeviceOffline"
            @online="handleDeviceOnline"
        />
    </el-container>
</template>

<script>
import mainHeader from './components/mainHeader.vue'
import mainFooter from './components/mainFooter.vue'
import DeviceOnlineMonitor from './components/DeviceOnlineMonitor.vue'
import HistoryData from './components/HistoryData.vue'
import { ElNotification } from 'element-plus';
import deviceOnlineService from '@/services/deviceOnlineService';
import logger from '@/utils/logger';

export default {
    name: 'tenant.handle.device',
    components: {
        mainHeader,
        mainFooter,
        DeviceOnlineMonitor,
        HistoryData
    },
    data() {
        return {
            currentDeviceId: null,
            currentDeviceName: '',
            currentDeviceOnline: null,
            currentDeviceInfo: null,
            activeTab: 'monitor'
        };
    },
    watch: {
        // 监听路由变化，获取当前设备信息
        '$route': {
            handler(newRoute) {
                this.updateCurrentDevice(newRoute);
            },
            immediate: true
        }
    },
    async mounted() {
        // 初始化当前设备信息
        await this.updateCurrentDevice(this.$route);
    },
    methods: {
        /**
         * 更新当前设备信息
         * @param {Object} route 路由对象
         */
        async updateCurrentDevice(route) {
            // 检查路由是否包含设备ID
            const deviceId = route.params?.id;

            if (!deviceId) {
                // 如果没有设备ID，清空当前设备信息
                this.currentDeviceId = null;
                this.currentDeviceName = '';
                this.currentDeviceOnline = null;
                return;
            }

            // 如果设备ID没有变化，不需要重新获取
            if (this.currentDeviceId === deviceId) {
                return;
            }

            try {
                logger.group(`获取设备 ${deviceId} 信息`);
                logger.info(`路由: ${route.path}`);

                // 获取设备信息
                const deviceInfo = await deviceOnlineService.getDeviceInfo(deviceId, {
                    layer: route.params?.layer,
                    silent: true // 静默模式，不显示错误提示
                });

                if (deviceInfo) {
                    this.currentDeviceId = deviceId;
                    this.currentDeviceName = deviceInfo.name || `设备${deviceId}`;
                    this.currentDeviceOnline = deviceInfo.online;
                    this.currentDeviceInfo = deviceInfo; // 保存完整的设备信息

                    logger.info(`设备信息更新:`, {
                        id: this.currentDeviceId,
                        name: this.currentDeviceName,
                        online: this.currentDeviceOnline
                    });
                } else {
                    // 如果获取设备信息失败，仍然设置基本信息以便监控组件工作
                    this.currentDeviceId = deviceId;
                    this.currentDeviceName = `设备${deviceId}`;
                    this.currentDeviceOnline = null;
                    this.currentDeviceInfo = null;

                    logger.warn(`无法获取设备 ${deviceId} 信息，使用默认值`);
                }

                logger.groupEnd();
            } catch (error) {
                logger.error(`获取设备 ${deviceId} 信息失败:`, error);

                // 即使出错，也设置基本信息
                this.currentDeviceId = deviceId;
                this.currentDeviceName = `设备${deviceId}`;
                this.currentDeviceOnline = null;
            }
        },

        /**
         * 处理设备离线事件
         * @param {Object} data 设备离线数据
         */
        handleDeviceOffline(data) {
            console.log('设备离线事件:', data);

            // 显示设备离线通知
            ElNotification({
                title: '设备离线',
                message: `设备 ${data.deviceName || data.deviceId} 已离线，请注意！`,
                type: 'warning',
                duration: 0 // 不自动关闭
            });
        },

        /**
         * 处理设备上线事件
         * @param {Object} data 设备上线数据
         */
        handleDeviceOnline(data) {
            console.log('设备上线事件:', data);

            // 显示设备上线通知
            ElNotification({
                title: '设备上线',
                message: `设备 ${data.deviceName || data.deviceId} 已重新上线！`,
                type: 'success',
                duration: 5000
            });
        },

        /**
         * 处理标签页切换
         * @param {string} tab 标签页名称
         */
        handleTabChange(tab) {
            this.activeTab = tab;
            logger.info(`切换到${tab === 'monitor' ? '数据监控' : '历史数据'}模式`);
        }
    }
};
</script>

<style lang="scss" scoped>
.main {
    // header高度
    $headerHeight: 70px;
    // footer高度
    $footerHeight: 30px;

    .el-header {
        color: #eeeeee;
        padding: 0;
        background-color: #662f8f;
        border-color: #662f8f;
        height: $headerHeight;
    }

    .el-container {
        height: calc(100vh - $headerHeight - $footerHeight);
    }

    .el-footer {
        border-color: #e8e8e8;
        background-color: #f5f5f5;
        font-size: 12px;
        height: $footerHeight;
        line-height: $footerHeight - 1px;
        padding: 0 10px;
    }
}

html[data-theme='dark'] {
    .el-header {
        background-color: var(--color-primary);
        border-color: var(--el-border-color);
    }
    .el-footer {
        border-color: var(--el-border-color);
        background-color: var(--color-background);
    }
}
</style>
