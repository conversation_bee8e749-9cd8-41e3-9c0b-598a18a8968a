<template>
    <el-container class="main">
        <el-header>
            <mainHeader />
        </el-header>
        <el-container>
            <router-view @device-offline="handleDeviceOffline" @device-online="handleDeviceOnline" />
        </el-container>
        <el-footer>
            <mainFooter />
        </el-footer>
    </el-container>
</template>

<script>
import mainHeader from './components/mainHeader.vue'
import mainFooter from './components/mainFooter.vue'
import { ElNotification } from 'element-plus';

export default {
    name: 'tenant.handle.device',
    components: {
        mainHeader,
        mainFooter
    },
    methods: {
        /**
         * 处理设备离线事件
         * @param {Object} data 设备离线数据
         */
        handleDeviceOffline(data) {
            console.log('设备离线事件:', data);

            // 显示设备离线通知
            ElNotification({
                title: '设备离线',
                message: `设备 ${data.deviceName || data.deviceId} 已离线，请注意！`,
                type: 'warning',
                duration: 0 // 不自动关闭
            });
        },

        /**
         * 处理设备上线事件
         * @param {Object} data 设备上线数据
         */
        handleDeviceOnline(data) {
            console.log('设备上线事件:', data);

            // 显示设备上线通知
            ElNotification({
                title: '设备上线',
                message: `设备 ${data.deviceName || data.deviceId} 已重新上线！`,
                type: 'success',
                duration: 5000
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.main {
    // header高度
    $headerHeight: 70px;
    // footer高度
    $footerHeight: 30px;

    .el-header {
        color: #eeeeee;
        padding: 0;
        background-color: #662f8f;
        border-color: #662f8f;
        height: $headerHeight;
    }

    .el-container {
        height: calc(100vh - $headerHeight - $footerHeight);
    }

    .el-footer {
        border-color: #e8e8e8;
        background-color: #f5f5f5;
        font-size: 12px;
        height: $footerHeight;
        line-height: $footerHeight - 1px;
        padding: 0 10px;
    }
}

html[data-theme='dark'] {
    .el-header {
        background-color: var(--color-primary);
        border-color: var(--el-border-color);
    }
    .el-footer {
        border-color: var(--el-border-color);
        background-color: var(--color-background);
    }
}
</style>
