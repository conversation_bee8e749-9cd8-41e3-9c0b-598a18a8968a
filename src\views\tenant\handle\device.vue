<template>
    <el-container class="main">
        <el-header>
            <mainHeader :active-tab="activeTab" :device-id="currentDeviceId" @tab-change="handleTabChange" />
        </el-header>
        <el-container>
            <router-view @device-offline="handleDeviceOffline" @device-online="handleDeviceOnline" />
        </el-container>
        <el-footer>
            <mainFooter />
        </el-footer>

        <!-- 统一的设备在线状态监控组件 -->
        <DeviceOnlineMonitor
            v-if="currentDeviceId && activeTab !== 'history'"
            ref="deviceMonitor"
            :device-id="currentDeviceId"
            :device-name="currentDeviceName"
            :initial-online="currentDeviceOnline"
            @offline="handleDeviceOffline"
            @online="handleDeviceOnline"
        />
    </el-container>
</template>

<script>
import mainHeader from './components/mainHeader.vue'
import mainFooter from './components/mainFooter.vue'
import DeviceOnlineMonitor from './components/DeviceOnlineMonitor.vue'
import { ElNotification } from 'element-plus';
import deviceOnlineService from '@/services/deviceOnlineService';
import logger from '@/utils/logger';

export default {
    name: 'tenant.handle.device',
    components: {
        mainHeader,
        mainFooter,
        DeviceOnlineMonitor
    },
    data() {
        return {
            currentDeviceId: null,
            currentDeviceName: '',
            currentDeviceOnline: null,
            currentDeviceInfo: null,
            // 防重复请求
            deviceInfoPromise: null
        };
    },
    computed: {
        // 根据当前路由确定激活的标签页
        activeTab() {
            const path = this.$route.path;
            if (path.includes('/history')) {
                return 'history';
            }
            return 'monitor';
        }
    },
    watch: {
        // 监听路由变化，获取当前设备信息
        '$route': {
            handler(newRoute) {
                this.updateCurrentDevice(newRoute);
            },
            immediate: true
        }
    },
    async mounted() {
        // 初始化当前设备信息
        await this.updateCurrentDevice(this.$route);

        console.log('设备监控组件已挂载::::', this.activeTab);
    },
    methods: {
        /**
         * 更新当前设备信息
         * @param {Object} route 路由对象
         */
        async updateCurrentDevice(route) {
            const deviceId = route.params?.id;

            if (!deviceId) {
                this.resetDeviceInfo();
                return;
            }

            // 如果设备ID没有变化，不需要重新获取
            if (this.currentDeviceId === deviceId) {
                return;
            }

            // 防重复请求
            if (this.deviceInfoPromise && this.deviceInfoPromise.deviceId === deviceId) {
                logger.info(`设备 ${deviceId} 信息请求已在进行中，等待完成`);
                return this.deviceInfoPromise.promise;
            }

            // 直接使用设备在线服务获取设备信息
            const promise = deviceOnlineService.getDeviceInfo(deviceId, {
                layer: route.params?.layer,
                silent: true
            });

            this.deviceInfoPromise = { deviceId, promise };

            try {
                const deviceInfo = await promise;
                if (deviceInfo) {
                    this.setDeviceInfo(deviceId, deviceInfo);
                } else {
                    this.setBasicDeviceInfo(deviceId);
                }
            } finally {
                if (this.deviceInfoPromise?.deviceId === deviceId) {
                    this.deviceInfoPromise = null;
                }
            }
        },

        /**
         * 设置设备信息
         */
        setDeviceInfo(deviceId, deviceInfo) {
            this.currentDeviceId = deviceId;
            this.currentDeviceName = deviceInfo.name || `设备${deviceId}`;
            this.currentDeviceOnline = deviceInfo.online;
            this.currentDeviceInfo = deviceInfo;

            // 同步设备状态到 Pinia store
            deviceOnlineService.initDeviceStatus(deviceId, deviceInfo.online);

            logger.info('设备信息更新成功:', {
                id: this.currentDeviceId,
                name: this.currentDeviceName,
                online: this.currentDeviceOnline
            });
        },

        /**
         * 设置基本设备信息（获取失败时的兜底）
         */
        setBasicDeviceInfo(deviceId) {
            this.currentDeviceId = deviceId;
            this.currentDeviceName = `设备${deviceId}`;
            this.currentDeviceOnline = null;
            this.currentDeviceInfo = null;
            logger.warn(`设备 ${deviceId} 信息获取失败，使用基本信息`);
        },

        /**
         * 重置设备信息
         */
        resetDeviceInfo() {
            this.currentDeviceId = null;
            this.currentDeviceName = '';
            this.currentDeviceOnline = null;
            this.currentDeviceInfo = null;
            this.deviceInfoPromise = null;
        },

        /**
         * 处理设备离线事件
         * @param {Object} data 设备离线数据
         */
        handleDeviceOffline(data) {
            console.log('设备离线事件:', data);

            // 如果当前在历史数据页面，不显示离线通知
            if (this.activeTab === 'history') {
                logger.info('当前在历史数据页面，跳过设备离线通知');
                return;
            }

            // 显示设备离线通知
            ElNotification({
                title: '设备离线',
                message: `设备 ${data.deviceName || data.deviceId} 已离线，请注意！`,
                type: 'warning',
                duration: 0 // 不自动关闭
            });
        },

        /**
         * 处理设备上线事件
         * @param {Object} data 设备上线数据
         */
        handleDeviceOnline(data) {
            console.log('设备上线事件:', data);

            // 如果当前在历史数据页面，不显示上线通知（历史数据不依赖在线状态）
            if (this.activeTab === 'history') {
                logger.info('当前在历史数据页面，跳过设备上线通知');
                return;
            }

            // 显示设备上线通知
            ElNotification({
                title: '设备上线',
                message: `设备 ${data.deviceName || data.deviceId} 已重新上线！`,
                type: 'success',
                duration: 5000
            });
        },

        /**
         * 处理标签页切换
         * @param {string} tab 标签页名称
         */
        async handleTabChange(tab) {
            const deviceId = this.currentDeviceId || this.$route.params.id;
            if (!deviceId) {
                logger.warn('无法切换标签页：设备ID不存在');
                return;
            }

            // 如果从历史数据页面切换到监控页面，强制刷新设备状态
            if (this.activeTab === 'history' && tab === 'monitor') {
                logger.info('从历史数据页面切换到监控页面，强制刷新设备状态');
                try {
                    // 强制刷新设备信息，确保获取最新状态
                    const deviceInfo = await deviceOnlineService.getDeviceInfo(deviceId, {
                        forceRefresh: true,
                        silent: true
                    });

                    if (deviceInfo) {
                        this.currentDeviceInfo = deviceInfo;
                        this.currentDeviceOnline = deviceInfo.online;

                        // 同步设备状态到 Pinia store
                        deviceOnlineService.initDeviceStatus(deviceId, deviceInfo.online);

                        logger.info('设备状态已更新:', { online: deviceInfo.online });
                    }
                } catch (error) {
                    logger.error('刷新设备状态失败:', error);
                }
            }

            let targetPath;
            if (tab === 'history') {
                targetPath = `/tenant/handle/device/${deviceId}/history`;
            } else {
                targetPath = `/tenant/handle/device/${deviceId}`;
            }

            // 如果当前路径与目标路径不同，则进行跳转
            if (this.$route.path !== targetPath) {
                this.$router.push(targetPath);
                logger.info(`切换到${tab === 'monitor' ? '数据监控' : '历史数据'}模式: ${targetPath}`);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.main {
    // header高度
    $headerHeight: 70px;
    // footer高度
    $footerHeight: 30px;

    .el-header {
        color: #eeeeee;
        padding: 0;
        background-color: #662f8f;
        border-color: #662f8f;
        height: $headerHeight;
    }

    .el-container {
        height: calc(100vh - $headerHeight - $footerHeight);
    }

    .el-footer {
        border-color: #e8e8e8;
        background-color: #f5f5f5;
        font-size: 12px;
        height: $footerHeight;
        line-height: $footerHeight - 1px;
        padding: 0 10px;
    }
}

html[data-theme='dark'] {
    .el-header {
        background-color: var(--color-primary);
        border-color: var(--el-border-color);
    }
    .el-footer {
        border-color: var(--el-border-color);
        background-color: var(--color-background);
    }
}
</style>
