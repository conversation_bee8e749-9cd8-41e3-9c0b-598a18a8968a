@charset "utf-8";

// element样式重置
// <AUTHOR> <<PERSON>yu<PERSON>@live.com>
// @Datetime 2024-12-13

// 变量定义
$login-text-secondary: rgba(255, 255, 255, 0.6);
$login-mask-color: rgba(0, 0, 0, 0.65);
$login-padding: 40px;

// 混合器
@mixin background-cover {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}

@mixin full-fixed {
    min-width: 100%;
    min-height: 100%;
    position: fixed;
}

.login_body {
    position: relative;
    width: 900px;
    height: 600px;
    display: flex;
    margin: 0 auto;
    z-index: 2;
    top: 150px;

    &.dialog_mode {
        width: inherit;
        height: inherit;
        top: inherit;
        z-index: inherit;
    }
}

.login_bg {
    @include background-cover;
    @include full-fixed;
    background-image: url('@/assets/images/login_bg.jpg');
    z-index: 0;
}

.login_bg_filter {
    @include background-cover;
    @include full-fixed;
    background-color: $login-mask-color;
    z-index: 1;
}

.login_left {
    width: 46%;
    position: relative;
    background-color: rgba(73, 83, 93, 0.5);

    &::before,
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
    }

    &::before {
        background: url('@/assets/images/content-wrap-1.png') no-repeat 0 0;
    }

    &::after {
        background: url('@/assets/images/content-wrap-2.png') no-repeat right bottom;
    }

    .profile-body {
        color: #fff;
        left: 0;
        padding: $login-padding;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 2;

        h4 {
            font-size: 18px;
            font-weight: normal;
            margin-top: 30px;
        }

        p {
            color: $login-text-secondary;
            font-size: 14px;
            line-height: 1.8;
            margin-top: 20px;
        }

        i.logo {
            position: relative;
            left: 30px;
            display: block;
            width: 64px;
            height: 64px;
            background: url('@/assets/images/icon-morning.svg') no-repeat 0 0;
            background-size: cover;
        }

        .profile-main {
            margin-top: 30px;

            label {
                color: #888888;
                display: block;
                font-size: 14px;
                line-height: 2;
            }
        }

        .sentences-main {
            padding: 0 0 0 0;
            margin-top: 60px;
            color: #999999;

            .--main {
                display: block;
                font-size: 14px;
                line-height: 2;
            }

            .--foot {
                display: block;
                margin-top: 10px;
                text-align: right;
            }
        }
    }

    // {.login_left__bottom}
    &__bottom {
        position: absolute;
        right: 0;
        bottom: 0;
        left: 0;
        background-image: linear-gradient(transparent, #000000);
        color: #666666;
        padding: 40px;
        z-index: 3;
    }

    // {.login_left__mask}
    &__mask {
        background: rgba(0, 0, 0, 0.5);
        bottom: 0;
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
    }
}

.login_main {
    background-color: rgba(255, 255, 255, 0.95);
    display: flex;
    flex: 1;
    position: relative;
    overflow: auto;

    &::before,
    &::after {
        content: '';
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-size: 90%;
        z-index: 0;
    }

    &::before {
        background-image: url('@/assets/images/login_main_top_bg.webp');
        background-position: center top;
    }

    &::after {
        background-image: url('@/assets/images/login_main_bottom_bg.webp');
        background-position: center bottom;
        filter: opacity(0.15);
    }

    .login-type-switch {
        position: absolute;
        top: 0;
        right: 0;
        width: 64px;
        height: 60px;
        border-top-right-radius: 2px;
        display: block;
        overflow: hidden;
        z-index: 1;

        &::before {
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            content: '';
            border-top: 60px solid #444444;
            border-left: 64px solid transparent;
            z-index: -1;
        }

        .tab-item {
            box-sizing: border-box;
            width: 64px;
            height: 60px;
            padding: 10px;
            text-align: right;
            display: none;

            &.active {
                display: block;
            }

            .login-icon {
                width: 40px;
                height: 40px;
                margin: 0;
                padding: 0;
                cursor: pointer;
                background-repeat: no-repeat;
                background-size: contain;
                display: inline-block;

                &.login-icon-qrcode {
                    background-image: url('@/assets/images/icon-qrcode.svg');
                }

                &.login-icon-account {
                    background-image: url('@/assets/images/icon-account.svg');
                }
            }
        }
    }

    .login-form {
        --form-spacing: 15px;
        margin: 50px auto 0;
        padding: var(--form-spacing) 0;
        width: 86%;
        z-index: 1;

        .login-form-head {
            text-align: center;
            margin: 20px 0;
            line-height: 1.5;

            legend {
                display: block;
                width: 100%;
                padding: 0;
                margin-bottom: 0.5rem;
                font-size: 1.5rem;
                line-height: inherit;
                color: inherit;
                white-space: normal;
                border: none;
            }

            .heading-name {
                margin: 0;
                border: none;
                color: #4f326e;
                font-weight: 600;

                legend {
                    font-size: 24px;
                }
            }

            .heading-sub {
                min-width: 0;
                padding: 0;
                margin: 0;
                border-width: 1px 0 0;
                border-style: solid;
                border-color: rgba(0, 0, 0, 0.15);

                legend {
                    width: inherit;
                    font-size: 13.5px;
                    padding: 0 15px;
                    color: rgba(106, 67, 142, 0.5);
                }
            }
        }

        .el-divider {
            margin-top: 15px;

            .el-divider__text {
                font-weight: normal;
                padding: 0 20px;
                background: transparent !important;
            }
        }

        .el-tabs {
            .el-tabs__header {
                margin-bottom: 25px;

                .el-tabs__item {
                    font-size: 14px;
                }
            }
        }

        .login-forgot {
            text-align: right;

            a {
                color: var(--el-color-primary);

                &:hover {
                    color: var(--el-color-primary-light-3);
                }
            }
        }

        .login-reg {
            color: var(--el-text-color-primary);
            font-size: 14px;

            a {
                color: var(--el-color-primary);

                &:hover {
                    color: var(--el-color-primary-light-3);
                }
            }
        }

        .login-msg-yzm {
            display: flex;
            width: 100%;

            .el-button {
                --el-button-size: 42px;
                margin-left: 10px;
            }
        }
    }

    .login-oauth,
    .login-other {
        display: flex;
        justify-content: space-around;
    }
}

// 对话框模式
.login_dialog {
    .el-dialog__body {
        padding: 0 0 10px 0;
    }
}

.dialog_mode {
    .login-form {
        margin: 0 auto;
        padding: 5px 0;
        width: 94%;
    }
}

.qrCodeLogin {
    padding: 20px 0;
    position: relative;
    text-align: center;

    img.qrCode {
        background: #ffffff;
        border-radius: 10px;
        padding: 20px;
    }

    p.msg {
        margin-top: 15px;
    }

    .qrCodeLogin-result {
        background: var(--el-mask-color);
        bottom: 0;
        left: 0;
        position: absolute;
        right: 0;
        text-align: center;
        top: 0;
    }
}

html[data-theme='dark'] {
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
        -webkit-text-fill-color: #9dc8f6 !important; /* 设置文本颜色 */
    }

    .login_main {
        &::before {
            filter: opacity(0.15);
        }
        background-color: transparent;
        .login-form .el-tabs .el-tabs__header .el-tabs__item {
            color: #fff !important;
        }
    }
}
