<template>
    <span v-html="greeting"></span>
</template>

<script>
/**
 * 时间段问候语提示组件
 */
export default {
    name: 'greeting',
    data() {
        return {
            timer: null,
            interval: 60, // 刷新频率。单位：秒
            greeting: ''
        };
    },
    mounted() {
        // 当前时间
        this.timer = new Date().getTime();

        // 获取问候语
        this.dailyGreeting();

        // 每隔N秒获取一次
        this.timer = setInterval(() => {
            // 获取数据
            this.dailyGreeting();
        }, this.interval * 1000);

        this.$emit('on-mounted');
    },
    unmounted() {
        clearInterval(this.timer);
    },
    methods: {
        /**
         * 时间段转换
         * @returns {string}
         */
        timeFix() {
            const hour = new Date().getHours();
            return hour < 9
                ? '早上好'
                : hour <= 11
                  ? '上午好'
                  : hour <= 13
                    ? '中午好'
                    : hour < 20
                      ? '下午好'
                      : '晚上好';
        },
        /**
         * 时间段问候语提示
         * @param time
         * @returns {string}
         */
        dailyGreeting(time) {
            let time_format = '';
            if (time === null || time === undefined) {
                time = new Date();
            }
            const hr = time.getHours();
            if (hr >= 0 && hr <= 4) {
                time_format = '深夜了，注意身体哦！';
            }
            if (hr >= 4 && hr < 7) {
                time_format = '新的一天，清晨好~';
            }
            if (hr >= 7 && hr < 10) {
                time_format = '早安🌞，祝你开心每一天！';
            }
            if (hr >= 10 && hr < 12) {
                time_format = '上午好，起来活动活动，喝杯水~';
            }
            if (hr >= 12 && hr <= 13) {
                time_format = '午饭时间到了！准备吃什么呢？呃，这好像是个人生的大哲学问题~';
            }
            if (hr >= 13 && hr <= 15) {
                time_format = '登高莫问顶，途中耳目新。下午好！';
            }
            if (hr >= 15 && hr <= 17) {
                time_format = '别总坐着，站起来伸伸懒腰，走动走动~';
            }
            if (hr >= 17 && hr <= 18) {
                time_format = '进入傍晚了，你那儿天黑了吗~';
            }
            if (hr >= 18 && hr <= 20) {
                time_format = '吃过晚饭了吗？';
            }
            if (hr >= 20 && hr <= 24) {
                time_format = '在加班吗？辛苦啦~';
            }

            this.greeting = time_format;
        }
    }
};
</script>

<style scoped lang="scss">
.welcome-tip {
    margin: 0 0 10px 0;
}

.welcome-tip-wrapper {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 0.25rem;
    color: #155724;
    display: table;
    line-height: 2;
    margin-bottom: 20px;
    padding: 0.75rem 1.25rem;
    position: relative;

    :deep(.el-alert__title) {
        display: table;
        line-height: inherit;
        width: 100%;
    }

    .smile-icon {
        display: table-cell;
        font-size: 70px;
        opacity: 0.6;

        svg {
            position: relative;
            top: 6px;
        }
    }

    .welcome-tip--content {
        display: table-cell;
        float: none;
        margin: 0;
        min-height: 0;
        padding-left: 15px;
        vertical-align: middle;

        h4 {
            font-size: 16px;
            font-weight: 600;
            line-height: 1.2;
            margin: 10px 0;
        }

        span {
            font-size: 14px;
            line-height: 1.2;
        }

        hr {
            border: 0;
            border-top: 1px solid #b1dfbb;
            box-sizing: content-box;
            height: 0;
            margin: 10px 0;
            overflow: visible;
            width: 100%;
        }
    }
}
</style>
