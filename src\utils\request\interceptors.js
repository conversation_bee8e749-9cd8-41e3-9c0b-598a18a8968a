import { ElNotification, ElMessageBox } from 'element-plus';
import router from '@/router';
import { useGlobalStore } from '@/stores/global';
import tool from '@/utils/tool';
import fingerprint from '@/utils/fingerprint';
import account from '@/utils/account';
import { requestQueue } from './queue';
import { requestTracker } from './requestTracker';
import sysConfig from '@/config';
import storageConfig from '@/config/storage';

// 状态控制
const state = {
    MessageBox_401_show: false,
    MessageBox_Blocked_show: false,
    MessageBox_KickOuted_show: false
};

// 响应拦截器
export async function responseInterceptor(response, service) {
    // 如果开启了请求追踪回传开关
    if (sysConfig.REQUEST_BACK_TRACKER) {
        const allHeaders = response.headers;
        const requestId = allHeaders.get ? allHeaders.get('x-request-id') : allHeaders['x-request-id'];
        const sequenceNum = allHeaders.get ? allHeaders.get('x-sequence-num') : allHeaders['x-sequence-num'];

        if (requestId) {
            requestTracker.setRequestId(requestId, response.config.url);
            console.log(`[Response][${requestId}] ${response.status} ${response.config.url}`);
        }

        if (sequenceNum) {
            requestTracker.setSequenceNum(sequenceNum);
            console.log(`[Response][${sequenceNum}] ${response.status} ${response.config.url}`);
        }
    }

    if (response.config.external === true) {
        return response;
    }

    const globalStore = useGlobalStore();

    // 处理强制踢出
    if (response.data?.label === 'kickOuted' && !state.MessageBox_KickOuted_show) {
        return handleKickOut(response);
    }

    // 处理设备禁用
    if (response.data?.label === 'blocked' && !state.MessageBox_Blocked_show) {
        return handleBlocked(response);
    }

    // 处理强制修改密码
    if (response.data?.label === 'force_change_password') {
        // 设置用户修改密码状态
        globalStore.SET_userResetPassword(true);
    }

    // 处理换取新token
    if (response.data?.label === 'refreshToken') {
        globalStore.SET_userTokenExpired(true);
    }

    // 处理重新登录
    if (response.data?.label === 'needReLogin') {
        globalStore.SET_userLoginDialogVisible(true);
    }

    // 处理错误码
    const err_code = parseInt(response.data.code);
    handleErrorCode(err_code, globalStore);

    return response;
}

// 请求拦截器
export async function requestInterceptor(config) {
    if (config.external === true) {
        return config;
    }

    // 添加token
    let accessToken = tool.cookie.get(storageConfig.vars.accessToken);
    if (accessToken) {
        config.headers[sysConfig.TOKEN_NAME] = sysConfig.TOKEN_PREFIX + accessToken;
    }

    // 单独处理用户登录时的指纹获取
    if (config.url.includes('/login') || !config.headers['X-Finger-Id']) {
        try {
            const fingerId = await fingerprint.getStableFingerprint();
            if (fingerId) {
                config.headers['X-Finger-Id'] = fingerId;
            }
        } catch (error) {
            console.error('添加设备指纹失败:', error);
        }
    }

    // 添加请求ID和序列号
    if (sysConfig.REQUEST_BACK_TRACKER) {
        try {
            const lastRequestId = await requestTracker.getCurrentRequestId();
            if (lastRequestId) {
                config.headers['X-Request-Id'] = lastRequestId;
            }
        } catch (error) {
            console.error('回传请求ID失败:', error);
        }

        try {
            const lastSequenceNum = await requestTracker.getCurrentSequenceNum();
            if (lastSequenceNum) {
                config.headers['X-Sequence-Num'] = lastSequenceNum;
            }
        } catch (error) {
            console.error('回传序列号失败:', error);
        }
    }

    // 添加时间戳
    if (!sysConfig.REQUEST_CACHE && config.method === 'get') {
        config.params = config.params || {};
        config.params['_'] = new Date().getTime();
    }

    Object.assign(config.headers, sysConfig.HEADERS);

    return config;
}

// 错误拦截器
export function errorInterceptor(error) {
    if (sysConfig.REQUEST_BACK_TRACKER) {
        // 从响应头获取后端生成的请求ID
        const requestId = error.response?.headers?.['x-request-id'];
        if (requestId) {
            // 记录错误请求信息
            requestTracker.setRequestId(requestId, error.config?.url);
            console.error(`[Error][${requestId}] ${error.config?.url}`, error);
        }
    }

    if (error.config?.external === true) {
        return Promise.reject(error.response);
    }

    if (error.config?.silent === true) {
        return;
    }

    if (error.response) {
        handleHttpError(error);
    } else {
        ElNotification.error({
            title: '请求错误',
            message: '请求服务器无响应！'
        });
    }

    return Promise.reject(error.response);
}

// 处理强制踢出
function handleKickOut(response) {
    state.MessageBox_KickOuted_show = true;
    account.clearUserCaches().then(() => {
        ElMessageBox.alert(response.data.message || '您的账号已被强制下线，请联系管理员', '强制下线通知', {
            type: 'warning',
            closeOnClickModal: false,
            center: true,
            confirmButtonText: '确定',
            beforeClose: (action, instance, done) => {
                state.MessageBox_KickOuted_show = false;
                done();
            },
            callback: () => {
                router.replace({ path: '/login' });
            }
        });
    });

    return Promise.reject(response);
}

// 处理设备禁用
function handleBlocked(response) {
    state.MessageBox_Blocked_show = true;
    ElMessageBox.alert(response.data.message || '您当前的设备已被禁用，请联系管理员', '访问受限', {
        type: 'error',
        closeOnClickModal: false,
        center: true,
        confirmButtonText: '确定',
        beforeClose: (action, instance, done) => {
            state.MessageBox_Blocked_show = false;
            done();
        },
        callback: () => {
            router.replace({ path: '/login' });
        }
    });

    return Promise.reject(response);
}

// 根据接口返回的错误码处理
function handleErrorCode(err_code, globalStore) {
    // 后期根据实际情况进行扩展
}

// 处理HTTP错误
function handleHttpError(error) {
    const errorMap = {
        404: {
            title: '请求错误',
            message: 'Status:404，正在请求不存在的服务器记录！'
        },
        500: {
            title: '请求错误',
            message: error.response.data.message || 'Status:500，服务器发生错误！'
        },
        401: () => {
            if (!state.MessageBox_401_show) {
                state.MessageBox_401_show = true;
                ElMessageBox.confirm('当前用户已被登出或无权限访问当前资源，请尝试重新登录后再操作。', '无权限访问', {
                    type: 'error',
                    closeOnClickModal: false,
                    center: true,
                    confirmButtonText: '重新登录',
                    beforeClose: (action, instance, done) => {
                        state.MessageBox_401_show = false;
                        done();
                    }
                })
                    .then(() => {
                        router.replace({ path: '/login' });
                    })
                    .catch(() => {});
            }
        }
    };

    const status = error.response.status;
    if (errorMap[status]) {
        if (typeof errorMap[status] === 'function') {
            errorMap[status]();
        } else {
            ElNotification.error(errorMap[status]);
        }
    } else {
        ElNotification.error({
            title: '请求错误',
            message: error.message || `Status:${status}，未知错误！`
        });
    }
}
