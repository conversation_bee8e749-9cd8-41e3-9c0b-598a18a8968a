<template>
    <el-container style="overflow: hidden">
        <el-aside style="width: 140px">
            <el-container class="menu-container">
                <el-header class="header-with-button">
                    <el-button
                        type="primary"
                        icon="el-icon-edit"
                        color="#1C409A"
                        @click="showSendDialog"
                        style="width: 100%"
                        >发送新消息</el-button
                    >
                </el-header>
                <el-main class="p0">
                    <el-menu :default-active="defaultPage" class="menu">
                        <el-menu-item
                            v-for="item in regularMenuItems"
                            :key="item.name"
                            :index="item.name"
                            @click="switchType(item)"
                        >
                            <el-icon v-if="item.icon"><component :is="item.icon" /></el-icon>
                            <template #title>
                                <span>{{ item.title }}</span>
                                <el-badge
                                    v-if="item.name === 'all' && item.unreadCount > 0"
                                    :value="item.unreadCount"
                                    class="item"
                                />
                            </template>
                        </el-menu-item>
                    </el-menu>
                </el-main>
                <el-footer class="p0">
                    <el-menu :default-active="defaultPage">
                        <el-menu-item :index="sentMenuItem.name" @click="switchType(sentMenuItem)">
                            <el-icon v-if="sentMenuItem.icon">
                                <component :is="sentMenuItem.icon" />
                            </el-icon>
                            <template #title>
                                <span>{{ sentMenuItem.title }}</span>
                            </template>
                        </el-menu-item>
                    </el-menu>
                </el-footer>
            </el-container>
        </el-aside>
        <notification-list v-if="currentComponent === 'list'" ref="listRef" :type="currentMenuItem?.type" key="list" />
        <notification-sent
            v-else-if="currentComponent === 'sent'"
            ref="listRef"
            :type="currentMenuItem?.type"
            key="sent"
        />

        <!-- 发送消息对话框 -->
        <el-dialog v-model="sendVisible" :close-on-click-modal="false" title="发送消息" width="600px" destroy-on-close>
            <send-message v-if="sendVisible" @success="handleSendSuccess" />
        </el-dialog>
    </el-container>
</template>

<script>
import SendMessage from './send.vue';
import NotificationList from './list.vue';
import NotificationSent from './sent.vue';
import { MENU_ITEMS } from '@/constants/notification';
import { useNotificationStore } from '@/store/notification';
import { storeToRefs } from 'pinia';

export default {
    name: 'NotificationIndex',
    components: {
        NotificationList,
        NotificationSent,
        SendMessage
    },
    setup() {
        const notificationStore = useNotificationStore();
        const { unreadCount } = storeToRefs(notificationStore);
        return { unreadCount };
    },
    data() {
        // 分离常规菜单项和已发送菜单项
        const menuItems = MENU_ITEMS.map(item => ({
            ...item,
            unreadCount: item.name === 'all' ? 0 : undefined
        }));
        const sentIndex = menuItems.findIndex(item => item.name === 'sent');
        const sentMenuItem = menuItems.splice(sentIndex, 1)[0];

        return {
            defaultPage: 'all',
            sendVisible: false,
            currentComponent: 'list',
            currentMenuItem: null,
            regularMenuItems: menuItems, // 常规菜单项
            sentMenuItem, // 已发送菜单项单独存储
            updateTimer: null
        };
    },
    watch: {
        // 监听全局未读数变化，只更新"所有消息"的未读数
        unreadCount: {
            immediate: true,
            handler(count) {
                console.log('消息通知未读数:', count);
                const allItem = this.regularMenuItems.find(item => item.name === 'all');
                if (allItem) {
                    allItem.unreadCount = count;
                }
            }
        }
    },
    mounted() {
        // 获取未读数
        const notificationStore = useNotificationStore();
        notificationStore.getUnreadCount();
    },
    beforeDestroy() {
        // 清除定时器
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }
    },
    methods: {
        /**
         * 切换消息类型
         * @param {Object} item - 菜单项数据
         */
        switchType(item) {
            console.log('切换消息类型：', item);
            this.defaultPage = item.name;
            this.currentComponent = item.component;
            this.currentMenuItem = item; // 保存当前菜单项
        },

        showSendDialog() {
            this.sendVisible = true;
        },

        async handleSendSuccess() {
            this.sendVisible = false;

            // 先刷新列表数据
            if (this.$refs.listRef) {
                await this.$refs.listRef.handleSearch();
            }

            // 立即更新未读数量
            const notificationStore = useNotificationStore();
            await notificationStore.getUnreadCount();

            // 如果在消息通知抽屉打开时发送，也更新抽屉中的列表
            const notification = document.querySelector('.notification');
            if (notification) {
                const notifyComponent = notification.__vue__;
                if (notifyComponent?.notifyVisible) {
                    notifyComponent.resetList();
                    notifyComponent.loadMessages();
                }
            }
        }
    }
};
</script>

<style scoped lang="scss">
// for 菜单未读数量
.el-menu {
    .el-menu-item * {
        vertical-align: super;
    }
}

.header-with-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 10px;

    h3 {
        margin: 0;
    }
}

.el-footer {
    --el-footer-height: 57px;
    padding: 0;
    border-top: 1px solid var(--el-border-color-light);
    background-color: var(--el-bg-color-overlay);

    .el-menu {
        border: none;
    }
}
</style>
