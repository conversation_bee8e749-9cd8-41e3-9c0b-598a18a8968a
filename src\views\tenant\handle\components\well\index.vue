<template>
    <el-container class="well-layout" v-loading="loading">
        <el-header>
            <wellHeader :wellInfo="wellInfo" :pageInfo="pageInfo" />
        </el-header>
        <el-container>
            <wellMain :wellInfo="wellInfo" :pageInfo="pageInfo" />
        </el-container>

        <!-- 设备在线状态监控组件 -->
        <DeviceOnlineMonitor
            ref="deviceMonitor"
            :device-id="wellId"
            :device-name="wellInfo.name"
            :initial-online="deviceOnlineStatus"
            @offline="handleDeviceOffline"
            @online="handleDeviceOnline"
        />
    </el-container>
</template>

<script>
import wellHeader from './wellHeader.vue';
import wellMain from './wellMain.vue';
import DeviceOnlineMonitor from '../DeviceOnlineMonitor.vue';
import { ElMessage } from "element-plus";
import logger from '@/utils/logger';
import deviceOnlineService from '@/services/deviceOnlineService';

export default {
    name: 'wellLayout',
    components: {
        wellHeader,
        wellMain,
        DeviceOnlineMonitor
    },
    data() {
        return {
            // 显示loading
            loading: true,
            wellId: null,
            wellInfo: {
                id: 0,
                name: '',
                level: 0
            },
            pageInfo: {
                currentPage: 1,
                pageShow: 4,
                pageTotal: 1
            },
            // 设备在线状态（从后端获取）
            deviceOnlineStatus: true // 默认为在线，等待后端数据更新
        };
    },
    watch: {
        '$route.params.id': {
            handler(newId) {
                if (newId && this.wellId !== newId) {
                    this.wellId = newId;
                    this.fetchWellInfo();
                }
            },
            immediate: true
        },
        // 监听路由参数变化
        '$route.query.page': {
            handler(newPage) {
                if (newPage) {
                    this.pageInfo.currentPage = parseInt(newPage) || 1;
                }
            },
            immediate: true
        }
    },
    methods: {
        /**
         * 获取油井数据
         * @returns {Promise<void>}
         */
        async fetchWellInfo() {
            try {
                // 调用API获取设备信息
                const result = await this.$API.tenant.device.info.get({
                    id: this.wellId
                });

                // 检查返回状态是否为1
                if (result.status !== 1) {
                    ElMessage.error(result.message || '获取油井数据失败');
                    return;
                }

                // 从返回结果中获取data字段作为油井信息
                this.wellInfo = result.data;

                // 获取设备在线状态
                if (result.data.online !== undefined) {
                    this.deviceOnlineStatus = !!result.data.online;
                    logger.info(`设备 ${this.wellId} 后端返回的在线状态: ${this.deviceOnlineStatus ? '在线' : '离线'}`);

                    // 如果设备离线，显示离线通知
                    if (!this.deviceOnlineStatus) {
                        this.$nextTick(() => {
                            this.$refs.deviceMonitor?.showOfflineNotification();
                        });
                    }
                }

                // 计算分页信息
                if (this.wellInfo.level) {
                    this.pageInfo.pageTotal = Math.ceil(this.wellInfo.level / this.pageInfo.pageShow);
                }

                logger.info('油井数据加载成功:', this.wellInfo);
            } catch (error) {
                ElMessage.error(error.message || '获取油井数据出错');
                logger.error('获取油井数据出错:', error);
            } finally {
                this.loading = false;
            }
        },

        /**
         * 处理设备离线事件
         * @param {Object} data 离线事件数据
         */
        handleDeviceOffline(data) {
            logger.info('设备离线:', data);

            // 可以在这里添加额外的离线处理逻辑
            // 例如停止数据刷新、禁用操作按钮等
            if (this.$refs.wellMain) {
                // 通知wellMain组件设备已离线
                const status = { deviceId: this.wellId, online: false, lastHeartbeat: data.lastHeartbeat };
                this.$refs.wellMain.handleStatusChange(status);
            }

            // 通知父组件设备离线
            this.$emit('device-offline', {
                deviceId: this.wellId,
                deviceName: this.wellInfo.name,
                lastHeartbeat: data.lastHeartbeat
            });
        },

        /**
         * 处理设备上线事件
         * @param {Object} data 上线事件数据
         */
        handleDeviceOnline(data) {
            logger.info('设备上线:', data);

            // 可以在这里添加额外的上线处理逻辑
            // 例如恢复数据刷新、启用操作按钮等
            if (this.$refs.wellMain) {
                // 通知wellMain组件设备已上线
                const status = { deviceId: this.wellId, online: true, lastHeartbeat: data.lastHeartbeat };
                this.$refs.wellMain.handleStatusChange(status);
            }

            // 通知父组件设备上线
            this.$emit('device-online', {
                deviceId: this.wellId,
                deviceName: this.wellInfo.name,
                lastHeartbeat: data.lastHeartbeat
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.el-header {
    height: 36px;
    padding: 0 15px;
}
</style>
