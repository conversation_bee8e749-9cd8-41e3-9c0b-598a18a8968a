<template>
    <el-container class="well-layout" v-loading="loading">
        <el-header>
            <wellHeader :wellInfo="wellInfo" :pageInfo="pageInfo" :active-tab="activeTab" @tab-change="handleTabChange" />
        </el-header>
        <el-container>
            <!-- 数据监控 -->
            <wellMain v-if="activeTab === 'monitor'" :wellInfo="wellInfo" :pageInfo="pageInfo" />

            <!-- 历史数据 -->
            <HistoryData v-if="activeTab === 'history'" :device-id="wellId" :device-info="wellInfo" />
        </el-container>
    </el-container>
</template>

<script>
import wellHeader from './wellHeader.vue';
import wellMain from './wellMain.vue';
import HistoryData from '../HistoryData.vue';
import { ElMessage } from "element-plus";
import logger from '@/utils/logger';
import deviceOnlineService from '@/services/deviceOnlineService';

export default {
    name: 'wellLayout',
    components: {
        wellHeader,
        wellMain,
        HistoryData
    },
    data() {
        return {
            // 显示loading
            loading: true,
            wellId: null,
            wellInfo: {
                id: 0,
                name: '',
                level: 0
            },
            pageInfo: {
                currentPage: 1,
                pageShow: 4,
                pageTotal: 1
            },
            // 当前激活的标签页
            activeTab: 'monitor'
        };
    },
    watch: {
        '$route.params.id': {
            handler(newId) {
                if (newId && this.wellId !== newId) {
                    this.wellId = newId;
                    this.fetchWellInfo();
                }
            },
            immediate: true
        },
        // 监听路由参数变化
        '$route.query.page': {
            handler(newPage) {
                if (newPage) {
                    this.pageInfo.currentPage = parseInt(newPage) || 1;
                }
            },
            immediate: true
        }
    },
    methods: {
        /**
         * 获取油井数据
         * @returns {Promise<void>}
         */
        async fetchWellInfo() {
            try {
                // 使用统一的设备在线服务获取设备信息
                const deviceInfo = await deviceOnlineService.getDeviceInfo(this.wellId);

                if (!deviceInfo) {
                    // 错误信息已在服务中处理
                    return;
                }

                // 设置设备信息
                this.wellInfo = deviceInfo;

                // 计算分页信息
                if (this.wellInfo.level) {
                    this.pageInfo.pageTotal = Math.ceil(this.wellInfo.level / this.pageInfo.pageShow);
                }

                logger.info('油井数据加载成功:', this.wellInfo);
            } catch (error) {
                ElMessage.error(error.message || '获取油井数据出错');
                logger.error('获取油井数据出错:', error);
            } finally {
                this.loading = false;
            }
        },

        /**
         * 处理标签页切换
         * @param {string} tab 标签页名称
         */
        handleTabChange(tab) {
            this.activeTab = tab;
        }
    }
};
</script>

<style lang="scss" scoped>
.el-header {
    height: 36px;
    padding: 0 15px;
}
</style>
