<template>
    <el-container class="well-layout" v-loading="loading">
        <el-header>
            <wellHeader :wellInfo="wellInfo" :pageInfo="pageInfo" />
        </el-header>
        <el-container>
            <wellMain :wellInfo="wellInfo" :pageInfo="pageInfo" />
        </el-container>
    </el-container>
</template>

<script>
import wellHeader from './wellHeader.vue';
import wellMain from './wellMain.vue';
import { ElMessage } from "element-plus";
import logger from '@/utils/logger';
import deviceInfoService from '@/services/deviceInfoService';

export default {
    name: 'wellLayout',
    components: {
        wellHeader,
        wellMain
    },
    data() {
        return {
            // 显示loading
            loading: true,
            wellId: null,
            wellInfo: {
                id: 0,
                name: '',
                level: 0
            },
            pageInfo: {
                currentPage: 1,
                pageShow: 4,
                pageTotal: 1
            }
        };
    },
    watch: {
        '$route.params.id': {
            handler(newId) {
                if (newId && this.wellId !== newId) {
                    this.wellId = newId;
                    this.fetchWellInfo();
                }
            },
            immediate: true
        },
        // 监听路由参数变化
        '$route.query.page': {
            handler(newPage) {
                if (newPage) {
                    this.pageInfo.currentPage = parseInt(newPage) || 1;
                }
            },
            immediate: true
        }
    },
    methods: {
        /**
         * 获取油井数据
         * @returns {Promise<void>}
         */
        async fetchWellInfo() {
            try {
                // 使用统一的设备信息服务获取设备信息
                const deviceInfo = await deviceInfoService.getDeviceInfo(this.wellId);

                if (!deviceInfo) {
                    // 错误信息已在服务中处理
                    return;
                }

                // 设置设备信息
                this.wellInfo = deviceInfo;

                // 计算分页信息
                if (this.wellInfo.level) {
                    this.pageInfo.pageTotal = Math.ceil(this.wellInfo.level / this.pageInfo.pageShow);
                }

                logger.info('油井数据加载成功:', this.wellInfo);
            } catch (error) {
                ElMessage.error(error.message || '获取油井数据出错');
                logger.error('获取油井数据出错:', error);
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.el-header {
    height: 36px;
    padding: 0 15px;
}
</style>
