<template>
    <el-container class="well-layout" v-loading="loading">
        <el-header>
            <wellHeader :wellInfo="wellInfo" :pageInfo="pageInfo" />
        </el-header>
        <el-container>
            <wellMain :wellInfo="wellInfo" :pageInfo="pageInfo" :loading="loading" />
        </el-container>
    </el-container>
</template>

<script>
import wellHeader from './wellHeader.vue';
import wellMain from './wellMain.vue';
import { ElMessage } from "element-plus";
import logger from '@/utils/logger';
import deviceOnlineService from '@/services/deviceOnlineService';

export default {
    name: 'wellLayout',
    components: {
        wellHeader,
        wellMain
    },
    data() {
        return {
            // 显示loading
            loading: true,
            wellId: null,
            wellInfo: {
                id: 0,
                name: '',
                level: 0
            },
            pageInfo: {
                currentPage: 1,
                pageShow: 4,
                pageTotal: 1
            }
        };
    },
    watch: {
        '$route.params.id': {
            handler(newId) {
                if (newId && this.wellId !== newId) {
                    this.wellId = newId;
                    this.fetchWellInfo();
                }
            },
            immediate: true
        },
        // 监听路由参数变化
        '$route.query.page': {
            handler(newPage) {
                if (newPage) {
                    this.pageInfo.currentPage = parseInt(newPage) || 1;
                }
            },
            immediate: true
        }
    },
    methods: {
        /**
         * 获取油井数据
         * @returns {Promise<void>}
         */
        async fetchWellInfo() {
            try {
                // 检查父组件是否已经有设备信息
                if (this.$parent && this.$parent.currentDeviceInfo && this.$parent.currentDeviceId == this.wellId) {
                    this.wellInfo = this.$parent.currentDeviceInfo;

                    // 计算分页信息
                    if (this.wellInfo.level) {
                        this.pageInfo.pageTotal = Math.ceil(this.wellInfo.level / this.pageInfo.pageShow);
                    }

                    logger.info('使用父组件设备信息:', this.wellInfo);
                    return;
                }

                // 延迟一小段时间，让父组件先完成设备信息获取
                await new Promise(resolve => setTimeout(resolve, 100));

                // 再次检查父组件是否已经有设备信息
                if (this.$parent && this.$parent.currentDeviceInfo && this.$parent.currentDeviceId == this.wellId) {
                    this.wellInfo = this.$parent.currentDeviceInfo;

                    // 计算分页信息
                    if (this.wellInfo.level) {
                        this.pageInfo.pageTotal = Math.ceil(this.wellInfo.level / this.pageInfo.pageShow);
                    }

                    logger.info('延迟获取到父组件设备信息:', this.wellInfo);
                    return;
                }

                // 使用统一的设备在线服务获取设备信息
                logger.info('开始获取设备信息:', this.wellId);
                const deviceInfo = await deviceOnlineService.getDeviceInfo(this.wellId, {
                    silent: false // 显示错误信息，便于调试
                });

                if (!deviceInfo) {
                    logger.warn('未获取到设备信息，尝试强制刷新');
                    // 尝试强制刷新
                    const refreshedInfo = await deviceOnlineService.getDeviceInfo(this.wellId, {
                        forceRefresh: true,
                        silent: false
                    });

                    if (!refreshedInfo) {
                        ElMessage.error('当前设备数据为空或错误，请检查设备ID是否正确');
                        return;
                    }

                    this.wellInfo = refreshedInfo;
                } else {
                    this.wellInfo = deviceInfo;
                }

                // 验证设备信息的有效性
                if (!this.wellInfo || !this.wellInfo.id) {
                    ElMessage.error('获取到的设备信息无效');
                    logger.error('设备信息无效:', this.wellInfo);
                    return;
                }

                // 计算分页信息
                if (this.wellInfo.level) {
                    this.pageInfo.pageTotal = Math.ceil(this.wellInfo.level / this.pageInfo.pageShow);
                }

                logger.info('油井数据加载成功:', this.wellInfo);
            } catch (error) {
                ElMessage.error(error.message || '获取油井数据出错');
                logger.error('获取油井数据出错:', error);
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.el-header {
    height: 36px;
    padding: 0 15px;
}
</style>
