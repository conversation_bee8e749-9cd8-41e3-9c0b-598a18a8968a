## 设备状态管理系统介绍和原理：

1. 心跳记录：记录每个设备的最后心跳时间
2. 自动检测：定期检查设备心跳，自动将超时设备标记为离线
3. 持久化存储：使用 IndexedDB 存储设备状态和心跳时间
4. 实时更新：收到设备状态消息时立即更新设备状态
5. 状态恢复：页面加载时从 IndexedDB 恢复设备状态

这种实现方式有以下优点：

1. 更准确的状态：基于实际心跳时间判断设备状态，而不是简单的探测
2. 更好的用户体验：用户打开页面时能立即看到准确的设备状态，而不是默认离线
3. 减少网络流量：不需要频繁发送探测消息，只在需要时发送
4. 更高效的状态管理：通过定时检查自动处理设备离线情况
5. 状态持久化：设备状态和心跳时间持久化存储，页面刷新后恢复状态

## 使用说明

1. 当页面加载时，会从 IndexedDB 加载设备状态，并启动设备状态检查定时器
2. 设备状态会根据最后心跳时间判断，如果超过15秒没有收到心跳，则认为设备离线
3. 当收到设备状态消息时，会记录心跳时间并更新设备状态
4. 当用户翻页或刷新表格时，会先将所有设备设置为离线状态，然后向当前页面的设备发送探测消息

## 注意事项

1. 心跳超时时间设置为15秒，比设备心跳间隔（10秒）稍长，可以根据实际情况调整
2. 设备状态检查定时器在组件销毁时会自动停止，避免内存泄漏
3. 设备状态和心跳时间保存在 IndexedDB 中，页面刷新后仍然可用
