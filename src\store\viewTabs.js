import { defineStore } from 'pinia';
import router from '@/router';

export const useViewTabsStore = defineStore('viewTabsStore', {
    state: () => {
        return {
            viewTabs: []
        };
    },
    getters: {},
    actions: {
        pushViewTabs(route) {
            let backPathIndex = this.viewTabs.findIndex(item => item.fullPath == router.options.history.state.back);
            let target = this.viewTabs.find(item => item.fullPath === route.fullPath);
            let isName = route.name;
            if (!target && isName) {
                if (backPathIndex == -1) {
                    this.viewTabs.push(route);
                } else {
                    this.viewTabs.splice(backPathIndex + 1, 0, route);
                }
            }
        },
        removeViewTabs(route) {
            this.viewTabs.forEach((item, index) => {
                if (item.fullPath === route.fullPath) {
                    this.viewTabs.splice(index, 1);
                }
            });
        },
        updateViewTabs(route) {
            this.viewTabs.forEach(item => {
                if (item.fullPath == route.fullPath) {
                    item = Object.assign(item, route);
                }
            });
        },
        updateViewTabsTitle(title = '') {
            const nowFullPath = location.hash.substring(1);
            this.viewTabs.forEach(item => {
                if (item.fullPath == nowFullPath) {
                    item.meta.title = title;
                }
            });
        },
        clearViewTabs() {
            this.viewTabs = [];
        }
    }
});
