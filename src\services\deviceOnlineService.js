import mqttService from '@/services/mqttService';
import eventManager from '@/utils/eventManager';
import mqttEvents from '@/events/mqttEvents';
import logger from '@/utils/logger';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';
import API from '@/api';
import { ElMessage } from 'element-plus';

// 创建设备在线状态服务专用日志记录器
const log = logger.createLogger('DeviceOnlineService');

// 设备心跳超时时间（毫秒）
const HEARTBEAT_TIMEOUT = 30000; // 30秒

// 获取 Pinia store 实例
let deviceStore = null;

// 设备信息请求缓存
const deviceInfoCache = new Map();
const CACHE_DURATION = 5000; // 5秒缓存时间

// 初始化 store 的函数
function getDeviceStore() {
    if (!deviceStore) {
        deviceStore = useDeviceOnlineStore();
    }
    return deviceStore;
}

/**
 * 设备在线状态服务
 */
const deviceOnlineService = {
    /**
     * 初始化服务
     */
    init() {
        // 初始化 Pinia store
        const store = getDeviceStore();

        // 监听MQTT消息
        mqttEvents.onAutoMessage(this.handleMqttMessage, this);

        // 监听MQTT连接状态
        mqttEvents.onConnected(() => {
            log.info('MQTT已连接');
            store.setMqttConnected(true);
        }, this);

        mqttEvents.onDisconnected(() => {
            log.info('MQTT已断开连接');
            store.setMqttConnected(false);
        }, this);

        // 初始化MQTT连接状态
        store.setMqttConnected(mqttService.isConnected);

        // 定期清理过期设备状态
        eventManager.setInterval(
            () => store.cleanupExpiredStatus(),
            HEARTBEAT_TIMEOUT
        );

        // 定期清理过期的设备信息缓存
        eventManager.setInterval(
            () => this.cleanupExpiredCache(),
            CACHE_DURATION
        );

        log.info('设备在线状态服务初始化完成');
    },

    /**
     * 获取MQTT连接状态
     */
    get mqttConnected() {
        const store = getDeviceStore();
        return store.mqttConnected;
    },

    /**
     * 销毁服务
     */
    destroy() {
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.AUTO_MESSAGE);
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.CONNECTED);
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.DISCONNECTED);
        eventManager.clearAllIntervals();

        // 清理设备信息缓存
        deviceInfoCache.clear();
    },

    /**
     * 清理过期的设备信息缓存
     */
    cleanupExpiredCache() {
        const now = Date.now();
        for (const [key, cached] of deviceInfoCache.entries()) {
            if (now - cached.timestamp > CACHE_DURATION) {
                deviceInfoCache.delete(key);
            }
        }
    },

    /**
     * 处理MQTT消息
     * @param {CustomEvent} event MQTT消息事件
     */
    async handleMqttMessage(event) {
        const { topic, message } = event.detail;
        const store = getDeviceStore();

        // 处理设备心跳消息
        if (topic.includes('/device/') && topic.endsWith('/heartbeat')) {
            const parts = topic.split('/');
            const deviceIndex = parts.indexOf('device');

            if (deviceIndex >= 0 && deviceIndex < parts.length - 1) {
                const deviceId = parts[deviceIndex + 1];
                if (deviceId) {
                    const isOnline = this.determineDeviceStatus(message.status);

                    // 获取当前状态
                    const currentStatus = store.getDeviceStatus(deviceId);
                    log.info(`MQTT消息处理 - 设备 ${deviceId} 当前状态:`, currentStatus);
                    log.info(`MQTT消息处理 - 设备 ${deviceId} 新状态:`, { online: isOnline, status: message.status });

                    // 确定心跳时间：
                    // - 如果设备在线，使用消息时间戳作为心跳时间
                    // - 如果设备离线，不传递心跳时间（让 store 保留最后的心跳时间）
                    const heartbeatTime = isOnline ? (message.timestamp || Date.now()) : null;

                    // 更新设备状态
                    await this.updateDeviceStatus(deviceId, isOnline, heartbeatTime, currentStatus, message.status);
                }
            }
        }
    },

    /**
     * 根据消息状态确定设备在线状态
     * @param {string} status 消息状态
     * @returns {boolean} 设备是否在线
     */
    determineDeviceStatus(status) {
        if (!status) return true;

        switch (status.toLowerCase()) {
            case 'online':
                log.debug(`设备状态: 上线通知`);
                return true;
            case 'offline':
                log.debug(`设备状态: 离线通知`);
                return false;
            case 'alive':
                log.debug(`设备状态: 心跳`);
                return true;
            default:
                log.debug(`设备状态: 未知状态: ${status}`);
                return true;
        }
    },

    /**
     * 更新设备状态
     * @param {string} deviceId 设备ID
     * @param {boolean} isOnline 在线状态
     * @param {number} heartbeatTime 心跳时间戳
     * @param {Object} currentStatus 当前状态
     * @param {string} statusMessage 状态消息
     */
    async updateDeviceStatus(deviceId, isOnline, heartbeatTime, currentStatus, statusMessage) {
        const store = getDeviceStore();

        // 如果状态发生变化，或者没有缓存状态，则更新状态
        if (!currentStatus || currentStatus.online !== isOnline) {
            store.updateDeviceStatus(deviceId, isOnline, heartbeatTime);
            log.info(`设备 ${deviceId} 状态变更: ${isOnline ? '在线' : '离线'}`);
            if (currentStatus) {
                log.info(`状态变更原因: ${statusMessage || '心跳消息'}`);
            }
        } else if (isOnline) {
            // 如果状态没变但设备在线，更新心跳时间
            store.updateDeviceStatus(deviceId, isOnline, heartbeatTime);
            log.info(`设备 ${deviceId} 更新心跳时间`);
        }
    },

    /**
     * 初始化设备状态（从后端API获取）
     * @param {string|number} deviceId 设备ID
     * @param {boolean} online 在线状态
     */
    initDeviceStatus(deviceId, online) {
        const store = getDeviceStore();
        store.initDeviceStatus(deviceId, online);
    },

    /**
     * 检查设备是否在线
     * @param {string|number} deviceId 设备ID
     * @returns {boolean} 是否在线
     */
    isDeviceOnline(deviceId) {
        const store = getDeviceStore();
        return store.isDeviceOnline(deviceId);
    },

    /**
     * 获取设备状态对象
     * @param {string|number} deviceId 设备ID
     * @returns {Object} 设备状态对象
     */
    getDeviceStatus(deviceId) {
        const store = getDeviceStore();
        return store.getDeviceStatus(deviceId);
    },

    /**
     * 获取设备详细信息
     * @param {string|number} deviceId 设备ID
     * @param {Object} options 可选参数
     * @param {number} options.layer 层级（可选，用于分层查询）
     * @param {boolean} options.silent 是否静默模式（不显示错误提示）
     * @param {boolean} options.forceRefresh 是否强制刷新（忽略缓存）
     * @returns {Promise<Object|null>} 设备信息对象，失败时返回null
     */
    async getDeviceInfo(deviceId, options = {}) {
        const { layer, silent = false, forceRefresh = false } = options;

        // 构建缓存键
        const cacheKey = `${deviceId}_${layer || 'all'}`;
        const now = Date.now();

        // 检查缓存（如果不是强制刷新）
        if (!forceRefresh && deviceInfoCache.has(cacheKey)) {
            const cached = deviceInfoCache.get(cacheKey);
            if (now - cached.timestamp < CACHE_DURATION) {
                // 如果是正在进行的请求，等待该请求完成
                if (cached.promise) {
                    log.info(`设备 ${deviceId} 信息请求已在进行中，等待完成`);
                    try {
                        return await cached.promise;
                    } catch (error) {
                        // 如果等待的请求失败了，清除缓存并继续执行新请求
                        deviceInfoCache.delete(cacheKey);
                        log.warn(`等待的请求失败，清除缓存并重新请求: ${error.message}`);
                    }
                } else if (cached.data) {
                    // 返回缓存的数据
                    log.info(`使用缓存的设备 ${deviceId} 信息`);
                    return cached.data;
                }
            } else {
                // 缓存过期，清除
                deviceInfoCache.delete(cacheKey);
            }
        }

        try {
            log.group(`获取设备 ${deviceId} 信息`);
            log.info(`设备ID: ${deviceId}${layer ? `, 层级: ${layer}` : ''}`);

            // 构建请求参数
            const params = { id: deviceId };
            if (layer !== undefined) {
                params.layer = layer;
            }

            // 创建请求Promise
            const requestPromise = API.tenant.device.info.get(params);

            // 将正在进行的请求存入缓存
            deviceInfoCache.set(cacheKey, {
                timestamp: now,
                promise: requestPromise,
                data: null
            });

            // 调用API获取设备信息
            const response = await requestPromise;

            // 检查返回状态
            if (response && response.status === 1 && response.data) {
                log.info('设备信息获取成功:', response.data);

                // 更新缓存，移除Promise，保存数据
                deviceInfoCache.set(cacheKey, {
                    timestamp: now,
                    promise: null,
                    data: response.data
                });

                log.groupEnd();
                return response.data;
            } else {
                const errorMsg = response?.message || '获取设备信息失败';
                log.error('API返回错误:', errorMsg);

                // 清除缓存
                deviceInfoCache.delete(cacheKey);

                if (!silent) {
                    ElMessage.error(errorMsg);
                }

                log.groupEnd();
                return null;
            }
        } catch (error) {
            log.error('获取设备信息异常:', error);

            // 清除缓存
            deviceInfoCache.delete(cacheKey);

            if (!silent) {
                ElMessage.error('无法连接到服务器，请检查网络连接');
            }

            log.groupEnd();
            return null;
        }
    },

    /**
     * 刷新设备状态并更新本地缓存
     * @param {string|number} deviceId 设备ID
     * @param {boolean} silent 是否静默模式（不显示提示）
     * @returns {Promise<boolean|null>} 设备在线状态，失败时返回null
     */
    async refreshDeviceStatus(deviceId, silent = false) {
        try {
            log.group(`刷新设备 ${deviceId} 状态`);

            const deviceInfo = await this.getDeviceInfo(deviceId, { silent: true });

            if (deviceInfo && typeof deviceInfo.online === 'boolean') {
                const isOnline = deviceInfo.online;
                const store = getDeviceStore();

                // 更新本地状态
                store.updateDeviceStatus(deviceId, isOnline);
                log.info(`已更新本地状态: ${isOnline ? '在线' : '离线'}`);

                if (!silent) {
                    if (isOnline) {
                        ElMessage.success('设备当前在线');
                    } else {
                        ElMessage.warning('设备仍然离线');
                    }
                }

                log.groupEnd();
                return isOnline;
            } else {
                log.warn('无法获取有效的设备状态');

                if (!silent) {
                    ElMessage.error('获取设备状态失败');
                }

                log.groupEnd();
                return null;
            }
        } catch (error) {
            log.error('刷新设备状态异常:', error);

            if (!silent) {
                ElMessage.error('刷新设备状态失败');
            }

            log.groupEnd();
            return null;
        }
    },

    /**
     * 获取多个设备的在线状态
     * @param {Array<string|number>} deviceIds 设备ID数组
     * @returns {Array} 设备状态结果数组
     */
    getDevicesOnlineStatus(deviceIds) {
        const store = getDeviceStore();
        return store.getDevicesOnlineStatus(deviceIds);
    },

    /**
     * 获取在线设备数量
     * @returns {number} 在线设备数量
     */
    getOnlineDeviceCount() {
        const store = getDeviceStore();
        return store.onlineDeviceCount;
    }
};

// 初始化服务
deviceOnlineService.init();

export default deviceOnlineService;
