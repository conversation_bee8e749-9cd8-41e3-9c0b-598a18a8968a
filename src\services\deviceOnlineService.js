import mqttService from '@/services/mqttService';
import { reactive } from 'vue';
import eventManager from '@/utils/eventManager';
import mqttEvents from '@/events/mqttEvents';
import logger from '@/utils/logger';

// 创建设备在线状态服务专用日志记录器
const log = logger.createLogger('DeviceOnlineService');

// 设备心跳超时时间（毫秒）
const HEARTBEAT_TIMEOUT = 30000; // 30秒

// 设备状态存储（内存中的缓存）
const deviceOnlineStatus = reactive({
    // 设备状态监听器 {deviceId: Set<Function>}
    listeners: new Map(),
    // 内存缓存 {deviceId: {online: boolean, lastHeartbeat: timestamp}}
    cache: new Map()
});

// 确保cache是一个正确的Map实例
if (!(deviceOnlineStatus.cache instanceof Map)) {
    deviceOnlineStatus.cache = new Map();
}

/**
 * 设备在线状态服务
 */
const deviceOnlineService = {
    // MQTT连接状态
    mqttConnected: false,

    /**
     * 初始化服务
     */
    init() {
        // 监听MQTT消息
        mqttEvents.onAutoMessage(this.handleMqttMessage, this);

        // 监听MQTT连接状态
        mqttEvents.onConnected(() => {
            log.info('MQTT已连接');
            this.mqttConnected = true;
        }, this);

        mqttEvents.onDisconnected(() => {
            log.info('MQTT已断开连接');
            this.mqttConnected = false;
        }, this);

        // 初始化MQTT连接状态
        this.mqttConnected = mqttService.isConnected;

        // 定期清理过期设备状态
        eventManager.setInterval(
            () => this.cleanupExpiredStatus(),
            HEARTBEAT_TIMEOUT
        );

        log.info('设备在线状态服务初始化完成');
    },

    /**
     * 销毁服务
     */
    destroy() {
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.AUTO_MESSAGE);
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.CONNECTED);
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.DISCONNECTED);
        eventManager.clearAllIntervals();
    },

    /**
     * 处理MQTT消息
     * @param {CustomEvent} event MQTT消息事件
     */
    async handleMqttMessage(event) {
        const { topic, message } = event.detail;

        // 处理设备心跳消息
        if (topic.includes('/device/') && topic.endsWith('/heartbeat')) {
            const parts = topic.split('/');
            const deviceIndex = parts.indexOf('device');

            if (deviceIndex >= 0 && deviceIndex < parts.length - 1) {
                const deviceId = parts[deviceIndex + 1];
                if (deviceId) {
                    const heartbeatTime = message.timestamp || Date.now();
                    const isOnline = this.determineDeviceStatus(message.status);

                    // 获取当前状态
                    const currentStatus = deviceOnlineStatus.cache.get(deviceId);
                    log.info(`MQTT消息处理 - 设备 ${deviceId} 当前状态:`, currentStatus);
                    log.info(`MQTT消息处理 - 设备 ${deviceId} 新状态:`, { online: isOnline, status: message.status });

                    // 更新设备状态
                    await this.updateDeviceStatus(deviceId, isOnline, heartbeatTime, currentStatus, message.status);
                }
            }
        }
    },

    /**
     * 根据消息状态确定设备在线状态
     * @param {string} status 消息状态
     * @returns {boolean} 设备是否在线
     */
    determineDeviceStatus(status) {
        if (!status) return true;

        switch (status.toLowerCase()) {
            case 'online':
                log.debug(`设备状态: 上线通知`);
                return true;
            case 'offline':
                log.debug(`设备状态: 离线通知`);
                return false;
            case 'alive':
                log.debug(`设备状态: 心跳`);
                return true;
            default:
                log.debug(`设备状态: 未知状态: ${status}`);
                return true;
        }
    },

    /**
     * 更新设备状态
     * @param {string} deviceId 设备ID
     * @param {boolean} isOnline 在线状态
     * @param {number} heartbeatTime 心跳时间戳
     * @param {Object} currentStatus 当前状态
     * @param {string} statusMessage 状态消息
     */
    async updateDeviceStatus(deviceId, isOnline, heartbeatTime, currentStatus, statusMessage) {
        // 如果状态发生变化，或者没有缓存状态，则更新状态
        if (!currentStatus || currentStatus.online !== isOnline) {
            await this.updateDeviceOnlineStatus(deviceId, isOnline);
            log.info(`设备 ${deviceId} 状态变更: ${isOnline ? '在线' : '离线'}`);
            if (currentStatus) {
                log.info(`状态变更原因: ${statusMessage || '心跳消息'}`);
            }
        } else if (isOnline) {
            // 如果状态没变但设备在线，更新心跳时间
            const newStatus = {
                ...currentStatus,
                lastHeartbeat: heartbeatTime,
                lastUpdate: Date.now()
            };
            deviceOnlineStatus.cache.set(deviceId, newStatus);
            log.info(`设备 ${deviceId} 更新心跳时间:`, newStatus);
            await this.notifyStatusChange(deviceId);
        }
    },

    /**
     * 初始化设备状态（从后端API获取）
     * @param {string|number} deviceId 设备ID
     * @param {boolean} online 在线状态
     */
    initDeviceStatus(deviceId, online) {
        const deviceIdStr = String(deviceId);
        const now = Date.now();

        // 只有当缓存中没有状态时才初始化
        if (!deviceOnlineStatus.cache.has(deviceIdStr)) {
            const status = {
                deviceId: deviceIdStr,
                online: !!online,
                lastHeartbeat: online ? now : null,
                lastUpdate: now
            };

            deviceOnlineStatus.cache.set(deviceIdStr, status);
            log.debug(`初始化设备 ${deviceIdStr} 状态: ${online ? '在线' : '离线'}`);
        }
    },

    /**
     * 更新设备在线状态（从MQTT消息）
     * @param {string} deviceId 设备ID
     * @param {boolean} online 在线状态
     * @returns {Promise<void>}
     */
    async updateDeviceOnlineStatus(deviceId, online) {
        const now = Date.now();
        const status = {
            deviceId,
            online,
            lastHeartbeat: online ? now : null,
            lastUpdate: now
        };

        // 使用Map的set方法更新状态
        deviceOnlineStatus.cache.set(deviceId, status);

        // 通知状态变化
        await this.notifyStatusChange(deviceId);
    },

    /**
     * 获取设备状态
     * @param {string} deviceId 设备ID
     * @returns {Promise<Object>} 设备状态对象
     */
    async getDeviceOnlineStatus(deviceId) {
        const status = deviceOnlineStatus.cache.get(deviceId);

        if (!status) {
            // 如果缓存中没有状态，返回默认离线状态
            // 实际状态应该通过后端API获取或MQTT消息更新
            log.debug(`设备 ${deviceId} 状态未找到，返回默认离线状态`);
            return { deviceId, online: false, lastHeartbeat: null, lastUpdate: null };
        }

        return status;
    },

    /**
     * 检查设备是否在线（异步方法）
     * @param {string} deviceId 设备ID
     * @returns {Promise<boolean>} 是否在线
     */
    async isDeviceOnline(deviceId) {
        const status = await this.getDeviceOnlineStatus(deviceId);
        return status.online;
    },

    /**
     * 检查设备是否在线（同步方法）
     * @param {string|number} deviceId 设备ID
     * @returns {boolean} 是否在线
     */
    isDeviceOnlineSync(deviceId) {
        // 确保deviceId是字符串类型
        const deviceIdStr = String(deviceId);
        const status = deviceOnlineStatus.cache.get(deviceIdStr);
        return status ? status.online : false;
    },

    /**
     * 添加设备状态监听器
     * @param {string} deviceId 设备ID
     * @param {Function} listener 监听器函数
     * @param {Object} context 上下文对象
     */
    async addStatusListener(deviceId, listener, context) {
        if (!deviceOnlineStatus.listeners.has(deviceId)) {
            deviceOnlineStatus.listeners.set(deviceId, new Set());
        }

        const boundListener = context ? listener.bind(context) : listener;
        const listenerInfo = { original: listener, bound: boundListener, context };

        deviceOnlineStatus.listeners.get(deviceId).add(listenerInfo);

        // 立即通知当前状态（如果缓存中有的话）
        const status = await this.getDeviceOnlineStatus(deviceId);
        boundListener(status);

        return boundListener;
    },

    /**
     * 移除设备状态监听器
     * @param {string} deviceId 设备ID
     * @param {Function} listener 监听器函数
     * @param {Object} context 上下文对象
     */
    removeStatusListener(deviceId, listener, context) {
        if (deviceOnlineStatus.listeners.has(deviceId)) {
            const listeners = deviceOnlineStatus.listeners.get(deviceId);
            const listenerInfo = Array.from(listeners).find(info =>
                info.original === listener && info.context === context);

            if (listenerInfo) {
                listeners.delete(listenerInfo);
                if (listeners.size === 0) {
                    deviceOnlineStatus.listeners.delete(deviceId);
                }
            }
        }
    },

    /**
     * 通知设备状态变更
     * @param {string} deviceId 设备ID
     */
    async notifyStatusChange(deviceId) {
        if (!deviceOnlineStatus.listeners.has(deviceId)) {
            log.warn(`设备 ${deviceId} 没有监听器，无法通知状态变更`);
            return;
        }

        const status = deviceOnlineStatus.cache.get(deviceId);
        if (!status) {
            log.warn(`设备 ${deviceId} 没有缓存状态，无法通知状态变更`);
            return;
        }

        log.info(`通知设备 ${deviceId} 状态变更 - 当前状态:`, status);
        log.info(`通知设备 ${deviceId} 状态变更 - 监听器数量:`, deviceOnlineStatus.listeners.get(deviceId).size);

        deviceOnlineStatus.listeners.get(deviceId).forEach(listenerInfo => {
            try {
                log.info(`通知设备 ${deviceId} 状态变更 - 调用监听器`);
                listenerInfo.bound(status);
            } catch (error) {
                log.error(`设备状态监听器执行错误:`, error);
            }
        });
    },

    /**
     * 清理过期的设备状态
     */
    async cleanupExpiredStatus() {
        const now = Date.now();
        const SEVEN_DAYS = 7 * 24 * 60 * 60 * 1000;
        let cleanedCount = 0;

        for (const [deviceId, status] of deviceOnlineStatus.cache.entries()) {
            if (status.lastUpdate && now - status.lastUpdate > SEVEN_DAYS) {
                deviceOnlineStatus.cache.delete(deviceId);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            log.group('清理长期不活跃设备状态');
            log.info(`清理了 ${cleanedCount} 个长期不活跃设备状态`);
            log.info(`清理时间: ${new Date().toLocaleString()}`);
            log.groupEnd();
        }
    },

    /**
     * 获取多个设备的在线状态
     * @param {Array<string|number>} deviceIds 设备ID数组
     * @returns {Promise<Array>} 设备状态结果数组
     */
    async getDevicesOnlineStatus(deviceIds) {
        if (!deviceIds?.length) return [];

        // 使用同步方法批量获取状态，避免重复的异步调用
        return deviceIds.map(deviceId => {
            const deviceIdStr = String(deviceId);
            const isOnline = this.isDeviceOnlineSync(deviceIdStr);
            return {
                deviceId: deviceIdStr,
                online: isOnline
            };
        });
    }
};

// 初始化服务
deviceOnlineService.init();

export default deviceOnlineService;
