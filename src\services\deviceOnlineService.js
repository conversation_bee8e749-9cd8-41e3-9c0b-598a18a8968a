import mqttService from '@/services/mqttService';
import eventManager from '@/utils/eventManager';
import mqttEvents from '@/events/mqttEvents';
import logger from '@/utils/logger';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';

// 创建设备在线状态服务专用日志记录器
const log = logger.createLogger('DeviceOnlineService');

// 设备心跳超时时间（毫秒）
const HEARTBEAT_TIMEOUT = 30000; // 30秒

// 获取 Pinia store 实例
let deviceStore = null;

// 初始化 store 的函数
function getDeviceStore() {
    if (!deviceStore) {
        deviceStore = useDeviceOnlineStore();
    }
    return deviceStore;
}

/**
 * 设备在线状态服务
 */
const deviceOnlineService = {
    /**
     * 初始化服务
     */
    init() {
        // 初始化 Pinia store
        const store = getDeviceStore();

        // 监听MQTT消息
        mqttEvents.onAutoMessage(this.handleMqttMessage, this);

        // 监听MQTT连接状态
        mqttEvents.onConnected(() => {
            log.info('MQTT已连接');
            store.setMqttConnected(true);
        }, this);

        mqttEvents.onDisconnected(() => {
            log.info('MQTT已断开连接');
            store.setMqttConnected(false);
        }, this);

        // 初始化MQTT连接状态
        store.setMqttConnected(mqttService.isConnected);

        // 定期清理过期设备状态
        eventManager.setInterval(
            () => store.cleanupExpiredStatus(),
            HEARTBEAT_TIMEOUT
        );

        log.info('设备在线状态服务初始化完成');
    },

    /**
     * 获取MQTT连接状态
     */
    get mqttConnected() {
        const store = getDeviceStore();
        return store.mqttConnected;
    },

    /**
     * 销毁服务
     */
    destroy() {
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.AUTO_MESSAGE);
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.CONNECTED);
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.DISCONNECTED);
        eventManager.clearAllIntervals();
    },

    /**
     * 处理MQTT消息
     * @param {CustomEvent} event MQTT消息事件
     */
    async handleMqttMessage(event) {
        const { topic, message } = event.detail;
        const store = getDeviceStore();

        // 处理设备心跳消息
        if (topic.includes('/device/') && topic.endsWith('/heartbeat')) {
            const parts = topic.split('/');
            const deviceIndex = parts.indexOf('device');

            if (deviceIndex >= 0 && deviceIndex < parts.length - 1) {
                const deviceId = parts[deviceIndex + 1];
                if (deviceId) {
                    const isOnline = this.determineDeviceStatus(message.status);

                    // 获取当前状态
                    const currentStatus = store.getDeviceStatus(deviceId);
                    log.info(`MQTT消息处理 - 设备 ${deviceId} 当前状态:`, currentStatus);
                    log.info(`MQTT消息处理 - 设备 ${deviceId} 新状态:`, { online: isOnline, status: message.status });

                    // 确定心跳时间：
                    // - 如果设备在线，使用消息时间戳作为心跳时间
                    // - 如果设备离线，不传递心跳时间（让 store 保留最后的心跳时间）
                    const heartbeatTime = isOnline ? (message.timestamp || Date.now()) : null;

                    // 更新设备状态
                    await this.updateDeviceStatus(deviceId, isOnline, heartbeatTime, currentStatus, message.status);
                }
            }
        }
    },

    /**
     * 根据消息状态确定设备在线状态
     * @param {string} status 消息状态
     * @returns {boolean} 设备是否在线
     */
    determineDeviceStatus(status) {
        if (!status) return true;

        switch (status.toLowerCase()) {
            case 'online':
                log.debug(`设备状态: 上线通知`);
                return true;
            case 'offline':
                log.debug(`设备状态: 离线通知`);
                return false;
            case 'alive':
                log.debug(`设备状态: 心跳`);
                return true;
            default:
                log.debug(`设备状态: 未知状态: ${status}`);
                return true;
        }
    },

    /**
     * 更新设备状态
     * @param {string} deviceId 设备ID
     * @param {boolean} isOnline 在线状态
     * @param {number} heartbeatTime 心跳时间戳
     * @param {Object} currentStatus 当前状态
     * @param {string} statusMessage 状态消息
     */
    async updateDeviceStatus(deviceId, isOnline, heartbeatTime, currentStatus, statusMessage) {
        const store = getDeviceStore();

        // 如果状态发生变化，或者没有缓存状态，则更新状态
        if (!currentStatus || currentStatus.online !== isOnline) {
            store.updateDeviceStatus(deviceId, isOnline, heartbeatTime);
            log.info(`设备 ${deviceId} 状态变更: ${isOnline ? '在线' : '离线'}`);
            if (currentStatus) {
                log.info(`状态变更原因: ${statusMessage || '心跳消息'}`);
            }
        } else if (isOnline) {
            // 如果状态没变但设备在线，更新心跳时间
            store.updateDeviceStatus(deviceId, isOnline, heartbeatTime);
            log.info(`设备 ${deviceId} 更新心跳时间`);
        }
    },

    /**
     * 初始化设备状态（从后端API获取）
     * @param {string|number} deviceId 设备ID
     * @param {boolean} online 在线状态
     */
    initDeviceStatus(deviceId, online) {
        const store = getDeviceStore();
        store.initDeviceStatus(deviceId, online);
    },

    /**
     * 更新设备在线状态（从MQTT消息）
     * @param {string} deviceId 设备ID
     * @param {boolean} online 在线状态
     * @returns {Promise<void>}
     */
    async updateDeviceOnlineStatus(deviceId, online) {
        const store = getDeviceStore();
        store.updateDeviceStatus(deviceId, online);
    },

    /**
     * 获取设备状态
     * @param {string} deviceId 设备ID
     * @returns {Promise<Object>} 设备状态对象
     */
    async getDeviceOnlineStatus(deviceId) {
        const store = getDeviceStore();
        return store.getDeviceStatus(deviceId);
    },

    /**
     * 检查设备是否在线（异步方法）
     * @param {string} deviceId 设备ID
     * @returns {Promise<boolean>} 是否在线
     */
    async isDeviceOnline(deviceId) {
        const store = getDeviceStore();
        return store.isDeviceOnline(deviceId);
    },

    /**
     * 检查设备是否在线（同步方法）
     * @param {string|number} deviceId 设备ID
     * @returns {boolean} 是否在线
     */
    isDeviceOnlineSync(deviceId) {
        const store = getDeviceStore();
        return store.isDeviceOnline(deviceId);
    },

    /**
     * 添加设备状态监听器
     * @param {string} deviceId 设备ID
     * @param {Function} listener 监听器函数
     * @param {Object} context 上下文对象
     */
    async addStatusListener(deviceId, listener, context) {
        const store = getDeviceStore();
        return store.addStatusListener(deviceId, listener, context);
    },

    /**
     * 移除设备状态监听器
     * @param {string} deviceId 设备ID
     * @param {Function} listener 监听器函数
     * @param {Object} context 上下文对象
     */
    removeStatusListener(deviceId, listener, context) {
        const store = getDeviceStore();
        store.removeStatusListener(deviceId, listener, context);
    },

    /**
     * 通知设备状态变更（内部方法，由 Pinia store 处理）
     * @param {string} deviceId 设备ID
     */
    async notifyStatusChange(deviceId) {
        const store = getDeviceStore();
        store.notifyStatusChange(deviceId);
    },

    /**
     * 获取多个设备的在线状态
     * @param {Array<string|number>} deviceIds 设备ID数组
     * @returns {Promise<Array>} 设备状态结果数组
     */
    async getDevicesOnlineStatus(deviceIds) {
        const store = getDeviceStore();
        return store.getDevicesOnlineStatus(deviceIds);
    },

    /**
     * 获取在线设备数量
     * @returns {number} 在线设备数量
     */
    getOnlineDeviceCount() {
        const store = getDeviceStore();
        return store.onlineDeviceCount;
    }
};

// 初始化服务
deviceOnlineService.init();

export default deviceOnlineService;
