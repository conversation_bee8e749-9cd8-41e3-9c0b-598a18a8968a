class RequestTracker {
    constructor() {
        this.currentRequestId = null;
        this.currentSequenceNum = 0;
        this.requestHistory = new Map(); // 存储最近的请求记录
    }

    setRequestId(requestId, url) {
        this.currentRequestId = requestId;
        this.requestHistory.set(requestId, {
            timestamp: Date.now(),
            url
        });

        console.log('requestId:::', requestId, 'url:::', url);
        console.log('this.requestHistory:::', this.requestHistory);

        // 只保留最近的 10 条记录
        if (this.requestHistory.size > 10) {
            const oldestKey = this.requestHistory.keys().next().value;
            this.requestHistory.delete(oldestKey);
        }
    }

    setSequenceNum(sequenceNum) {
        this.currentSequenceNum = sequenceNum;
    }

    getCurrentSequenceNum() {
        return this.currentSequenceNum;
    }

    getCurrentRequestId() {
        return this.currentRequestId;
    }

    getRequestInfo(requestId) {
        return this.requestHistory.get(requestId);
    }
}

export const requestTracker = new RequestTracker();
