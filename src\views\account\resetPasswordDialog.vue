<template>
    <!--用户密码重置对话框-->
    <el-dialog
        v-model="useGlobalStore().userResetPassword"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :draggable="true"
        :show-close="false"
        :show-fullscreen="false"
        title="重置密码"
        custom-class="password_reset_dialog"
        modal-class="password_reset_modal"
        width="530px"
    >
        <resetPassword showMode="dialog"></resetPassword>
    </el-dialog>
</template>

<script>
import { useGlobalStore } from '@/stores/global';
import resetPassword from '@/views/profile/user/password';

export default {
    name: 'resetPasswordDialog',
    components: {
        resetPassword
    },
    data() {},
    created() {},
    methods: {
        /**
         * 引用全局状态管理
         */
        useGlobalStore
    }
};
</script>

<style lang="scss" scoped>
.password_reset_dialog {
    .el-dialog__body {
        padding: 0;
    }
}

:global(.password_reset_modal) {
    backdrop-filter: blur(10px);
    background-color: rgba(0, 0, 0, 0.6) !important;
}
</style>
