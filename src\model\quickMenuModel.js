import { closeDatabase, db } from '@/utils/indexedDB';
import storage from '@/config/storage';

// 存储对象名称
const STORE_NAME = storage.indexedDB.storeConfigs.quickMenu.name;

/**
 * 快捷菜单数据库模型
 */
class QuickMenuModel {
    // 初始化 Promise
    initPromise = null;

    /**
     * 构造函数
     */
    constructor() {
        console.log('初始化快捷菜单数据库模型');
        // 初始化 Promise
        this.initPromise = Promise.resolve();
    }

    /**
     * 获取快速菜单列表
     * @returns {Promise<Array>} 快速菜单列表
     */
    async getQuickMenus() {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 使用 db 的 getAll 方法获取所有快捷菜单
            return await db.getAll(STORE_NAME);
        } catch (error) {
            console.error('获取快捷菜单失败:', error);
            return [];
        }
    }

    /**
     * 保存快速菜单列表
     * @param {Array} items 快速菜单列表
     * @returns {Promise<Boolean>} 是否成功
     */
    async saveQuickMenus(items) {
        try {
            // 确保数据库已初始化
            await this.initPromise;

            // 使用 db 的 saveAll 方法批量保存快捷菜单
            await db.saveAll(STORE_NAME, items);
            return true;
        } catch (error) {
            console.error('保存快捷菜单失败:', error);
            return false;
        }
    }

    /**
     * 关闭数据库连接
     */
    close() {
        closeDatabase();
    }
}

// 创建数据库实例
const quickMenuModel = new QuickMenuModel();

// 导出数据库实例
export default quickMenuModel;
