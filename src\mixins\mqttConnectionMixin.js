import mqttEvents from '@/events/mqttEvents';
import eventManager from '@/utils/eventManager';
import loggerFactory from '@/utils/logger';
import deviceOnlineService from '@/services/deviceOnlineService';

// 创建自定义前缀的日志记录器
const logger = loggerFactory.createLogger('MqttConnectionMixin');

export default {
    data() {
        return {
            mqttConnected: deviceOnlineService.mqttConnected
        };
    },
    methods: {
        handleMqttConnected() {
            logger.debug('MQTT已连接');
            this.mqttConnected = true;
        },
        handleMqttDisconnected() {
            logger.debug('MQTT已断开连接');
            this.mqttConnected = false;
        }
    },
    mounted() {
        // 添加MQTT连接状态监听
        mqttEvents.onConnected(this.handleMqttConnected, this);
        mqttEvents.onDisconnected(this.handleMqttDisconnected, this);

        // 初始化MQTT连接状态
        this.mqttConnected = deviceOnlineService.mqttConnected;
    },
    beforeDestroy() {
        // 移除MQTT连接状态监听
        eventManager.removeEventListener(mqttEvents.EVENTS.CONNECTED, this.handleMqttConnected, this);
        eventManager.removeEventListener(mqttEvents.EVENTS.DISCONNECTED, this.handleMqttDisconnected, this);
    }
}; 