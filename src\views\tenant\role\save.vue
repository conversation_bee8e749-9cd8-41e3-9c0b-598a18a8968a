<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="500px"
        :destroy-on-close="true"
        :draggable="true"
        :close-on-click-modal="false"
        @closed="dialogClosed"
    >
        <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="100px"
            label-position="right"
        >
            <el-form-item label="角色名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入角色名称"></el-input>
            </el-form-item>
            <el-form-item label="角色标识" prop="code">
                <el-input v-model="formData.code" placeholder="请输入角色标识"></el-input>
            </el-form-item>
            <el-form-item label="角色状态" prop="status">
                <el-switch
                    v-model="formData.status"
                    :active-value="1"
                    :inactive-value="0"
                ></el-switch>
            </el-form-item>
            <el-form-item label="备注" prop="description">
                <el-input
                    v-model="formData.description"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入备注信息"
                ></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
        </template>
    </el-dialog>
</template>

<script>
import { reactive, ref, getCurrentInstance } from 'vue'; // 添加 getCurrentInstance
import { ElMessage } from 'element-plus';
import tableConfig from '@/config/table.js';

export default {
    name: 'tenant.role.save',
    emits: ['saveDialogClosedEmit', 'saveSuccessEmit'],
    setup(props, { emit }) {
        const { proxy } = getCurrentInstance(); // 获取实例
        const formRef = ref(null);
        const dialogVisible = ref(false);
        const dialogType = ref('');
        const dialogTitle = ref('');
        const formData = reactive({
            id: '',
            name: '',
            code: '',
            status: 1,
            description: ''
        });

        const formRules = {
            name: [
                { required: true, message: '请输入角色名称', trigger: 'blur' },
                { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
            ],
            code: [
                { required: true, message: '请输入角色标识', trigger: 'blur' },
                { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
            ]
        };

        const show = (type = 'add', data = null) => {
            dialogType.value = type;
            dialogVisible.value = true;

            if (type === 'add') {
                dialogTitle.value = '添加角色';
                Object.assign(formData, {
                    id: '',
                    name: '',
                    code: '',
                    status: 1,
                    description: ''
                });
            } else {
                dialogTitle.value = type === 'edit' ? '编辑角色' : '查看角色';
                Object.assign(formData, data);
            }

            return {
                setFormData: (data, isBatch = false) => {
                    if (isBatch) {
                        formData.ids = data;
                    } else {
                        Object.assign(formData, data);
                    }
                }
            };
        };

        const submitForm = () => {
            formRef.value.validate(valid => {
                if (valid) {
                    const apiMethod = dialogType.value === 'add' ? 'add' : 'edit';
                    const reqData = { ...formData };

                    // 确保编辑时包含ID
                    if (dialogType.value === 'edit' && !reqData.id) {
                        ElMessage.error('角色ID不能为空');
                        return;
                    }

                    proxy.$API.tenant.role[apiMethod]  // 使用 proxy.$API 替代 window.$API
                        .post(reqData)
                        .then(res => {
                            if (res.code === tableConfig.successCode) {
                                ElMessage.success(res.message);
                                dialogVisible.value = false;
                                emit('saveSuccessEmit');
                            } else {
                                ElMessage.error(res.message || '操作失败');
                            }
                        })
                        .catch((error) => {
                            console.error('保存失败:', error);
                            ElMessage.error('操作失败');
                        });
                }
            });
        };

        const dialogClosed = () => {
            emit('saveDialogClosedEmit');
        };

        return {
            formRef,
            dialogVisible,
            dialogType,
            dialogTitle,
            formData,
            formRules,
            show,
            submitForm,
            dialogClosed
        };
    }
};
</script>
