<template>
    <el-form ref="loginForm" :model="form" :rules="rules" label-width="0" size="large" @keyup.enter="login">
        <el-form-item prop="phone">
            <el-input
                v-model="form.phone"
                :placeholder="$t('login.mobilePlaceholder')"
                clearable
                prefix-icon="el-icon-iphone"
                @input="loginLength"
                style="width: 100%; height: 42px"
            >
                <template #prepend>+86</template>
            </el-input>
        </el-form-item>
        <el-form-item prop="code" style="margin-bottom: 35px">
            <div class="login-msg-yzm">
                <el-input
                    v-model="form.code"
                    :placeholder="$t('login.smsPlaceholder')"
                    clearable
                    prefix-icon="el-icon-unlock"
                    style="width: 100%; height: 42px"
                ></el-input>
                <el-button
                    :disabled="disabled"
                    :round="false"
                    color="#339257"
                    style="height: 42px; filter: opacity(0.75)"
                    type="primary"
                    @click="getYzm"
                    >获取验证码<span v-if="disabled"> ({{ countdown }})</span></el-button
                >
            </div>
        </el-form-item>
        <el-form-item>
            <el-button
                :loading="loading"
                :round="false"
                :color="dialogMode ? '#339257' : '#3f3648'"
                style="width: 100%; height: 42px; filter: opacity(0.95)"
                type="primary"
                @click="login"
                >登 录</el-button
            >
        </el-form-item>
        <!--<div class="login-reg">
            {{ $t('login.noAccount') }}
            <router-link to="/user_register">{{ $t('login.createAccount') }}</router-link>
        </div>-->
    </el-form>
</template>

<script>
import account from '@/utils/account';
import tableConfig from '@/config/table.js';

export default {
    props: {
        // 是否对话框模式（用于重新登录时传入对应参数）
        dialogMode: {
            type: Boolean,
            default: () => {
                return false;
            }
        }
    },
    data() {
        return {
            loading: false,
            form: {
                phone: '',
                code: ''
            },
            rules: {
                phone: [{ required: true, message: '请输入手机号码' }],
                code: [{ required: true, message: '请输入短信验证码' }]
            },
            disabled: false,
            countdown: 0
        };
    },
    mounted() {},
    methods: {
        async getYzm() {
            const validate = await this.$refs.loginForm.validateField('phone').catch(() => {});
            if (!validate) {
                return false;
            }

            const testPhone = account.isMobile(this.form.phone);

            if (!testPhone) {
                return false;
            }

            // 账号密码登录
            const res = await this.$API.account.sendYzm.post({
                phone: this.form.phone
            });

            if (res.code === tableConfig.successCode) {
                this.$message.success('验证码发送成功');
                this.disabled = true;
                this.countdown = 60;
                const t = setInterval(() => {
                    this.countdown -= 1;
                    if (this.countdown < 1) {
                        clearInterval(t);
                        this.disabled = false;
                        this.countdown = 0;
                    }
                }, 1000);
            } else {
                this.$message.error(res.message);
            }
        },
        async login() {
            const validate = await this.$refs.loginForm.validate().catch(() => {});

            if (!validate) {
                return false;
            }

            const testPhone = this.testMobile(this.form.phone);

            if (!testPhone) {
                return false;
            }

            if (this.form.code === '') {
                this.$message.error('请输入验证码');
                return false;
            }

            // 手机号登录
            const res = await this.$API.account.login.post({
                type: 'phone',
                phone: this.form.phone,
                code: this.form.code
            });

            if (res && res.status === 1) {
                // 是否重定向
                const _redirect = !this.dialogMode;

                // 任意类型登录后置操作封装
                await account.loginSuccess(res, _redirect);

                this.$message.success('登录成功');
            } else {
                if (res.code > 10110) {
                    this.$message.error(res.message);
                }
            }

            // 隐藏掉loading
            this.loading = false;
        },
        /**
         * 限制登录手机号输入长度
         * @param e
         */
        loginLength(e) {
            if (e.length > 10) {
                this.form.phone = this.form.phone.slice(0, 11);
            }
        },
        /**
         * 验证手机号
         * @param number
         * @returns {boolean}
         */
        testMobile(number) {
            if (number === '' || number === undefined || number == null || number.length === 0) {
                this.$message.error('手机号不能为空');
                return false;
            } else if (account.isMobile(number) === false) {
                this.$message.error('手机号格式不正确');
                return false;
            } else {
                return true;
            }
        }
    }
};
</script>

<style></style>
