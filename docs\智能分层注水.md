---
title: 智能分层注水
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 智能分层注水

Base URLs:

* <a href="http://api.heli.cn">开发环境: http://api.heli.cn</a>

# Authentication

# heliCloud-agent/账号相关

## POST 用户登录（手机+验证码） 

POST /tenant/account/login

> Body 请求参数

```yaml
login_type: mobile
tenant_code: shengli001
mobile: "***********"
code: "2222"

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|object| 否 |none|
|» login_type|body|string| 是 |登录方式（mobile:手机验证码）|
|» tenant_code|body|string| 是 |租户编码|
|» mobile|body|string| 是 |用户名|
|» code|body|string| 是 |验证码（暂时随便输4-20位数字或字母）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 获取用户有权限管理的设备列表

POST /tenant/account/getDeviceList

> Body 请求参数

```yaml
page: 1
pageSize: 20
keyword: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» page|body|integer| 是 |页码（默认第1页）|
|» pageSize|body|integer| 否 |每页显示数（不填写则默认20条）|
|» keyword|body|string| 是 |搜索关键词（可搜索油井编码，油井名称，为空或者不传入该参数时不执行搜索）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 刷新换取新Token

POST /tenant/account/refreshToken

> Body 请求参数

```yaml
refresh_token: "{{REFRESH_TOKEN}}"

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» refresh_token|body|string| 否 |必须携带最初登录时获取到的refresh_token来换取新accessToken|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST emqx服务器鉴权

POST /tenant/mqtt/authenticate

> Body 请求参数

```yaml
clientid: xaxnh_mqttx_0e43a770
username: shengli001@zhangsan
password: "{{ACCESS_TOKEN}}"

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» clientid|body|string| 否 |none|
|» username|body|string| 否 |none|
|» password|body|string| 否 |必须携带最初登录时获取到的refresh_token来换取新accessToken|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST emqx服务器ACL检查

POST /tenant/mqtt/auth

> Body 请求参数

```yaml
username: zhangsan
password: "123456"
timestamp: "{{$date.timestamp}}"
signature: de5ad90ac8765a678861a61959ba9e09f3bc7da6d23ad38f332604a9012424d7

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» username|body|string| 否 |none|
|» password|body|string| 否 |none|
|» timestamp|body|string| 否 |none|
|» signature|body|string| 否 |必须携带最初登录时获取到的refresh_token来换取新accessToken|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 修改个人信息

POST /tenant/account/updateProfile

> Body 请求参数

```yaml
username: ""
realname: ""
avatar: ""
email: ""
mobile: ""
gender: ""
birthday: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» username|body|string| 否 |用户名|
|» realname|body|string| 否 |姓名|
|» avatar|body|string| 否 |头像|
|» email|body|string| 否 |邮箱|
|» mobile|body|string| 否 |手机号|
|» gender|body|string| 否 |性别|
|» birthday|body|string| 否 |生日|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 修改密码

POST /tenant/account/changePassword

> Body 请求参数

```yaml
old_password: e10adc3949ba59abbe56e057f20f883e
new_password: e10adc3949ba59abbe56e057f20f883e
confirm_password: e10adc3949ba59abbe56e057f20f883e

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» old_password|body|string| 否 |原密码（32位md5小写后的字符串）|
|» new_password|body|string| 否 |新密码（32位md5小写后的字符串）|
|» confirm_password|body|string| 否 |确认密码（32位md5小写后的字符串）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 退出登录

POST /tenant/account/logout

> Body 请求参数

```yaml
{}

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# heliCloud-agent/设备管理

## POST 设备列表

POST /tenant/device/list

> Body 请求参数

```yaml
page: 1
pageSize: 20
keyword: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» page|body|integer| 是 |页码（默认第1页）|
|» pageSize|body|integer| 否 |每页显示数（不填写则默认20条）|
|» keyword|body|string| 否 |搜索关键词（可搜索设备编码，设备名称，为空或者不传入该参数时不执行搜索）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 添加设备

POST /tenant/device/add

> Body 请求参数

```yaml
code: SLXA004
name: 胜利油井004
level: 8
location: 118.504100,37.544300
depth: 1200
status: 1

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» code|body|string| 是 |设备编码|
|» name|body|string| 是 |设备名称|
|» level|body|integer| 是 |层位数|
|» location|body|string| 否 |所在位置经纬度（地图组件拾取）|
|» depth|body|number| 否 |油井深度(米)|
|» status|body|integer| 是 |设备状态（0:停用,1:正常,2:异常,3:维修中）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 编辑设备

POST /tenant/device/edit

> Body 请求参数

```yaml
id: 10003
code: SLXA003
name: 胜利油井003
level: 6
location: 118.504100,37.544300
depth: 1000
status: 1

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 否 |设备ID编号|
|» code|body|string| 是 |设备编码|
|» name|body|string| 是 |设备名称|
|» level|body|integer| 是 |层位数|
|» location|body|string| 否 |所在位置经纬度（地图组件拾取）|
|» depth|body|number| 否 |油井深度(米)|
|» status|body|integer| 是 |设备状态（0:停用,1:正常,2:异常,3:维修中）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 设置设备状态

POST /tenant/device/status

> Body 请求参数

```yaml
id: 10003
status: 1

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 否 |设备ID编号|
|» status|body|integer| 是 |设备状态（0:停用,1:正常,2:异常,3:维修中）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST (软)删除设备

POST /tenant/device/delete

> Body 请求参数

```yaml
id: 10004

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 否 |设备ID编号|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# heliCloud-agent/用户管理

## POST 用户列表

POST /tenant/user/list

> Body 请求参数

```yaml
page: 1
pageSize: 20
keyword: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» page|body|integer| 是 |页码（默认第1页）|
|» pageSize|body|integer| 否 |每页显示数（不填写则默认20条）|
|» keyword|body|string| 否 |搜索关键词（可搜索用户名、姓名、手机号、邮箱号，为空或者不传入该参数时不执行搜索）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 用户添加

POST /tenant/user/add

> Body 请求参数

```yaml
username: "{{$internet.userName}}"
password: e10adc3949ba59abbe56e057f20f883e
realname: "{{$person.fullName}}"
mobile: "{{$phone.mobile}}"
email: "{{$internet.email}}"
avatar: "{{$image.avatar}}"
birthday: "{{$date.birthdate}}"

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» username|body|string| 是 |用户名|
|» password|body|string| 是 |密码（32位md5小写后的字符串）|
|» realname|body|string| 是 |姓名或昵称|
|» mobile|body|string| 是 |手机号（同一租户下必须唯一，可用于手机验证登录）|
|» email|body|string| 否 |邮箱号（同一租户下必须唯一，可用于邮箱验证登录）|
|» avatar|body|string| 否 |none|
|» birthday|body|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 用户编辑

POST /tenant/user/edit

> Body 请求参数

```yaml
id: 10007
username: zhaoqi
password: "123456"
realname: 赵七
mobile: "13800138005"
email: <EMAIL>

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 是 |用户ID|
|» username|body|string| 否 |用户名|
|» password|body|string| 否 |密码|
|» realname|body|string| 否 |姓名或昵称|
|» mobile|body|string| 否 |手机号（同一租户下必须唯一，可用于手机验证登录）|
|» email|body|string| 否 |邮箱号（同一租户下必须唯一，可用于邮箱验证登录）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 用户删除

POST /tenant/user/delete

> Body 请求参数

```yaml
id: 10006

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 是 |用户ID|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 用户分配角色

POST /tenant/user/assignRoles

> Body 请求参数

```yaml
id: 10001
role_ids: 1,2,3
"role_ids[]":
  - "1"
  - "2"
  - "3"
"special_permissions[1]":
  - read,write
"special_permissions[2]": read
"special_permissions[3]": read,delete

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 是 |用户ID|
|» role_ids|body|string| 否 |角色组IDS（字符串格式，用逗号分割）|
|» role_ids[]|body|array| 否 |角色组IDS（数组格式）|
|» special_permissions[1]|body|[string]| 否 |特殊权限（数组格式）|
|» special_permissions[2]|body|[string]| 否 |none|
|» special_permissions[3]|body|[string]| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 用户角色ids获取

POST /tenant/user/getRoleIds

> Body 请求参数

```yaml
id: 10001

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 是 |用户ID|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 用户启用

POST /tenant/user/enable

> Body 请求参数

```yaml
id: 10006

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 是 |用户ID|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 用户禁用

POST /tenant/user/disable

> Body 请求参数

```yaml
id: 10006

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 是 |用户ID|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# heliCloud-agent/角色权限

## POST 角色组列表

POST /tenant/role/list

> Body 请求参数

```yaml
page: 1
pageSize: 20
keyword: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» page|body|integer| 是 |页码（默认第1页）|
|» pageSize|body|integer| 否 |每页显示数（不填写则默认20条）|
|» keyword|body|string| 否 |搜索关键词（可搜索角色名、角色标识、角色描述，为空或者不传入该参数时不执行搜索）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 角色组添加

POST /tenant/role/add

> Body 请求参数

```yaml
name: 钻井2组
code: role002
description: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» name|body|string| 是 |角色名称|
|» code|body|string| 是 |角色标识|
|» description|body|string| 否 |角色描述|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 角色组编辑

POST /tenant/role/edit

> Body 请求参数

```yaml
id: 1
name: 钻井1组
code: role001
description: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 否 |ID 编号|
|» name|body|string| 是 |角色名称|
|» code|body|string| 是 |角色标识|
|» description|body|string| 否 |角色描述|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 角色组状态设置

POST /tenant/role/status

> Body 请求参数

```yaml
id: 1
status: 0

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 否 |ID 编号|
|» status|body|integer| 是 |角色状态（0：禁用，1：启用）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 角色组(软)删除

POST /tenant/role/delete

> Body 请求参数

```yaml
id: 1

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 否 |ID 编号|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 给角色分配设备

POST /tenant/role/assignDevices

> Body 请求参数

```yaml
id: 2
device_ids: 10001,10002,10003
"device_ids[]":
  - "10001"
  - "10002"
  - "10003"

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 是 |角色ID|
|» device_ids|body|string| 否 |设备IDS（字符串格式，用逗号分割）|
|» device_ids[]|body|array| 否 |设备IDS（数组格式，任选其一）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 获取角色已分配的设备IDS

POST /tenant/role/getAssignedDevices

> Body 请求参数

```yaml
id: 2

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» id|body|integer| 是 |角色ID|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## POST 获取可分配的设备列表

POST /tenant/role/getAssignableDevices

> Body 请求参数

```yaml
page: 1
pageSize: 20
keyword: ""

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |请求token|
|body|body|object| 否 |none|
|» page|body|integer| 是 |页码（默认第1页）|
|» pageSize|body|integer| 否 |每页显示数（不填写则默认20条）|
|» keyword|body|string| 否 |搜索关键词（可搜索设备编码，设备名称，为空或者不传入该参数时不执行搜索）|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 数据模型

