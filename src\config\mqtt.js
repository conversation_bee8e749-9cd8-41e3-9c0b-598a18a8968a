/**
 * MQTT配置文件
 * 包含连接选项、主题配置和自动订阅设置
 */

// 定义主题路由
const topicRoute = {
    // 系统消息通知主题（租户级别）
    notification: 'helio/tenant/{$tenantCode}/notification',
    // 用户消息通知主题（会自动匹配当前商户编码和用户id）
    userNotification: 'helio/tenant/{$tenantCode}/{$userId}/notification',
    // 设备心跳主题 - 接收当前商户下所有设备的响应（使用通配符订阅）
    deviceHeartbeat: 'helio/tenant/{$tenantCode}/device/+/heartbeat',
    // 客户端状态主题 - 用于发布当前客户端状态消息
    clientStatus: 'helio/tenant/{$tenantCode}/{$userId}/status',
};

export default {
    // MQTT服务器WebSocket地址
    // 注意：生产环境应该使用环境变量或配置文件动态获取
    serverUrl: 'ws://localhost:8083/mqtt',

    // 连接选项
    options: {
        // 客户端ID前缀（服务端连接要求为helio_，云端要求为cloud_，客户端要求为client_）
        clientIdPrefix: 'cloud_',

        // 是否使用清除会话
        clean: true,

        // 连接超时时间（毫秒）
        connectTimeout: 30 * 1000,

        // 重连间隔（毫秒）
        reconnectPeriod: 5000,

        // 心跳间隔（秒）
        keepalive: 60,

        // 默认QoS级别（0: 最多一次，1: 至少一次，2: 恰好一次）
        qos: 0,

        // 是否保留消息
        retain: false,

        // 最大重连次数
        maxReconnectAttempts: 3,

        // MQTT协议版本
        protocolVersion: 4,

        // 是否验证证书
        rejectUnauthorized: false,

        // 是否自动重新订阅
        resubscribe: true
    },

    // 主题相关配置（原mqttTopics.js的内容）
    topics: {
        // 是否启用自动订阅功能
        enableAutoSubscribe: true,

        // 主题路由定义
        topicRoute: topicRoute,

        // 自动订阅的主题列表
        // 支持通配符：+ (单层通配符) 和 # (多层通配符)
        // 支持变量替换：使用 {$变量名} 格式
        subscribeTopics: [
            topicRoute.notification,
            topicRoute.userNotification,
            topicRoute.deviceHeartbeat
        ]
    },

    // 消息持久化配置
    messagePersistence: {
        // 是否启用消息持久化（启用后会将所有MQTT消息保存到IndexedDB）
        enabled: true,

        // 保留的消息条数（超过此数量会自动删除旧消息）
        maxMessages: 10000,

        // 是否在控制台显示持久化相关日志
        enableLogging: true
    },

    // 客户端状态消息配置
    clientStatus: {
        // 是否启用客户端状态消息（启用后会发送上下线状态给对应主题）
        enabled: true,

        // 是否启用遗嘱消息（启用后会在断开连接时发送遗嘱消息，必须和enabled同时启用才有效）
        willEnabled: true,

        // 是否启用心跳消息（启用后会定时发送心跳消息）
        heartbeatEnabled: false,

        // 心跳间隔（毫秒）
        heartbeatInterval: 30 * 1000,

        // 客户端状态消息QoS级别（0, 1, 2）
        qos: 1,

        // 是否保留客户端状态消息
        retain: true,

        // 状态消息主题，使用预定义的主题路由
        // 该主题会在连接时动态替换 {$tenantCode} 和 {$userId} 为当前租户编码和用户ID
        // 例如：helio/tenant/shengli/789012/status
        topic: topicRoute.clientStatus,

        // 状态消息内容
        payload: {
            // 设备状态：online/offline/alive（alive为心跳）
            status: '',
            // 时间戳（毫秒）
            timestamp: 0, // 将在连接时动态设置为当前时间戳
            // 租户编码（与主题中的 {$tenantCode} 对应）
            tenantCode: '', // 将在连接时动态设置为当前租户编码
            // 用户ID（与主题中的 {$userId} 对应）
            userId: '', // 将在连接时动态设置为当前用户ID
            // 客户端类型（web, mobile, etc.）
            clientType: 'web',
        },

        // 遗嘱消息QoS级别（0, 1, 2）
        willQos: 1,

        // 是否保留遗嘱消息
        willRetain: true
    }
};
