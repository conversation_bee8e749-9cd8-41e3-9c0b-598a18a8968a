<template>
    <el-card :header="dialogMode ? '' : 修改密码" shadow="never" :style="dialogMode ? { border: 0 } : {}">
        <el-alert
            show-icon
            style="margin-bottom: 15px"
            title="密码重置后，您将被重定向到登录页面，您可以使用新密码重新登录。"
            type="info"
            :closable="false"
            v-if="dialogMode"
        />
        <el-form
            ref="form"
            :model="formData"
            :rules="rules"
            @keyup.enter="changePassword"
            label-width="120px"
            style="margin-top: 20px"
        >
            <el-form-item label="当前密码" prop="old_password" v-if="!dialogMode">
                <el-input
                    v-model="formData.old_password"
                    placeholder="请输入当前密码"
                    show-password
                    type="password"
                ></el-input>
                <div class="el-form-item-msg">必须提供当前登录用户密码才能进行更改</div>
            </el-form-item>
            <el-form-item label="新密码" prop="password">
                <el-input
                    v-model="formData.password"
                    placeholder="请输入新密码"
                    show-password
                    type="password"
                ></el-input>
                <sc-password-strength v-model="formData.password"></sc-password-strength>
                <div class="el-form-item-msg" v-if="!dialogMode">最好请输入包含英文、数字的8位以上密码</div>
            </el-form-item>
            <el-form-item label="确认新密码" prop="confirm_password">
                <el-input
                    v-model="formData.confirm_password"
                    placeholder="请再次输入新密码"
                    show-password
                    type="password"
                ></el-input>
            </el-form-item>
            <el-form-item :style="dialogMode ? { marginTop: '30px' } : {}">
                <el-button
                    type="primary"
                    :loading="loading"
                    @click="changePassword"
                    :size="dialogMode ? 'large' : 'default'"
                    :style="dialogMode ? { width: '100%' } : {}"
                    >{{ dialogMode ? '重置密码' : '保存密码' }}</el-button
                >
            </el-form-item>
        </el-form>
    </el-card>
</template>

<script>
import scPasswordStrength from '@/components/scPasswordStrength';
import account from '@/utils/account';
import { useGlobalStore } from '@/stores/global';

const globalStore = useGlobalStore();

export default {
    name: 'reset_password',
    props: {
        // 显示模式数据对象（用于重置密码对话框传入对应参数）
        showMode: {
            type: String,
            default: () => {
                return 'page'; // 默认为页面显示方式
            }
        }
    },
    components: {
        scPasswordStrength
    },
    data() {
        return {
            loading: false,
            formData: {
                old_password: '',
                password: '',
                confirm_password: ''
            },
            rules: {
                old_password: [{ required: true, message: '请输入当前密码' }],
                password: [{ required: true, message: '请输入新密码' }],
                confirm_password: [
                    { required: true, message: '请再次输入新密码' },
                    {
                        validator: (rule, value, callback) => {
                            if (value !== this.formData.password) {
                                callback(new Error('两次输入密码不一致'));
                            } else {
                                callback();
                            }
                        }
                    }
                ]
            }
        };
    },
    created() {
        // 是否对话框模式
        this.dialogMode = this.showMode !== 'page';

        // 对话框模式时删除原密码验证
        if (this.dialogMode) {
            delete this.rules.old_password;
        }
    },
    methods: {
        /**
         * 修改密码提交
         * @returns {Promise<void>}
         */
        async changePassword() {
            const validate = await this.$refs.form.validate().catch(() => {});
            if (!validate) {
                return false;
            }

            this.loading = true;

            const _formData = {
                old_password: this.$TOOL.crypto.MD5(this.formData.old_password),
                password: this.$TOOL.crypto.MD5(this.formData.password),
                confirm_password: this.$TOOL.crypto.MD5(this.formData.confirm_password)
            };

            // 对话框模式时删除原密码字段
            if (this.dialogMode) {
                delete _formData.old_password;
            }

            // 提交数据
            const res = await this.$API.account.changePassword.post(_formData);

            if (res.status === 1) {
                if (this.dialogMode) {
                    this.$message.success('重置成功，请重新登录！');

                    // 更新缓存中的字段值
                    account.userInfo.set({ force_change_password: 0 });

                    globalStore.SET_userResetPassword(false);

                    // 退出重新登录
                    this.$router.replace({ path: '/login' });
                } else {
                    this.$message.success('修改成功');
                }
            } else {
                this.$message.warning(res.message);
            }

            this.loading = false;
        }
    }
};
</script>

<style lang="scss" scoped></style>
