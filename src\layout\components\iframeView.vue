<!--
 * @Descripttion: 处理iframe持久化，涉及store(pinia)
-->
<template>
    <div v-show="$route.meta.type === 'iframe'" class="iframe-pages">
        <iframe
            v-for="item in iframeList"
            v-show="$route.meta.url === item.meta.url"
            :key="item.meta.url"
            :src="item.meta.url"
            frameborder="0"
        ></iframe>
    </div>
</template>

<script>
import { useLayoutStore } from '@/store/layout';
import { useIframeStore } from '@/store/iframe';

export default {
    data() {
        return {};
    },
    watch: {
        $route(e) {
            this.push(e);
        }
    },
    created() {
        this.push(this.$route);
    },
    computed: {
        iframeList() {
            return useIframeStore().iframeList;
        },
        appLayoutMobile() {
            return useLayoutStore().appLayoutMobile;
        },
        appShowTabs() {
            return useLayoutStore().appShowTabs;
        }
    },
    mounted() {},
    methods: {
        push(route) {
            if (route.meta.type === 'iframe') {
                if (this.appLayoutMobile || !this.appShowTabs) {
                    useIframeStore().setIframeList(route);
                } else {
                    useIframeStore().pushIframeList(route);
                }
            } else {
                if (this.appLayoutMobile || !this.appShowTabs) {
                    useIframeStore().clearIframeList();
                }
            }
        }
    }
};
</script>

<style scoped>
.iframe-pages {
    width: 100%;
    height: 100%;
}

iframe {
    border: 0;
    width: 100%;
    height: 100%;
    display: block;
}
</style>
