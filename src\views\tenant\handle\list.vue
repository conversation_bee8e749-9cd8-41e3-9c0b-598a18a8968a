<template>
    <el-container>
        <el-header>
            <div class="left-panel">
                <h3>注水设备管理</h3>
            </div>
            <div class="right-panel">
                <div class="right-panel-search">
                    <el-input
                        v-model="searchParams.keyword"
                        clearable
                        placeholder="设备名称 / 设备标识"
                        @keyup.enter="handleSearch"
                        @clear="handleSearch"
                    ></el-input>
                    <el-button icon="el-icon-search" type="primary" color="#1C409A" @click="handleSearch"></el-button>
                </div>
            </div>
        </el-header>
        <el-main class="p0">
            <device-list-status-manager
                ref="deviceListStatusManager"
                :device-ids="currentPageDeviceIds"
                @online-status-change="handleDeviceOnlineStatusChange"
            >
                <device-selector
                    ref="deviceSelector"
                    :apiObj="apiObj"
                    :search-params="searchParams"
                    :showSearchBar="false"
                    :row-click-select="false"
                    :showOnlineStatus="true"
                    @selection-change="handleSelectionChange"
                    @table-load="handleTableLoad"
                >
                    <template #additional-columns>
                        <el-table-column label="操作" width="180" align="center" fixed="right">
                            <template #default="scope">
                                <template v-if="scope.row && scope.row.id">
                                    <el-button
                                        v-auths="['tenant.device.view']"
                                        size="small"
                                        type="info"
                                        @click="view(scope.row)"
                                        color="#32aa66"
                                        >查看
                                    </el-button>
                                    <el-button
                                        v-auths="['tenant.device.operate']"
                                        size="small"
                                        type="primary"
                                        @click="operate(scope.row)"
                                        :disabled="!canOperateDevice(scope.row)"
                                        color="#1C409A"
                                        >监控
                                    </el-button>
                                    <el-button
                                        v-auths="['tenant.device.view']"
                                        size="small"
                                        type="warning"
                                        @click="viewHistory(scope.row)"
                                        color="#623899"
                                        >历史记录
                                    </el-button>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </device-selector>
            </device-list-status-manager>
        </el-main>
    </el-container>
    <device-detail v-model="deviceDetailVisible" :device="currentDevice" />
</template>

<script>
import DeviceSelector from '../common/DeviceSelector.vue';
import DeviceDetail from '../common/DeviceDetail.vue';
import DeviceListStatusManager from '../common/DeviceListStatusManager.vue';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';

export default {
    name: 'tenant.handle.list',
    components: {
        DeviceSelector,
        DeviceDetail,
        DeviceListStatusManager
    },
    setup() {
        // 获取 Pinia store
        const deviceStore = useDeviceOnlineStore();

        return {
            deviceStore
        };
    },
    data() {
        return {
            apiObj: this.$API.tenant.device.myList,
            searchParams: {
                keyword: ''
            },
            selection: [],
            deviceDetailVisible: false,
            currentDevice: null,
            currentPageDeviceIds: [] // 当前页面设备ID列表
        };
    },
    computed: {
        // 设备在线状态映射，用于UI渲染
        deviceOnlineMap() {
            const result = {};
            // 为当前页面的所有设备ID创建在线状态映射
            for (const deviceId of this.currentPageDeviceIds) {
                // 直接从 Pinia store 获取最新状态
                result[deviceId] = this.deviceStore.isDeviceOnline(deviceId);
            }
            return result;
        }
    },
    mounted() {
        // 状态检查由 DeviceListStatusManager 统一管理
    },
    methods: {
        /**
         * 处理搜索
         */
        handleSearch() {
            this.$refs.deviceSelector.refresh();
        },

        /**
         * 处理表格数据加载
         * @param {Array} data 表格数据
         */
        handleTableLoad(data) {
            if (!Array.isArray(data)) {
                // 无效的表格数据
                return;
            }

            // 更新当前页面设备ID列表
            this.currentPageDeviceIds = data
                .filter(item => item && typeof item.id !== 'undefined')
                .map(item => Number(item.id));

            // 当前页面设备ID列表已更新

            // 状态检查由 DeviceListStatusManager 统一管理
            // 当 currentPageDeviceIds 更新后，DeviceListStatusManager 会自动获取状态
        },

        /**
         * 处理选择变更
         * @param {Array} selection 选中的设备ID数组
         */
        handleSelectionChange(selection) {
            if (!Array.isArray(selection)) {
                // 无效的选择数据
                return;
            }

            // 过滤掉无效的设备数据
            this.selection = selection
                .filter(item => item && typeof item.id !== 'undefined')
                .map(item => Number(item.id));
        },

        /**
         * 查看设备详情
         * @param {Object} row 设备信息
         */
        view(row) {
            this.currentDevice = row;
            this.deviceDetailVisible = true;
        },

        /**
         * 操作设备
         * @param {Object} row 设备信息
         */
        operate(row) {
            this.$router.push(`/tenant/handle/device/${row.id}`);
        },

        /**
         * 查看设备历史记录
         * @param {Object} row 设备信息
         */
        viewHistory(row) {
            this.$router.push(`/tenant/handle/device/${row.id}/history`);
        },

        /**
         * 检查设备是否在线
         * @param {number} deviceId 设备ID
         * @returns {boolean} 是否在线
         */
        isDeviceOnline(deviceId) {
            if (!deviceId) {
                return false;
            }

            // 直接从 Pinia store 获取最新状态
            return this.deviceStore.isDeviceOnline(deviceId);
        },

        /**
         * 检查设备是否可以操作
         * @param {Object} device 设备信息
         * @returns {boolean} 是否可以操作
         */
        canOperateDevice(device) {
            if (!device || typeof device !== 'object' || typeof device.id === 'undefined' || typeof device.status === 'undefined') {
                return false;
            }

            const isOnline = this.isDeviceOnline(device.id);
            const isEnabled = device.status === 1;

            return isEnabled && isOnline;
        }
    }
};
</script>

<style lang="scss" scoped>
.el-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.left-panel {
    display: flex;
    gap: 10px;
}

.right-panel-search {
    display: flex;
    align-items: center;
}

.p0 {
    padding: 0;
}
</style>
