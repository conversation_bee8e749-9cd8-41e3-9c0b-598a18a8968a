import * as echarts from 'echarts';
import { markRaw } from 'vue';

// 常量配置
const CONFIG = {
    DATA_POINTS: 10,
    RANGES: {
        innerPressure: { min: 22.0, max: 23.0, unit: 'MPa' },
        outerPressure: { min: 18.0, max: 19.0, unit: 'MPa' },
        flow: { min: 120.0, max: 130.0, unit: 'm³/d' },
        temperature: { min: 48.0, max: 50.0, unit: '℃' },
        valveOpening: { min: 20.0, max: 20.0, unit: '%' }
    }
};

/**
 * 初始化echarts图表
 * @param chartRefEl
 * @param chartOptions
 */
export function initECharts(chartRefEl: any, chartOptions: any) {
    // 初始化图表对象
    const chartInstance = echarts.init(chartRefEl);
    // 防止对象被转换为响应式
    const myChart = markRaw(chartInstance);

    // 加载loading
    myChart.showLoading({
        text: '正在加载...',
        color: '#888',
        textColor: '#888',
        maskColor: 'rgba(255, 255, 255, 0.25)',
        fontSize: 12,
        showSpinner: true,
        spinnerRadius: 10,
        lineWidth: 3,
    });

    myChart.setOption(chartOptions);
    return myChart;
}

/**
 * 生成测试数据
 * @param type 数据类型
 * @param length 数据长度
 */
function generateDataByType(type: string, length: number = CONFIG.DATA_POINTS): number[] {
    const range = CONFIG.RANGES[type];
    if (!range) {
        console.error(`Unknown data type: ${type}`);
        return [];
    }
    return generateRandomData(length, range.min, range.max);
}

/**
 * 更新测试图表数据
 */
export function updateChartDataForTest(myChart: any, chartData: any, chartOption: any) {
    if (!myChart) return;

    if (chartData.timeData.length === 0) {
        // 初始化数据
        chartData.timeData = getTimeData();
        chartData.innerPressureData = generateDataByType('innerPressure');
        chartData.outerPressureData = generateDataByType('outerPressure');
        chartData.flowData = generateDataByType('flow');
        chartData.temperatureData = generateDataByType('temperature');
        chartData.valveOpeningData = generateDataByType('valveOpening');
    } else {
        // 更新数据
        const newTimeData = getTimeData().pop();
        chartData.timeData.push(newTimeData);
        chartData.timeData.shift();

        ['innerPressure', 'outerPressure', 'flow', 'temperature', 'valveOpening'].forEach(type => {
            const newValue = generateDataByType(type, 1)[0];
            chartData[`${type}Data`].push(newValue);
            chartData[`${type}Data`].shift();
        });
    }

    // 更新图表数据
    chartOption.xAxis[0].data = chartData.timeData;
    chartOption.series[0].data = chartData.innerPressureData;
    chartOption.series[1].data = chartData.outerPressureData;
    chartOption.series[2].data = chartData.flowData;
    chartOption.series[3].data = chartData.temperatureData;
    chartOption.series[4].data = chartData.valveOpeningData;

    // 更新副标题
    chartOption.title.subtext = formatSubtitle(chartData);

    myChart.hideLoading();
    myChart.setOption(chartOption);
}

/**
 * 格式化副标题
 */
function formatSubtitle(chartData: any): string {
    const averages = {
        flow: calculateAverage(chartData.flowData),
        innerPressure: calculateAverage(chartData.innerPressureData),
        outerPressure: calculateAverage(chartData.outerPressureData),
        valveOpening: calculateAverage(chartData.valveOpeningData)
    };

    return `{flowValue|流量值：${averages.flow.toFixed(2)} ${CONFIG.RANGES.flow.unit}}，` +
           `{innerPressureValue|内压值：${averages.innerPressure.toFixed(2)} ${CONFIG.RANGES.innerPressure.unit}}，` +
           `{outerPressureValue|外压值：${averages.outerPressure.toFixed(2)} ${CONFIG.RANGES.outerPressure.unit}}，` +
           `{valveOpeningValue|水嘴开度：${averages.valveOpening.toFixed(2)} ${CONFIG.RANGES.valveOpening.unit}}`;
}

/**
 * 切换全屏
 * @param chartRefEl
 * @param myChart
 */
export function toggleFullScreen(chartRefEl: any, myChart: any) {
    if (!document.fullscreenElement) {
        if (chartRefEl.requestFullscreen) {
            chartRefEl.requestFullscreen();
        } else if (chartRefEl.mozRequestFullScreen) { // Firefox
            chartRefEl.mozRequestFullScreen();
        } else if (chartRefEl.webkitRequestFullscreen) { // Chrome, Safari and Opera
            chartRefEl.webkitRequestFullscreen();
        } else if (chartRefEl.msRequestFullscreen) { // IE/Edge
            chartRefEl.msRequestFullscreen();
        }
        myChart.setOption({
            backgroundColor: '#ffffff'
        });
        document.addEventListener('fullscreenchange', () => handleFullScreenChange(myChart));
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
        myChart.setOption({
            backgroundColor: 'transparent'
        });
        document.removeEventListener('fullscreenchange', () => handleFullScreenChange(myChart));
    }
}

/**
 * 全屏事件回调
 * @param myChart
 */
export function handleFullScreenChange(myChart: any) {
    if (document.fullscreenElement) {
        myChart.resize();
    }
}

/**
 * 将对应的层级转换成中文
 */
export function chineseLayer(index: number) {
    const numToChineseMap = {
        0: '零',
        1: '一',
        2: '二',
        3: '三',
        4: '四',
        5: '五',
        6: '六',
        7: '七',
        8: '八',
        9: '九',
        10: '十'
    };
    return numToChineseMap[index] || index.toString();
}

/**
 * 计算一组数据的平均值
 * @param data
 */
function calculateAverage(data: Array<number>) {
    if (data.length === 0) return 0;
    const sum = data.reduce((acc, val) => acc + val, 0);
    return sum / data.length;
}

/**
 * 测试横向轴时间段生成
 */
function getTimeData() {
    const currentTime = new Date();
    const timeData: string[] = [];
    for (let i = 0; i < 10; i++) {
        const secondsAgo = currentTime.getTime() - i * 1000;
        const time = new Date(secondsAgo);
        const year = time.getFullYear();
        const month = (time.getMonth() + 1).toString().padStart(2, '0');
        const day = time.getDate().toString().padStart(2, '0');
        const hours = time.getHours().toString().padStart(2, '0');
        const minutes = time.getMinutes().toString().padStart(2, '0');
        const seconds = time.getSeconds().toString().padStart(2, '0');
        timeData.push(`${year}-${month}-${day} ${hours}:${minutes}:${seconds}`);
    }
    return timeData.reverse();
}

/**
 * 测试数据统一生成
 * @param length
 * @param minValue
 * @param maxValue
 */
function generateRandomData(length: number, minValue: number, maxValue: number): Array<number> {
    const data: Array<number> = [];
    for (let i = 0; i < length; i++) {
        const randomValue = Math.random() * (maxValue - minValue) + minValue;
        data.push(Number(randomValue.toFixed(1)));
    }
    return data;
}
