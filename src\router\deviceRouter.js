// 设备管理路由配置

const routes = [
    {
        path: '/tenant/handle/device',
        component: () => import('@/views/tenant/handle/device.vue'),
        meta: {
            title: '设备管理'
        },
        children: [
            {
                path: ':id',
                component: () => import('@/views/tenant/handle/components/well/index.vue'),
                meta: {
                    title: '设备监控'
                }
            },
            {
                path: ':id/layer/:layer',
                component: () => import('@/views/tenant/handle/components/well/wellDetail.vue'),
                meta: {
                    title: '分层详情'
                }
            }
        ]
    }
];

export default routes;
