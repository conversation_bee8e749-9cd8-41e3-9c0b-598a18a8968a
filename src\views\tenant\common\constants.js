// 设备业务状态映射（与设备在线状态不同，这是业务层面的状态）
export const deviceStatusMap = {
    type: {
        0: 'warning',
        1: 'success',
        2: 'danger',
        3: 'info'
    },
    text: {
        0: '停用',
        1: '正常',
        2: '异常',
        3: '维修'
    }
};

// 设备业务状态工具类（与在线状态工具类不同）
export const deviceBusinessStatusUtils = {
    getDeviceStatusType(status) {
        return deviceStatusMap.type[status] || 'info';
    },
    getDeviceStatusText(status) {
        return deviceStatusMap.text[status] || '未知';
    },
    getDeviceStatusOptions() {
        return Object.entries(deviceStatusMap.text).map(([value, text]) => ({
            text,
            value: Number(value),
            type: deviceStatusMap.type[value]
        }));
    }
};
