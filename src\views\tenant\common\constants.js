export const deviceStatusMap = {
    type: {
        0: 'warning',
        1: 'success',
        2: 'danger',
        3: 'info'
    },
    text: {
        0: '停用',
        1: '正常',
        2: '异常',
        3: '维修'
    }
};

export const deviceStatusUtils = {
    getDeviceStatusType(status) {
        return deviceStatusMap.type[status] || 'info';
    },
    getDeviceStatusText(status) {
        return deviceStatusMap.text[status] || '未知';
    },
    getDeviceStatusOptions() {
        return Object.entries(deviceStatusMap.text).map(([value, text]) => ({
            text,
            value: Number(value),
            type: deviceStatusMap.type[value]
        }));
    }
};
