<template>
    <div :ref="`chart${layerInfo.layer}`" class="charts-main"></div>
</template>

<script>
import { initECharts, updateChartDataForTest, toggleFullScreen } from './chartUtils';
import { chartOptions } from './chartOptions';
import { deepMerge } from '@/utils/object';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';

export default {
    name: "wellChart",
    setup() {
        // 获取 Pinia store
        const deviceStore = useDeviceOnlineStore();

        return {
            deviceStore
        };
    },
    props: {
        wellInfo: {
            type: Object,
            required: true,
            default: () => ({
                id: 0,
                name: '',
                level: 0
            })
        },
        layerInfo: {
            type: Object,
            required: true,
            default: () => ({
                layer: 0,
                name: ''
            })
        },
        customOptions: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            myChart: null,
            chartRefEl: null,
            chartOptions: [],
            chartData: {
                timeData: [],
                types: ['innerPressure', 'outerPressure', 'flow', 'temperature', 'valveOpening'],
                data: {}
            },
            resizeHandler: null,
            lastUpdateTime: new Date(),
            updateInterval: null
        };
    },
    computed: {
        // 从 Pinia store 获取设备在线状态
        deviceOnline() {
            return this.deviceStore.isDeviceOnline(this.wellInfo.id);
        }
    },
    watch: {
        // 监听设备在线状态变化
        deviceOnline(newOnline, oldOnline) {
            if (newOnline !== oldOnline) {
                console.log(`图表组件接收到设备 ${this.wellInfo.id} 状态变更:`, newOnline ? '在线' : '离线');

                // 如果设备重新上线，恢复数据更新
                if (newOnline && !this.updateInterval) {
                    this.startDataRefresh();
                }
                // 如果设备离线，停止数据更新
                else if (!newOnline && this.updateInterval) {
                    this.stopDataRefresh();
                }
            }
        }
    },
    mounted() {
        console.log('第' + this.layerInfo.layer + '层组件已挂载.');
        this.initChart();
        this.startDataRefresh();
    },
    beforeUnmount() {
        console.log("beforeUnmount::", this.layerInfo.layer);
        this.stopDataRefresh();
        window.removeEventListener('resize', this.handleResize);
        if (this.resizeHandler) {
            clearTimeout(this.resizeHandler);
        }
        if (this.myChart) {
            this.myChart.dispose();
            this.myChart = null;
        }
    },
    methods: {
        async initChart() {
            await this.$nextTick();

            const chartRef = `chart${this.layerInfo.layer}`;
            this.chartRefEl = this.$refs[chartRef];

            if (this.chartRefEl) {
                let chartOption = JSON.parse(JSON.stringify(chartOptions));

                this.chartData.types.forEach(type => {
                    this.chartData.data[type] = [];
                });

                if (chartOption.toolbox?.feature) {
                    this.bindToolboxEvents(chartOption.toolbox.feature);
                }

                chartOption = deepMerge(chartOption, this.customOptions);
                this.chartOptions.push(chartOption);
                this.myChart = initECharts(this.chartRefEl, chartOption);
                this.updateChartData();

                this.handleResize();
                window.addEventListener('resize', this.handleResize);

                this.$emit('chartInitComplete', this.layerInfo.layer);
            } else {
                console.error(`无法找到对应ref为${chartRef}的DOM元素`);
            }
        },
        startDataRefresh() {
            this.updateInterval = setInterval(() => {
                // 只有在设备在线时才更新图表数据
                if (this.deviceOnline) {
                    const currentTime = new Date();
                    if (currentTime.getTime() - this.lastUpdateTime.getTime() >= 1000) {
                        this.updateChartData();
                        this.lastUpdateTime = currentTime;
                        this.$forceUpdate();
                    }
                }
            }, 100);
        },
        stopDataRefresh() {
            if (this.updateInterval) {
                clearInterval(this.updateInterval);
                this.updateInterval = null;
            }
        },
        bindToolboxEvents(feature) {
            if (feature.myFullScreen) {
                feature.myFullScreen.onclick = () => this.toggleFullScreen();
            }
            if (feature.myViewInfo) {
                feature.myViewInfo.onclick = () => this.viewDetails();
            }
        },
        updateChartData() {
            // 如果设备离线，不更新图表数据
            if (!this.deviceOnline) return;

            const chartOption = this.chartOptions.find(option =>
                option.title.text === `第${this.layerInfo.name}层`
            );
            if (chartOption) {
                updateChartDataForTest(this.myChart, this.chartData, chartOption);
            }
        },
        handleResize() {
            if (this.resizeHandler) {
                clearTimeout(this.resizeHandler);
            }
            this.resizeHandler = setTimeout(() => {
                if (this.myChart) {
                    this.myChart.resize();
                }
            }, 10);
        },
        viewDetails() {
            this.$router.push(`/tenant/handle/device/${this.wellInfo.id}/layer/${this.layerInfo.layer}`);
        },
        toggleFullScreen() {
            toggleFullScreen(this.chartRefEl, this.myChart);
        }
    }
};
</script>

<style scoped lang="scss">
.charts-main {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
}
</style>
