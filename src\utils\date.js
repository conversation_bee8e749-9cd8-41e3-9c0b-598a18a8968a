/**
 * 将时间戳格式化为指定格式的日期字符串
 * @param {number} timestamp - 毫秒级时间戳
 * @param {string} [format='YYYY/MM/DD HH:mm:ss'] - 输出格式
 * @returns {string} 格式化后的日期字符串
 */
export function formatTimestamp(timestamp, format = 'YYYY/MM/DD HH:mm:ss') {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}
