<template>
    <div class="user-bar">
        <!--<div class="panel-item hidden-sm-and-down" @click="search">
            <el-icon class="el-icon&#45;&#45;left"><el-icon-search /></el-icon>
            <span>搜索</span>
        </div>-->

        <div class="panel-item fullscreen" @click="fullscreen">
            <el-icon class="el-icon--left"><sc-icon-fullscreen /></el-icon>
            <span>全屏</span>
        </div>
        <div class="panel-item theme" @click="settingAppLayout">
            <el-icon class="el-icon--left"><sc-icon-theme /></el-icon>
            <span>主题</span>
        </div>
        <notification></notification>
        <el-dropdown class="panel-item userinfo" trigger="hover" show-timeout="0" @command="handleUser">
            <div class="user-avatar">
                <el-avatar :size="30" :src="userInfo.avatar" :class="userInfo.gender_class"></el-avatar>
                <span>{{ userInfo.realname }}</span>
                <el-icon class="el-icon--right" style="bottom: 1px">
                    <el-icon-caret-bottom />
                </el-icon>
            </div>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item command="uc">账号信息</el-dropdown-item>
                    <el-dropdown-item command="clearCache">清除缓存</el-dropdown-item>
                    <el-dropdown-item command="reloadPage">重载页面</el-dropdown-item>
                    <el-dropdown-item command="outLogin" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
        <div class="userbar-right"></div>
    </div>

    <el-dialog v-model="searchVisible" :width="700" title="搜索" center destroy-on-close>
        <search @success="searchVisible = false"></search>
    </el-dialog>

    <el-drawer v-model="tasksVisible" :size="450" title="任务中心" destroy-on-close>
        <tasks></tasks>
    </el-drawer>

    <!--用户登录对话框（teleport可以让对话框加载到body中）-->
    <teleport to="body">
        <reLoginDialog></reLoginDialog>
    </teleport>



    <!--用户密码重置对话框-->
    <teleport to="body">
        <resetPasswordDialog></resetPasswordDialog>
    </teleport>
</template>

<script>
import { useGlobalStore } from '@/stores/global';
import { ref, computed } from 'vue';
import search from './search.vue';
import tasks from './tasks.vue';
import notification from './notification.vue';
import account from '@/utils/account';
import reLoginDialog from '@/views/account/reLoginDialog';
import resetPasswordDialog from '@/views/account/resetPasswordDialog';

const globalStore = useGlobalStore();

export default {
    components: {
        search,
        tasks,
        notification,
        reLoginDialog,
        resetPasswordDialog
    },
    data() {
        return {
            userInfo: {
                username: 'Anonymous',
                realname: '匿名用户',
                gender: 1,
                gender_class: 'unknown',
                avatar: 'img/icons/avatar-m.svg'
            },
            searchVisible: false,
            tasksVisible: false
        };
    },
    created() {
        // 查询获取一次最新的用户缓存信息
        this.handleUpdateUserInfoSuccess();
    },
    mounted() {
        if (this.userInfo && this.userInfo.force_change_password === 1) {
            globalStore.SET_userResetPassword(true);
        }
    },
    computed: {
        // 判断全局是否显示重置密码对话框
        isShowResetPasswordDialog() {
            return globalStore.userResetPassword;
        },
        // 判断全局是否更新了用户信息
        isUpdatedUserInfo() {
            return globalStore.userInfoLastUpdateTime;
        }
    },
    watch: {
        /**
         * 监听全局是否需要显示密码重置对话框
         * @param newVal
         * @param oldVal
         */
        isShowResetPasswordDialog(newVal, oldVal) {
            console.info(newVal, oldVal);
        },

        /**
         * 监听全局是否更新了用户信息
         * @param thisTimer
         * @param lastTimer
         */
        isUpdatedUserInfo(thisTimer, lastTimer) {
            if (thisTimer !== lastTimer) {
                // 查询获取一次最新的用户缓存信息
                this.handleUpdateUserInfoSuccess();
            }
        }
    },
    methods: {
        /**
         * 清缓存退出操作
         */
        async clearCache() {
            const loading = this.$loading();
            await account.clearUserCaches();
            setTimeout(() => {
                loading.close();
                location.reload();
            }, 1000);
        },
        /**
         * 个人操作
         * @param command
         */
        handleUser(command) {
            if (command === 'uc') {
                this.$router.push({ path: '/profile/index' });
            }
            if (command === 'cmd') {
                this.$router.push({ path: '/cmd' });
            }
            if (command === 'clearCache') {
                this.$confirm('清除缓存会将系统初始化，包括登录状态、主题、语言设置等，是否继续？', '提示', {
                    type: 'info'
                })
                    .then(async () => {
                        // 清缓存退出操作
                        await this.clearCache();
                    })
                    .catch(() => {
                        //取消
                    });
            }
            if (command === 'reloadPage') {
                return window.location.reload();
            }
            if (command === 'outLogin') {
                return account.logoutSystem();
            }
        },

        /**
         * 全屏方法
         */
        fullscreen() {
            const element = document.documentElement;
            this.$TOOL.fullscreen(element);
        },
        /**
         * 搜索
         */
        search() {
            this.searchVisible = true;
        },
        /**
         * 任务
         */
        tasks() {
            this.tasksVisible = true;
        },
        /**
         * 打开主题设置
         */
        settingAppLayout() {
            this.$emit('settingAppLayoutMethod');
        },
        /**
         * 子组件更新完用户信息后的触发
         */
        handleUpdateUserInfoSuccess() {
            // 查询获取一次最新的用户缓存信息
            const userInfo = account.userInfo.get() || {};

            // 设置默认值
            this.userInfo = {
                username: userInfo.username || 'Anonymous',
                realname: userInfo.realname || '匿名用户',
                gender: userInfo.gender || 1,
                gender_class: 'unknown',
                avatar: 'img/icons/avatar-m.svg',
                ...userInfo
            };

            // 根据性别获取默认头像信息
            const avatar_info = account.getDefaultAvatarInfo(this.userInfo.gender);

            // 头像地址
            this.userInfo.avatar = avatar_info?.url || 'img/icons/avatar-m.svg';
            // 性别class
            this.userInfo.gender_class = avatar_info?.class || 'unknown';
        }
    }
};
</script>

<style lang="scss" scoped>
// 重置父级line-height影响高度不一致
.el-dropdown {
    line-height: inherit;
}
.el-avatar {
    --el-avatar-bg-color: rgba(255, 255, 255, 1);
}
.user-bar {
    align-items: center;
    display: flex;
    height: 100%;

    .panel-item {
        align-items: center;
        cursor: pointer;
        display: flex;
        height: 100%;
        border-radius: var(--main-header-item-radius);
        padding: var(--main-header-item-padding);

        &:hover {
            background: var(--main-header-item-hover-bg);
        }
    }

    .user-avatar {
        align-items: center;
        display: flex;

        span {
            margin-left: 5px;
        }

        .el-avatar {
            padding: 5px;
            &.woman {
                background-color: #ff8989;
            }
            &.man {
                background-color: #1760c9;
            }
            &.unknown {
                background-color: #e5e5e5;
            }
        }
    }

    .el-icon--right {
        line-height: inherit;
        margin-left: 2px;
    }
}

.userbar-right {
    display: flex;
    align-items: center;
}
</style>
