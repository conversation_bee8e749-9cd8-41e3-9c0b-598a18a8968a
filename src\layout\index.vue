<template>
    <!-- 通栏布局 -->
    <template v-if="appLayout === 'header'">
        <header class="layout-header">
            <div class="layout-header-left">
                <div class="logo-bar">
                    <img class="logo" src="@/assets/img/logo-r.svg" />
                    <span>{{ $CONFIG.APP_NAME }}</span>
                </div>
                <ul v-if="!appLayoutMobile" class="nav">
                    <li
                        v-for="item in menu"
                        :key="item"
                        :class="pmenu.path === item.path ? 'active' : ''"
                        @click="showMenu(item)"
                    >
                        <el-icon><component :is="item.meta.icon || 'el-icon-menu'" /></el-icon>
                        <span>{{ item.meta.title }}</span>
                    </li>
                </ul>
            </div>
            <div class="layout-header-right">
                <userBar @settingAppLayoutMethod="settingAppLayout"></userBar>
            </div>
        </header>
        <section class="layout-wrapper">
            <div
                v-if="(!appLayoutMobile && nextMenu.length > 0) || !pmenu.component"
                :cus="nextMenu.length"
                :class="appMenuCollapse ? 'layout-side isCollapse' : 'layout-side'"
            >
                <div v-if="!appMenuCollapse" class="layout-side-top">
                    <h2>{{ pmenu.meta && pmenu.meta.title }}</h2>
                </div>
                <div class="layout-side-scroll">
                    <el-scrollbar>
                        <el-menu
                            :collapse="appMenuCollapse"
                            :default-active="active"
                            :unique-opened="$CONFIG.MENU_UNIQUE_OPENED"
                            router
                        >
                            <NavMenu :navMenus="nextMenu"></NavMenu>
                        </el-menu>
                    </el-scrollbar>
                </div>
                <div class="layout-side-bottom" @click="useLayoutStore().setAppMenuCollapse()">
                    <el-icon><el-icon-expand v-if="appMenuCollapse" /><el-icon-fold v-else /></el-icon>
                </div>
            </div>
            <Side-m v-if="appLayoutMobile"></Side-m>
            <div class="layout-body el-container">
                <TopBar v-if="!appLayoutMobile" :appLayout="appLayout"></TopBar>
                <mainTabs ref="mainTabs" v-if="!appLayoutMobile && appShowTabs"></mainTabs>
                <div id="layout-main" class="layout-main">
                    <router-view v-slot="{ Component }">
                        <keep-alive :include="useKeepAliveStore().keepLiveRoute">
                            <component :is="Component" v-if="useKeepAliveStore().routeShow" :key="$route.fullPath" />
                        </keep-alive>
                    </router-view>
                    <iframe-view></iframe-view>
                </div>
            </div>
        </section>
    </template>

    <!-- 经典布局 -->
    <template v-else-if="appLayout === 'menu'">
        <header class="layout-header">
            <div class="layout-header-left">
                <div class="logo-bar">
                    <img class="logo" src="@/assets/img/logo-r.svg" />
                    <span>{{ $CONFIG.APP_NAME }}</span>
                </div>
            </div>
            <div class="layout-header-right">
                <userBar @settingAppLayoutMethod="settingAppLayout"></userBar>
            </div>
        </header>
        <section class="layout-wrapper">
            <div v-if="!appLayoutMobile" :class="appMenuCollapse ? 'layout-side isCollapse' : 'layout-side'">
                <div class="layout-side-scroll">
                    <el-scrollbar>
                        <el-menu
                            :collapse="appMenuCollapse"
                            :default-active="active"
                            :unique-opened="$CONFIG.MENU_UNIQUE_OPENED"
                            router
                        >
                            <NavMenu :navMenus="menu"></NavMenu>
                        </el-menu>
                    </el-scrollbar>
                </div>
                <div class="layout-side-bottom" @click="useLayoutStore().setAppMenuCollapse()">
                    <el-icon><el-icon-expand v-if="appMenuCollapse" /><el-icon-fold v-else /></el-icon>
                </div>
            </div>
            <Side-m v-if="appLayoutMobile"></Side-m>
            <div class="layout-body el-container">
                <TopBar v-if="!appLayoutMobile" :appLayout="appLayout"></TopBar>
                <mainTabs ref="mainTabs" v-if="!appLayoutMobile && appShowTabs"></mainTabs>
                <div id="layout-main" class="layout-main">
                    <router-view v-slot="{ Component }">
                        <keep-alive :include="useKeepAliveStore().keepLiveRoute">
                            <component :is="Component" v-if="useKeepAliveStore().routeShow" :key="$route.fullPath" />
                        </keep-alive>
                    </router-view>
                    <iframe-view></iframe-view>
                </div>
            </div>
        </section>
    </template>

    <!-- 功能坞布局 -->
    <template v-else-if="appLayout === 'dock'">
        <header class="layout-header">
            <div class="layout-header-left">
                <div class="logo-bar">
                    <img class="logo" src="@/assets/img/logo-r.svg" />
                    <span>{{ $CONFIG.APP_NAME }}</span>
                </div>
            </div>
            <div class="layout-header-right">
                <div v-if="!appLayoutMobile" class="layout-header-menu">
                    <el-menu :default-active="active" mode="horizontal" router>
                        <NavMenu :navMenus="menu"></NavMenu>
                    </el-menu>
                </div>
                <Side-m v-if="appLayoutMobile"></Side-m>
                <userBar @settingAppLayoutMethod="settingAppLayout"></userBar>
            </div>
        </header>
        <section class="layout-wrapper">
            <div class="layout-body el-container">
                <mainTabs ref="mainTabs" v-if="!appLayoutMobile && appShowTabs"></mainTabs>
                <div id="layout-main" class="layout-main">
                    <router-view v-slot="{ Component }">
                        <keep-alive :include="useKeepAliveStore().keepLiveRoute">
                            <component :is="Component" v-if="useKeepAliveStore().routeShow" :key="$route.fullPath" />
                        </keep-alive>
                    </router-view>
                    <iframe-view></iframe-view>
                </div>
            </div>
        </section>
    </template>

    <!-- 默认布局 -->
    <template v-else>
        <section class="layout-wrapper">
            <div v-if="!appLayoutMobile" class="layout-side-split">
                <div class="layout-side-split-top">
                    <router-link :to="$CONFIG.DASHBOARD_URL">
                        <img :title="$CONFIG.APP_NAME" class="logo" src="@/assets/img/logo-r.svg" />
                    </router-link>
                </div>
                <div class="layout-side-split-scroll">
                    <el-scrollbar>
                        <el-menu :collapse="true">
                            <el-menu-item
                                v-for="item in menu"
                                :key="item"
                                :class="pmenu.path === item.path ? 'active' : ''"
                                @click="showMenu(item)"
                            >
                                <div class="menu-item-content">
                                    <el-icon><component :is="item.meta.icon || 'el-icon-menu'" /></el-icon>
                                    <span>{{ item.meta.title }}</span>
                                </div>
                            </el-menu-item>
                        </el-menu>
                    </el-scrollbar>
                </div>
                <!--<div>footer</div>-->
            </div>
            <div class="layout-container">
                <div class="layout-container-header">
                    <TopBar :appLayout="appLayout">
                        <userBar @settingAppLayoutMethod="settingAppLayout"></userBar>
                    </TopBar>
                </div>
                <div class="layout-container-main">
                    <div
                        v-if="(!appLayoutMobile && nextMenu.length > 0) || !pmenu.component"
                        :class="appMenuCollapse ? 'layout-side isCollapse' : 'layout-side'"
                    >
                        <div v-if="!appMenuCollapse" class="layout-side-top">
                            <h2>
                                <el-icon v-if="pmenu.meta && pmenu.meta.icon"
                                    ><component :is="pmenu.meta.icon || 'el-icon-menu'"
                                /></el-icon>
                                <span>{{ pmenu.meta && pmenu.meta.title }}</span>
                            </h2>
                        </div>
                        <div class="layout-side-scroll">
                            <el-scrollbar>
                                <el-menu
                                    :collapse="appMenuCollapse"
                                    :default-active="active"
                                    :unique-opened="$CONFIG.MENU_UNIQUE_OPENED"
                                    router
                                >
                                    <NavMenu :navMenus="nextMenu"></NavMenu>
                                </el-menu>
                            </el-scrollbar>
                        </div>
                        <div class="layout-side-bottom" @click="useLayoutStore().setAppMenuCollapse()">
                            <el-icon><el-icon-expand v-if="appMenuCollapse" /><el-icon-fold v-else /></el-icon>
                        </div>
                    </div>
                    <Side-m v-if="appLayoutMobile"></Side-m>
                    <div class="layout-body el-container">
                        <mainTabs ref="mainTabs" v-if="!appLayoutMobile && appShowTabs"></mainTabs>
                        <div id="layout-main" class="layout-main">
                            <router-view v-slot="{ Component }">
                                <keep-alive :include="useKeepAliveStore().keepLiveRoute">
                                    <component
                                        :is="Component"
                                        v-if="useKeepAliveStore().routeShow"
                                        :key="$route.fullPath"
                                    />
                                </keep-alive>
                            </router-view>
                            <iframe-view></iframe-view>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </template>

    <div v-if="minimizable === true" class="main-maximize-exit" @click="exitMaximize">
        <el-icon><el-icon-close /></el-icon>
    </div>

    <!--<div class="layout-setting" @click="settingAppLayout">
        <el-icon><el-icon-brush-filled /></el-icon>
    </div>-->

    <!--布局切换器-->
    <el-drawer v-model="settingDialog" :size="400" append-to-body destroy-on-close title="布局切换器">
        <setting></setting>
    </el-drawer>

</template>

<script>
import { useLayoutStore } from '@/stores/layout';
import { useKeepAliveStore } from '@/stores/keepAlive';
import iframeView from './components/iframeView.vue';
import setting from './components/setting.vue';
import SideM from './components/sideM.vue';
import mainTabs from './components/mainTabs.vue';
import TopBar from './components/topbar.vue';
import userBar from './components/userbar.vue';
import NavMenu from './components/NavMenu.vue';

export default {
    name: 'index',
    components: {
        SideM,
        TopBar,
        mainTabs,
        NavMenu,
        userBar,
        setting,
        iframeView
    },
    data() {
        return {
            settingDialog: false,
            menu: [],
            nextMenu: [],
            pmenu: {},
            active: '',
            newWindow: false, // 是否新窗口中显示
            fullscreen: false, // 是否全屏显示
            minimizable: true, // 是否可以退出全屏
            uiClassName: null // 自定义ui布局样式
        };
    },
    computed: {
        appLayoutMobile() {
            return useLayoutStore().appLayoutMobile;
        },
        appLayout() {
            return useLayoutStore().appLayout;
        },
        appShowTabs() {
            return useLayoutStore().appShowTabs;
        },
        appMenuCollapse() {
            return useLayoutStore().appMenuCollapse;
        }
    },
    created() {
        this.onLayoutResize();
        window.addEventListener('resize', this.onLayoutResize);
        const menu = this.$router.sc_getMenu();
        this.menu = this.filterUrl(menu);
        this.showThis();

        const _this = this;

        // 快捷键监听
        document.onkeydown = function (e) {
            e = e || window.event;
            const key = e.keyCode; // 获得keyCode

            // 键盘组合操作：同时按下Command+R
            if ((window.isMac === true && e.metaKey && key === 82) || (window.isMac === false && key === 116)) {
                // 在浏览器中cmd+r的组合键有其他的用途，所以在这边重新定义的时候，需要关闭原组合键,下面的代码可屏蔽原快捷键。
                e.preventDefault();

                _this.refreshActiveTab();
            }
        };
    },
    watch: {
        $route() {
            this.showThis();
        },
        appLayout: {
            handler(val) {
                document.body.setAttribute('data-layout', val);
            },
            immediate: true
        }
    },
    methods: {
        /**
         * 引用状态管理
         */
        useLayoutStore,
        useKeepAliveStore,
        /**
         * 刷新当前tab
         */
        refreshActiveTab() {
            //this.$message.success('刷新啦');

            // 说明开启了tabs
            if (typeof this.$refs.mainTabs === 'object' && this.$refs.mainTabs) {
                this.$refs.mainTabs.refreshTab(this.$route);
            } else {
                window.location.reload();
            }
        },
        settingAppLayout() {
            this.settingDialog = true;
        },
        onLayoutResize() {
            useLayoutStore().setAppLayoutMobile(document.body.clientWidth < 992);
        },
        //路由监听高亮
        showThis() {
            this.newWindow = !!this.$route.query.newWindow; // 是否新窗口
            this.pmenu = this.$route.meta.breadcrumb ? this.$route.meta.breadcrumb[0] : {};
            this.nextMenu = this.filterUrl(this.pmenu.children);

            this.$nextTick(() => {
                this.active = this.$route.meta.active || this.$route.fullPath;

                // 是否全屏显示
                this.fullscreen =
                    typeof this.$route.meta.fullscreen === 'boolean' ? this.$route.meta.fullscreen : false;

                // 指定了自定义UI样式class
                this.uiClassName =
                    typeof this.$route.meta.uiClassName === 'string' && this.$route.meta.uiClassName
                        ? this.$route.meta.uiClassName
                        : null;

                // 是否可以退出全屏
                this.minimizable =
                    typeof this.$route.meta.minimizable === 'boolean' ? this.$route.meta.minimizable : true;

                // 如果指定了全屏显示
                if (this.fullscreen) {
                    document.getElementById('app').classList.add('main-fullscreen');
                } else {
                    document.getElementById('app').classList.remove('main-fullscreen');
                }

                // 指定了自定义UI样式class
                if (this.uiClassName) {
                    document.getElementById('app').classList.add('app-' + this.uiClassName);
                } else {
                    this.removeCustomClass('app-');
                }
            });
        },
        /**
         * 移除自定义样式custom-
         */
        removeCustomClass(_prefix) {
            const element = document.getElementById('app');

            for (let i = 0; i < element.classList.length; i++) {
                const className = element.classList[i];
                if (className.startsWith(_prefix)) {
                    element.classList.remove(className);
                }
            }
        },
        //点击显示
        showMenu(route) {
            this.$nextTick(() => {
                this.pmenu = route;
                this.nextMenu = this.filterUrl(route.children);
                if ((!route.children || route.children.length === 0) && route.component && route.path) {
                    this.$router.push({ path: route.path });
                } else if (route.redirect) {
                    this.$router.push({ path: route.redirect });
                }
            });
        },
        //转换外部链接的路由
        filterUrl(map) {
            var newMap = [];
            map &&
                map.forEach(item => {
                    item.meta = item.meta ? item.meta : {};
                    //处理隐藏
                    if (item.meta.hidden || item.meta.type === 'button') {
                        return false;
                    }
                    //处理http
                    if (item.meta.type === 'iframe') {
                        item.path = `/i/${item.name}`;
                    }
                    //递归循环
                    if (item.children && item.children.length > 0) {
                        item.children = this.filterUrl(item.children);
                    }
                    newMap.push(item);
                });
            return newMap;
        },
        //退出最大化
        exitMaximize() {
            document.getElementById('app').classList.remove('main-maximize');
        }
    }
};
</script>
