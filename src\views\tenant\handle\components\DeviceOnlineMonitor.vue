<template>
    <div class="device-online-monitor">
        <!-- 设备离线通知对话框 -->
        <el-dialog
            v-model="offlineDialogVisible"
            title="设备离线通知"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="false"
        >
            <div class="offline-dialog-content">
                <el-alert type="error" :closable="false" show-icon>
                    <template #title>
                        <span class="alert-title">设备已离线</span>
                    </template>
                    <div class="alert-content">
                        <p
                            >设备 <strong>{{ deviceName }}</strong> 已离线，可能导致操作失败或数据不准确。</p
                        >
                        <p>离线时间: {{ formatTime(lastHeartbeat) }}</p>
                    </div>
                </el-alert>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="returnToList">返回设备列表</el-button>
                    <el-button type="primary" @click="retryConnection">重试连接</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import deviceOnlineService from '@/services/deviceOnlineService';
import { formatTimestamp } from '@/utils/date';
import { ElMessage, ElMessageBox } from 'element-plus';
import logger from '@/utils/logger';

export default {
    name: 'DeviceOnlineMonitor',
    props: {
        // 设备ID
        deviceId: {
            type: [Number, String],
            required: true
        },
        // 设备名称
        deviceName: {
            type: String,
            default: '未知设备'
        },
        // 是否自动监控
        autoMonitor: {
            type: Boolean,
            default: true
        },
        // 初始在线状态
        initialOnline: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            isOnline: this.initialOnline,
            offlineDialogVisible: false,
            lastHeartbeat: null,
            retryCount: 0,
            maxRetryCount: 3,
            statusListener: null,
            hasInitialized: false
        };
    },
    watch: {
        initialOnline: {
            handler(newValue) {
                if (!this.hasInitialized) {
                    this.isOnline = newValue;
                    this.hasInitialized = true;
                } else if (newValue !== this.isOnline) {
                    this.isOnline = newValue;
                    if (newValue && this.offlineDialogVisible) {
                        this.hideOfflineNotification();
                    }
                }
            },
            immediate: true
        }
    },
    mounted() {
        if (this.autoMonitor) {
            this.statusListener = deviceOnlineService.addStatusListener(
                this.deviceId,
                this.handleStatusChange,
                { initialOnline: this.initialOnline }
            );
        }
    },
    beforeUnmount() {
        if (this.statusListener) {
            deviceOnlineService.removeStatusListener(
                this.deviceId,
                this.handleStatusChange,
                this
            );
            this.statusListener = null;
        }
    },
    methods: {
        /**
         * 处理设备状态变更
         * @param {Object} status 设备状态对象
         */
        handleStatusChange(status) {
            logger.group(`设备 ${this.deviceId} 状态变更`);
            logger.info(`设备ID: ${this.deviceId}, 设备名称: ${this.deviceName}`);
            logger.info(`在线状态: ${status.online ? '在线' : '离线'}`);
            logger.info(`最后心跳: ${status.lastHeartbeat ? formatTimestamp(status.lastHeartbeat) : '无'}`);
            logger.info(`初始状态: ${this.initialOnline === null ? '未设置' : (this.initialOnline ? '在线' : '离线')}`);
            logger.groupEnd();

            const wasOnline = this.isOnline;
            this.isOnline = status.online;
            this.lastHeartbeat = status.lastHeartbeat;

            // 只在状态发生变化时更新UI
            if (wasOnline !== this.isOnline) {
                if (this.isOnline) {
                    this.hideOfflineNotification();
                } else {
                    this.showOfflineNotification();
                }
            }

            this.$emit('device-status-change', status);
        },

        /**
         * 显示离线通知
         */
        showOfflineNotification() {
            if (this.offlineDialogVisible) return;
            
            this.offlineDialogVisible = true;
            ElMessage.warning({
                message: `设备 ${this.deviceName} 已离线，请注意！`,
                duration: 5000,
                showClose: true
            });

            this.$emit('offline', {
                deviceId: this.deviceId,
                lastHeartbeat: this.lastHeartbeat
            });
        },

        /**
         * 隐藏离线通知
         */
        hideOfflineNotification() {
            if (!this.offlineDialogVisible) return;
            
            this.offlineDialogVisible = false;
            ElMessage.success({
                message: `设备 ${this.deviceName} 已重新上线！`,
                duration: 3000
            });

            this.$emit('online', {
                deviceId: this.deviceId,
                lastHeartbeat: this.lastHeartbeat
            });

            this.retryCount = 0;
        },

        /**
         * 重试连接
         */
        async retryConnection() {
            if (this.retryCount >= this.maxRetryCount) {
                try {
                    await ElMessageBox.confirm('已达到最大重试次数，是否返回设备列表？', '重试失败', {
                        confirmButtonText: '返回列表',
                        cancelButtonText: '继续尝试',
                        type: 'warning'
                    });
                    this.returnToList();
                } catch {
                    this.retryCount = 0;
                }
                return;
            }

            this.retryCount++;
            const loading = ElMessage({
                message: `正在检查设备状态 (${this.retryCount}/${this.maxRetryCount})...`,
                type: 'info',
                duration: 0
            });

            try {
                logger.group(`检查设备 ${this.deviceId} 状态`);
                logger.info(`设备ID: ${this.deviceId}, 设备名称: ${this.deviceName}`);
                logger.info(`重试次数: ${this.retryCount}/${this.maxRetryCount}`);

                // 等待设备状态更新
                let checkCount = 0;
                const maxChecks = 3; // 最多检查3次
                const checkInterval = 2000; // 每2秒检查一次

                const checkDeviceStatus = async () => {
                    const status = await deviceOnlineService.getDeviceOnlineStatus(this.deviceId);
                    logger.info(`检查结果: ${status.online ? '在线' : '离线'}`, status);

                    if (status.online) {
                        loading.close();
                        this.hideOfflineNotification();
                        logger.info(`设备 ${this.deviceId} 已重新上线`);
                        logger.groupEnd();
                        return;
                    }

                    if (checkCount < maxChecks - 1) {
                        checkCount++;
                        logger.info(`设备尚未上线，继续等待... (${checkCount + 1}/${maxChecks})`);
                        await new Promise(resolve => setTimeout(resolve, checkInterval));
                        return checkDeviceStatus();
                    }

                    loading.close();
                    ElMessage.error({
                        message: `设备 ${this.deviceName} 仍然离线，请稍后再试！`,
                        duration: 3000
                    });
                    logger.info(`设备 ${this.deviceId} 重试失败`);
                    logger.groupEnd();
                };

                await checkDeviceStatus();
            } catch (error) {
                loading.close();
                ElMessage.error({
                    message: `检查设备状态失败: ${error.message || '未知错误'}`,
                    duration: 3000
                });
                logger.error(`检查设备 ${this.deviceId} 状态出错:`, error);
                logger.groupEnd();
            }
        },

        /**
         * 返回设备列表
         */
        returnToList() {
            this.$router.push('/tenant/handle/list');
        },

        /**
         * 格式化时间
         * @param {number} timestamp 时间戳
         * @returns {string} 格式化后的时间
         */
        formatTime(timestamp) {
            return formatTimestamp(timestamp);
        }
    }
};
</script>

<style lang="scss" scoped>
.device-online-monitor {
    .offline-dialog-content {
        padding: 10px 0;

        .alert-title {
            font-size: 16px;
            font-weight: bold;
        }

        .alert-content {
            margin-top: 10px;

            p {
                margin: 5px 0;
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
}
</style>
