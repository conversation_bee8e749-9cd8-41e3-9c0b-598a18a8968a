<template>
    <div class="device-online-monitor">
        <!-- 设备离线通知对话框 -->
        <el-dialog
            v-model="offlineDialogVisible"
            title="设备离线通知"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="false"
        >
            <div class="offline-dialog-content">
                <el-alert type="error" :closable="false" show-icon>
                    <template #title>
                        <span class="alert-title">设备已离线</span>
                    </template>
                    <div class="alert-content">
                        <p
                            >设备 <strong>{{ deviceName }}</strong> 已离线，可能导致操作失败或数据不准确。</p
                        >
                        <p>离线时间: {{ formatTime(lastHeartbeat) }}</p>
                    </div>
                </el-alert>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="returnToList">返回设备列表</el-button>
                    <el-button type="primary" @click="retryConnection">重试连接</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import deviceOnlineService from '@/services/deviceOnlineService';
import { formatTimestamp } from '@/utils/date';
import { ElMessage, ElMessageBox } from 'element-plus';
import logger from '@/utils/logger';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';

export default {
    name: 'DeviceOnlineMonitor',
    props: {
        // 设备ID
        deviceId: {
            type: [Number, String],
            required: true
        },
        // 设备名称
        deviceName: {
            type: String,
            default: '未知设备'
        },
        // 是否自动监控
        autoMonitor: {
            type: Boolean,
            default: true
        },
        // 初始在线状态
        initialOnline: {
            type: Boolean,
            default: false
        }
    },
    setup() {
        // 获取 Pinia store
        const deviceStore = useDeviceOnlineStore();

        return {
            deviceStore
        };
    },
    data() {
        return {
            offlineDialogVisible: false,
            retryCount: 0,
            maxRetryCount: 3,
            hasInitialized: false
        };
    },
    computed: {
        // 从 Pinia store 获取设备状态
        deviceStatus() {
            return this.deviceStore.getDeviceStatus(String(this.deviceId));
        },

        // 设备是否在线
        isOnline() {
            return this.deviceStatus.online;
        },

        // 最后心跳时间
        lastHeartbeat() {
            return this.deviceStatus.lastHeartbeat;
        }
    },
    watch: {
        // 监听初始在线状态变化
        initialOnline: {
            handler(newValue) {
                if (!this.hasInitialized) {
                    // 初始化设备状态到 Pinia store
                    if (newValue !== null) {
                        this.deviceStore.initDeviceStatus(this.deviceId, newValue);
                    }
                    this.hasInitialized = true;
                }
            },
            immediate: true
        },

        // 监听设备在线状态变化
        isOnline(newOnline, oldOnline) {
            if (oldOnline !== undefined && newOnline !== oldOnline) {
                if (newOnline) {
                    this.hideOfflineNotification();
                } else {
                    this.showOfflineNotification();
                }

                this.$emit('device-status-change', {
                    deviceId: this.deviceId,
                    online: newOnline,
                    lastHeartbeat: this.lastHeartbeat
                });
            }
        }
    },
    mounted() {
        // 设备状态现在由 Pinia store 自动管理，不需要手动添加监听器
    },
    methods: {

        /**
         * 显示离线通知
         */
        showOfflineNotification() {
            if (this.offlineDialogVisible) return;

            this.offlineDialogVisible = true;
            ElMessage.warning({
                message: `设备 ${this.deviceName} 已离线，请注意！`,
                duration: 5000,
                showClose: true
            });

            this.$emit('offline', {
                deviceId: this.deviceId,
                lastHeartbeat: this.lastHeartbeat
            });
        },

        /**
         * 隐藏离线通知
         */
        hideOfflineNotification() {
            if (!this.offlineDialogVisible) return;

            this.offlineDialogVisible = false;
            ElMessage.success({
                message: `设备 ${this.deviceName} 已重新上线！`,
                duration: 3000
            });

            this.$emit('online', {
                deviceId: this.deviceId,
                lastHeartbeat: this.lastHeartbeat
            });

            this.retryCount = 0;
        },

        /**
         * 重试连接
         */
        async retryConnection() {
            if (this.retryCount >= this.maxRetryCount) {
                try {
                    await ElMessageBox.confirm('已达到最大重试次数，是否返回设备列表？', '重试失败', {
                        confirmButtonText: '返回列表',
                        cancelButtonText: '继续尝试',
                        type: 'warning'
                    });
                    this.returnToList();
                } catch {
                    this.retryCount = 0;
                }
                return;
            }

            this.retryCount++;
            const loading = ElMessage({
                message: `正在检查设备状态 (${this.retryCount}/${this.maxRetryCount})...`,
                type: 'info',
                duration: 0
            });

            try {
                logger.group(`检查设备 ${this.deviceId} 状态`);
                logger.info(`设备ID: ${this.deviceId}, 设备名称: ${this.deviceName}`);
                logger.info(`重试次数: ${this.retryCount}/${this.maxRetryCount}`);

                // 等待设备状态更新
                let checkCount = 0;
                const maxChecks = 3; // 最多检查3次
                const checkInterval = 2000; // 每2秒检查一次

                const checkDeviceStatus = async () => {
                    // 直接从 Pinia store 获取最新状态
                    const isOnline = this.deviceStore.isDeviceOnline(this.deviceId);
                    logger.info(`检查结果: ${isOnline ? '在线' : '离线'}`);

                    if (isOnline) {
                        loading.close();
                        this.hideOfflineNotification();
                        logger.info(`设备 ${this.deviceId} 已重新上线`);
                        logger.groupEnd();
                        return;
                    }

                    if (checkCount < maxChecks - 1) {
                        checkCount++;
                        logger.info(`设备尚未上线，继续等待... (${checkCount + 1}/${maxChecks})`);
                        await new Promise(resolve => setTimeout(resolve, checkInterval));
                        return checkDeviceStatus();
                    }

                    loading.close();
                    ElMessage.error({
                        message: `设备 ${this.deviceName} 仍然离线，请稍后再试！`,
                        duration: 3000
                    });
                    logger.info(`设备 ${this.deviceId} 重试失败`);
                    logger.groupEnd();
                };

                await checkDeviceStatus();
            } catch (error) {
                loading.close();
                ElMessage.error({
                    message: `检查设备状态失败: ${error.message || '未知错误'}`,
                    duration: 3000
                });
                logger.error(`检查设备 ${this.deviceId} 状态出错:`, error);
                logger.groupEnd();
            }
        },

        /**
         * 返回设备列表
         */
        returnToList() {
            this.$router.push('/tenant/handle/list');
        },

        /**
         * 格式化时间
         * @param {number} timestamp 时间戳
         * @returns {string} 格式化后的时间
         */
        formatTime(timestamp) {
            if (!timestamp) {
                return '未知';
            }
            return formatTimestamp(timestamp);
        }
    }
};
</script>

<style lang="scss" scoped>
.device-online-monitor {
    .offline-dialog-content {
        padding: 10px 0;

        .alert-title {
            font-size: 16px;
            font-weight: bold;
        }

        .alert-content {
            margin-top: 10px;

            p {
                margin: 5px 0;
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
}
</style>
