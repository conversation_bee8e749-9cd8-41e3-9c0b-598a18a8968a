<template>
    <div class="device-online-monitor">
        <!-- 设备离线通知对话框 -->
        <el-dialog
            v-model="offlineDialogVisible"
            title="设备离线通知"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="false"
        >
            <div class="offline-dialog-content">
                <el-alert type="error" :closable="false" show-icon>
                    <template #title>
                        <span class="alert-title">设备已离线</span>
                    </template>
                    <div class="alert-content">
                        <p
                            >设备 <strong>{{ deviceName }}</strong> 已离线，可能导致操作失败或数据不准确。</p
                        >
                        <p>离线时间: {{ formatTime(lastHeartbeat) }}</p>
                    </div>
                </el-alert>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="returnToList">返回设备列表</el-button>
                    <el-button type="primary" @click="refreshDeviceStatus">刷新状态</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import deviceOnlineService from '@/services/deviceOnlineService';
import { formatTimestamp } from '@/utils/date';
import { ElMessage, ElMessageBox } from 'element-plus';
import logger from '@/utils/logger';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';
import deviceInfoService from '@/services/deviceInfoService';

export default {
    name: 'DeviceOnlineMonitor',
    props: {
        // 设备ID
        deviceId: {
            type: [Number, String],
            required: true
        },
        // 设备名称
        deviceName: {
            type: String,
            default: '未知设备'
        },
        // 是否自动监控
        autoMonitor: {
            type: Boolean,
            default: true
        },
        // 初始在线状态
        initialOnline: {
            type: Boolean,
            default: false
        }
    },
    setup() {
        // 获取 Pinia store
        const deviceStore = useDeviceOnlineStore();

        return {
            deviceStore
        };
    },
    data() {
        return {
            offlineDialogVisible: false,
            hasInitialized: false
        };
    },
    computed: {
        // 从 Pinia store 获取设备状态
        deviceStatus() {
            return this.deviceStore.getDeviceStatus(String(this.deviceId));
        },

        // 设备是否在线
        isOnline() {
            return this.deviceStatus.online;
        },

        // 最后心跳时间
        lastHeartbeat() {
            return this.deviceStatus.lastHeartbeat;
        }
    },
    watch: {
        // 监听初始在线状态变化
        initialOnline: {
            handler(newValue) {
                if (!this.hasInitialized) {
                    // 初始化设备状态到 Pinia store
                    if (newValue !== null) {
                        this.deviceStore.initDeviceStatus(this.deviceId, newValue);
                    }
                    this.hasInitialized = true;
                }
            },
            immediate: true
        },

        // 监听设备在线状态变化
        isOnline(newOnline, oldOnline) {
            // 只有在组件已初始化且状态确实发生变化时才处理
            if (this.hasInitialized && newOnline !== oldOnline) {
                logger.info(`设备 ${this.deviceId} 状态变化: ${oldOnline} → ${newOnline}`);

                if (newOnline) {
                    this.hideOfflineNotification();
                } else {
                    this.showOfflineNotification();
                }

                this.$emit('device-status-change', {
                    deviceId: this.deviceId,
                    online: newOnline,
                    lastHeartbeat: this.lastHeartbeat
                });
            }
        }
    },
    mounted() {
        // 设备状态现在由 Pinia store 自动管理，不需要手动添加监听器

        // 延迟检查：页面刷新后，等待MQTT连接建立并接收到最新状态
        // 如果初始状态与实际MQTT状态不一致，需要显示正确的通知
        setTimeout(() => {
            this.checkInitialStatus();
        }, 2000); // 等待2秒让MQTT连接建立并接收消息
    },
    methods: {
        /**
         * 检查初始状态
         * 页面刷新后，检查后端返回的状态与MQTT实际状态是否一致
         */
        checkInitialStatus() {
            if (!this.hasInitialized) return;

            const currentOnlineStatus = this.isOnline;
            const initialOnlineStatus = this.initialOnline;

            logger.info(`设备 ${this.deviceId} 状态检查:`, {
                initial: initialOnlineStatus,
                current: currentOnlineStatus,
                dialogVisible: this.offlineDialogVisible
            });

            // 如果当前状态是离线，但还没有显示离线通知，则显示
            if (!currentOnlineStatus && !this.offlineDialogVisible) {
                logger.info(`设备 ${this.deviceId} 检测到离线状态，显示离线通知`);
                this.showOfflineNotification();
            }
            // 如果当前状态是在线，但显示了离线通知，则隐藏
            else if (currentOnlineStatus && this.offlineDialogVisible) {
                logger.info(`设备 ${this.deviceId} 检测到在线状态，隐藏离线通知`);
                this.hideOfflineNotification();
            }
        },

        /**
         * 显示离线通知
         */
        showOfflineNotification() {
            if (this.offlineDialogVisible) return;

            this.offlineDialogVisible = true;
            ElMessage.warning({
                message: `设备 ${this.deviceName} 已离线，请注意！`,
                duration: 5000,
                showClose: true
            });

            this.$emit('offline', {
                deviceId: this.deviceId,
                lastHeartbeat: this.lastHeartbeat
            });
        },

        /**
         * 隐藏离线通知
         */
        hideOfflineNotification() {
            if (!this.offlineDialogVisible) return;

            this.offlineDialogVisible = false;
            ElMessage.success({
                message: `设备 ${this.deviceName} 已重新上线！`,
                duration: 3000
            });

            this.$emit('online', {
                deviceId: this.deviceId,
                lastHeartbeat: this.lastHeartbeat
            });
        },

        /**
         * 刷新设备状态
         */
        async refreshDeviceStatus() {
            const loading = ElMessage({
                message: `正在查询设备最新状态...`,
                type: 'info',
                duration: 0
            });

            try {
                // 使用统一的设备信息服务刷新状态
                const isOnline = await deviceInfoService.refreshDeviceStatus(
                    this.deviceId,
                    this.deviceStore,
                    true // 静默模式，我们自己处理提示
                );

                loading.close();

                if (isOnline === true) {
                    // 设备在线，关闭离线对话框
                    this.offlineDialogVisible = false;
                    ElMessage.success({
                        message: `设备 ${this.deviceName} 当前在线`,
                        duration: 3000
                    });

                    // 发射上线事件
                    this.$emit('online', {
                        deviceId: this.deviceId,
                        lastHeartbeat: this.lastHeartbeat
                    });
                } else if (isOnline === false) {
                    // 设备仍然离线
                    ElMessage.warning({
                        message: `设备 ${this.deviceName} 仍然离线`,
                        duration: 3000
                    });
                } else {
                    // 获取状态失败
                    ElMessage.error({
                        message: `无法获取设备状态，请稍后重试`,
                        duration: 3000
                    });
                }
            } catch (error) {
                loading.close();
                ElMessage.error({
                    message: `刷新设备状态失败`,
                    duration: 3000
                });
                logger.error(`刷新设备 ${this.deviceId} 状态异常:`, error);
            }
        },

        /**
         * 返回设备列表
         */
        returnToList() {
            this.$router.push('/tenant/handle/list');
        },

        /**
         * 格式化时间
         * @param {number} timestamp 时间戳
         * @returns {string} 格式化后的时间
         */
        formatTime(timestamp) {
            if (!timestamp) {
                return '未知';
            }
            return formatTimestamp(timestamp);
        }
    }
};
</script>

<style lang="scss" scoped>
.device-online-monitor {
    .offline-dialog-content {
        padding: 10px 0;

        .alert-title {
            font-size: 16px;
            font-weight: bold;
        }

        .alert-content {
            margin-top: 10px;

            p {
                margin: 5px 0;
            }
        }
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
}
</style>
