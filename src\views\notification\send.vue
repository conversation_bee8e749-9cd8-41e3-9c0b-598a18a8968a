<template>
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="消息类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择消息类型">
                <el-option v-for="type in messageTypes" :key="type.value" :label="type.label" :value="type.value" />
            </el-select>
        </el-form-item>

        <el-form-item label="接收范围" prop="receive_range">
            <el-select v-model="formData.receive_range" placeholder="请选择接收范围" @change="handleRangeChange">
                <el-option
                    v-for="range in receiveRanges"
                    :key="range.value"
                    :label="range.label"
                    :value="range.value"
                />
            </el-select>
        </el-form-item>

        <el-form-item v-if="formData.receive_range === 2" label="选择部门" prop="target_ids">
            <el-cascader
                v-model="formData.target_ids"
                :options="departmentOptions"
                :props="departmentProps"
                :show-all-levels="false"
                :collapse-tags="false"
                :collapse-tags-tooltip="false"
                clearable
                style="width: 100%"
                placeholder="请选择部门"
            />
        </el-form-item>

        <el-form-item v-if="formData.receive_range === 3" label="选择用户" prop="target_ids">
            <el-select
                v-model="formData.target_ids"
                multiple
                filterable
                remote
                placeholder="请选择用户"
                :remote-method="handleUserSearchDebounced"
                :loading="loading"
            >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.realname" :value="item.id">
                    <span style="float: left">{{ item.realname }}</span>
                    <span style="float: right; color: var(--el-text-color-secondary)">
                        {{ item.username }}
                    </span>
                </el-option>
            </el-select>
        </el-form-item>

        <el-form-item label="标题" prop="title">
            <el-input v-model="formData.title" placeholder="请输入标题" />
        </el-form-item>

        <el-form-item label="内容" prop="content">
            <el-input v-model="formData.content" type="textarea" :rows="6" placeholder="请输入内容" />
        </el-form-item>

        <el-form-item>
            <el-button type="primary" v-debounce="{ fn: handleSubmit, delay: 500 }">发送</el-button>
            <el-button @click="handleReset">重置</el-button>
        </el-form-item>
    </el-form>
</template>

<script>
import notificationApi from '@/api/model/notification';
import { ElMessage } from 'element-plus';
import { MESSAGE_TYPE_OPTIONS, RECEIVE_RANGE_OPTIONS } from '@/constants/notification';
import NotificationUtils from '@/utils/notification';
import { debounce } from '@/utils/throttleDebounce';

export default {
    name: 'NotificationSend',
    emits: ['success'],

    data() {
        return {
            formRef: null,
            loading: false,
            userOptions: [],
            departmentOptions: [],
            messageTypes: MESSAGE_TYPE_OPTIONS,
            receiveRanges: RECEIVE_RANGE_OPTIONS,
            formData: {
                type: 1,
                receive_range: 1,
                target_ids: [],
                title: '',
                content: ''
            },
            rules: {
                type: [{ required: true, message: '请选择消息类型' }],
                receive_range: [{ required: true, message: '请选择接收范围' }],
                target_ids: [{ required: true, message: '请选择接收对象', trigger: 'change' }],
                title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
                content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
            },
            departmentProps: {
                value: 'id',
                label: 'name',
                checkStrictly: true,
                multiple: true,
                emitPath: false,
                expandTrigger: 'hover'
            }
        };
    },

    created() {
        // 防抖处理用户搜索
        this.handleUserSearchDebounced = debounce(this.handleUserSearch, 300);
    },

    methods: {
        /**
         * 获取部门列表数据
         */
        async fetchDepartments() {
            try {
                const res = await notificationApi.departments.get();
                this.departmentOptions = res.data;
            } catch (error) {
                console.error('获取部门列表失败:', error);
                ElMessage.error('获取部门数据失败，请稍后重试');
            }
        },

        /**
         * 搜索用户
         * @param {string} query - 搜索关键词
         */
        async handleUserSearch(query) {
            if (query) {
                this.loading = true;
                try {
                    const res = await notificationApi.receivers.get({ keyword: query });
                    this.userOptions = res.data;
                } finally {
                    this.loading = false;
                }
            } else {
                this.userOptions = [];
            }
        },

        /**
         * 处理接收范围变更
         * @param {number} value - 新的接收范围值
         */
        handleRangeChange(value) {
            this.formData.target_ids = [];
            if (value === 2) {
                this.fetchDepartments();
            }
        },

        /**
         * 提交表单发送消息
         */
        async handleSubmit() {
            try {
                await this.$refs.formRef.validate();
                const { success } = await NotificationUtils.sendMessage(this.formData);
                if (success) {
                    this.$emit('success');
                    this.closeDialog();
                }
            } catch (error) {
                console.error('表单验证失败:', error);
            }
        },

        /**
         * 重置表单
         */
        handleReset() {
            this.$refs.formRef.resetFields();
        }
    }
};
</script>
