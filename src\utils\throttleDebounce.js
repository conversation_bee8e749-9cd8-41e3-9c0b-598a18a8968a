/**
 * 防抖函数（可以配合指令一起使用v-debounce）
 * @param {Function} fn - 需要防抖的函数
 * @param {number} delay - 延迟时间，默认300ms
 * @param {boolean} immediate - 是否立即执行，默认false
 * @returns {Function} - 防抖后的函数,返回Promise
 */
export function debounce(fn, delay = 300, immediate = false) {
    let timer = null;
    return function (...args) {
        return new Promise(resolve => {
            const context = this;

            if (timer) clearTimeout(timer);

            if (immediate && !timer) {
                Promise.resolve(fn.apply(context, args)).then(resolve);
            } else {
                timer = setTimeout(async () => {
                    const result = await fn.apply(context, args);
                    timer = null;
                    resolve(result);
                }, delay);
            }
        });
    };
}

/**
 * 节流函数（可以配合指令一起使用v-throttle）
 * @param {Function} fn - 需要节流的函数
 * @param {number} delay - 延迟时间，默认300ms
 * @param {Object} options - 配置项
 * @param {boolean} options.leading - 是否立即执行，默认true
 * @param {boolean} options.trailing - 是否延迟执行，默认true
 * @returns {Function} - 节流后的函数
 */
export function throttle(fn, delay = 300, options = {}) {
    let timer = null;
    let previous = 0;

    const { leading = true, trailing = true } = options;

    return function (...args) {
        const context = this;
        const now = Date.now();

        if (!previous && !leading) {
            previous = now;
        }

        const remaining = delay - (now - previous);

        if (remaining <= 0 || remaining > delay) {
            if (timer) {
                clearTimeout(timer);
                timer = null;
            }

            previous = now;
            fn.apply(context, args);
        } else if (!timer && trailing) {
            timer = setTimeout(() => {
                previous = leading ? Date.now() : 0;
                timer = null;
                fn.apply(context, args);
            }, remaining);
        }
    };
}
