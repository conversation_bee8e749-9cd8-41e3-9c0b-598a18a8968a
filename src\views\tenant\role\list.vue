<template>
    <el-container style="overflow: hidden">
        <el-container>
            <el-header>
                <div class="left-panel">
                    <el-button
                        v-auths="['tenant.role.add']"
                        icon="el-icon-plus"
                        type="primary"
                        @click="role_add"
                        color="#1C409A"
                        >添加角色</el-button
                    >
                    <el-button
                        v-auths="['tenant.role.delete']"
                        :disabled="selection.length === 0"
                        icon="el-icon-delete"
                        plain
                        type="danger"
                        @click="batch_delete"
                        >删除</el-button
                    >
                </div>
                <div class="right-panel">
                    <div class="right-panel-search">
                        <el-input
                            style="min-width: 180px"
                            v-model="searchObj.keyword"
                            clearable
                            placeholder="角色名称/角色标识/备注"
                            @keyup.enter="searchHandle"
                            @clear="searchHandle"
                        ></el-input>
                        <el-button
                            icon="el-icon-search"
                            type="primary"
                            color="#1C409A"
                            @click="searchHandle"
                        ></el-button>
                    </div>
                </div>
            </el-header>
            <el-main class="p0">
                <scTable
                    ref="roleDataTable"
                    :apiObj="roleDataObj"
                    remoteFilter
                    remoteSort
                    stripe
                    @selection-change="selectionChange"
                    class="custom-list-table"
                >
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column label="ID" prop="id" sortable="custom" width="80" fixed></el-table-column>
                    <el-table-column
                        label="角色名称"
                        prop="name"
                        sortable="custom"
                        width="150"
                        fixed
                    ></el-table-column>
                    <el-table-column
                        label="角色标识"
                        prop="code"
                        show-overflow-tooltip
                        sortable="custom"
                        width="150"
                    ></el-table-column>
                    <el-table-column
                        label="备注"
                        prop="description"
                        show-overflow-tooltip
                        sortable="custom"
                        width="200"
                    ></el-table-column>
                    <el-table-column
                        :filters="[
                            { text: '已禁用', value: '0' },
                            { text: '正常', value: '1' }
                        ]"
                        column-key="filter[status]"
                        align="center"
                        label="状态"
                        prop="status"
                        width="100"
                    >
                        <template #default="scope">
                            <el-switch
                                v-auths="['tenant.role.status']"
                                size="small"
                                style="--el-switch-on-color: #13ce66; --el-switch-off-color: #bfbfbf"
                                v-model="scope.row.status"
                                :loading="scope.row.loading || false"
                                :active-value="1"
                                :inactive-value="0"
                                @change="switchRoleStatus(scope.row)"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="创建时间"
                        prop="create_at"
                        show-overflow-tooltip
                        sortable="custom"
                        width="170"
                    ></el-table-column>
                    <el-table-column align="center" fixed="right" label="操作" width="180">
                        <template #default="scope">
                            <el-button
                                v-auths="['tenant.role.permission']"
                                size="small"
                                type="primary"
                                @click="role_permission(scope.row, scope.$index)"
                                color="#32aa66"
                                >权限</el-button
                            >
                            <el-button
                                v-auths="['tenant.role.edit']"
                                size="small"
                                type="primary"
                                @click="role_edit(scope.row, scope.$index)"
                                color="#1C409A"
                                >编辑</el-button
                            >
                            <el-popconfirm title="确定删除该角色吗？" @confirm="role_delete(scope.row, scope.$index)">
                                <template #reference>
                                    <el-button
                                        v-auths="['tenant.role.delete']"
                                        size="small"
                                        type="danger"
                                        >删除</el-button
                                    >
                                </template>
                            </el-popconfirm>
                        </template>
                    </el-table-column>
                </scTable>
            </el-main>
        </el-container>
    </el-container>

    <!--添加编辑对话框-->
    <save-dialog
        v-if="dialog.save"
        ref="saveDialog"
        @saveDialogClosedEmit="saveDialogClosedHandle"
        @saveSuccessEmit="saveSuccessHandle"
    ></save-dialog>

    <!--权限配置对话框-->
    <permission-dialog
        v-if="dialog.permission"
        ref="permissionDialog"
        @permissionDialogClosedEmit="permissionDialogClosedHandle"
    ></permission-dialog>
</template>

<script>
import saveDialog from './save';
import permissionDialog from './permission';
import tableConfig from '@/config/table.js';

export default {
    name: 'tenant.role.list',
    components: {
        saveDialog,
        permissionDialog
    },
    data() {
        return {
            dialog: {
                save: false,
                permission: false
            },
            roleDataObj: this.$API.tenant.role.list,
            selection: [],
            searchObj: {
                keyword: null
            }
        };
    },
    methods: {
        /**
         * 角色添加
         */
        role_add() {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show();
            });
        },
        /**
         * 角色编辑
         * @param row
         */
        role_edit(row) {
            this.dialog.save = true;
            this.$nextTick(() => {
                this.$refs.saveDialog.show('edit').setFormData(row, false);
            });
        },
        /**
         * 角色权限配置
         * @param row
         */
        role_permission(row) {
            this.dialog.permission = true;
            this.$nextTick(() => {
                this.$refs.permissionDialog.show(row);
            });
        },
        /**
         * 单个角色删除
         * @param row
         * @param index
         * @returns {Promise<void>}
         */
        async role_delete(row, index) {
            const reqData = { id: row.id };
            const res = await this.$API.tenant.role.delete.post(reqData);
            if (res.code === tableConfig.successCode) {
                this.$refs.roleDataTable.tableData.splice(index, 1);
                this.$message.success('删除成功');
            } else {
                await this.$alert(res.message, '提示', { type: 'error' });
            }
        },
        /**
         * 批量删除
         * @returns {Promise<void>}
         */
        async batch_delete() {
            this.$confirm(`确定删除选中的 ${this.selection.length} 个角色吗？`, '提示', {
                type: 'warning'
            })
                .then(async () => {
                    const loading = this.$loading();

                    let ids = this.selection.map(item => {
                        return item.id;
                    });

                    const reqData = { id: ids };

                    const res = await this.$API.tenant.role.delete.post(reqData);

                    if (res.code === tableConfig.successCode) {
                        this.selection.forEach(item => {
                            this.$refs.roleDataTable.tableData.forEach((itemI, indexI) => {
                                if (item.id === itemI.id) {
                                    this.$refs.roleDataTable.tableData.splice(indexI, 1);
                                }
                            });
                        });

                        this.$message.success(res.message);
                    } else {
                        this.$message.error(res.message);
                    }

                    loading.close();
                })
                .catch(() => {});
        },
        /**
         * 表格选择后回调事件
         * @param selection
         */
        selectionChange(selection) {
            this.selection = selection;
        },
        /**
         * 搜索
         */
        searchHandle() {
            this.$refs.roleDataTable.refresh();
        },
        /**
         * 角色状态切换
         * @param row
         */
        switchRoleStatus(row) {
            this.$API.tenant.role.status
                .post({ id: row.id, status: row.status })
                .then(res => {
                    if (res.code === tableConfig.successCode) {
                        this.$message.success('操作成功');
                    } else {
                        row.status = row.status === 1 ? 0 : 1;
                        this.$message.error(res.message);
                    }
                })
                .catch(() => {
                    row.status = row.status === 1 ? 0 : 1;
                });
        },
        /**
         * 保存对话框关闭事件
         */
        saveDialogClosedHandle() {
            this.dialog.save = false;
        },
        /**
         * 保存成功事件
         */
        saveSuccessHandle() {
            this.$refs.roleDataTable.refresh();
        },
        /**
         * 权限对话框关闭事件
         */
        permissionDialogClosedHandle() {
            this.dialog.permission = false;
        }
    }
};
</script>
