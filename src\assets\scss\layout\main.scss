// 布局主样式
// <AUTHOR> <Chaoyu<PERSON>@live.com>
// @Datetime 2024-12-13

// 头部样式
@use './header.scss';
// 主选项卡样式
@use './tabs.scss';
// 主侧边栏样式
@use './sidebar.scss';
// 主工具条样式
@use './tools.scss';

// 添加变量定义
$layout-border-color: var(--el-border-color-dark);
$layout-shadow: 3px 1px 2px rgba(0, 0, 0, 0.09);
$layout-bg-color: rgba(0, 0, 0, 0.015);

/* 大布局样式 */
.layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh; // 确保最小高度占满视口

    // {.layout-wrapper}
    &-wrapper {
        flex: 1;
        display: flex;
        overflow: auto;
        -webkit-overflow-scrolling: touch; // 改善移动端滚动体验
    }

    // 右侧框架{.layout-container}
    &-container {
        flex: 1 1 auto; // 简化flex属性写法
        display: flex;
        flex-direction: column;
        min-width: 0; // 防止flex子项溢出

        &-header {
            position: relative; // 为阴影效果添加定位上下文
            border-bottom: 1px solid $layout-border-color;
            box-shadow:
                $layout-shadow,
                inset 0 1px 0 var(--el-fill-color-blank);
            z-index: 3;
        }

        &-main {
            flex: 1 1 auto;
            display: flex;
            overflow: auto;
        }
    }

    // 右侧内容{.layout-body}
    &-body {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    // {.layout-main}
    &-main {
        flex: 1;
        overflow: auto;
        background-color: $layout-bg-color;
        -webkit-overflow-scrolling: touch;
    }
}
