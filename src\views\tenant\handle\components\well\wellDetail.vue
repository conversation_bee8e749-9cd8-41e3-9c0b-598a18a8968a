<template>
    <el-container class="layer-layout" v-loading="loadingStates[layer]">
        <el-header class="layer-header">
            <el-button type="primary" color="#662f8f" round size="small" @click="backWell()" icon="el-icon-d-arrow-left">返回</el-button>
            <block class="header-right">
                <wellInfo :wellInfo="wellInfo"></wellInfo>
            </block>
        </el-header>
        <el-container v-if="wellInfo.id" class="layer-container">
            <wellChart
                ref="wellChart"
                :wellInfo="wellInfo"
                :layerInfo="{
                    layer: layer,
                    name: chineseLayer(layer)
                }"
                :customOptions="{
                    // 设置图表标题
                    title: {
                        text: `第${this.chineseLayer(this.layer)}层`
                    },
                    toolbox: {
                        feature: {
                            myViewInfo: {
                                show: false
                            }
                        }
                    }
                }"
                @chartInitComplete="handleChartInitComplete"
            />
            <el-aside class="layer-tools">
                <wellTools
                    :wellInfo="wellInfo"
                    :layerInfo="{
                        layer: layer,
                        name: chinese<PERSON>ayer(layer)
                    }"
                />
            </el-aside>
        </el-container>
        <el-empty v-else class="layer-container" :image-size="180" image888="img/404.png">
            <template #description>
                <div class="error-message">
                    <span style="color: #909399; font-size: 14px">未找到有效的油井信息</span>
                </div>
            </template>
        </el-empty>

        <!-- 设备在线状态监控组件 -->
        <DeviceOnlineMonitor
            ref="deviceMonitor"
            :device-id="$route.params.id"
            :device-name="wellInfo.name"
            :initial-online="wellInfo.online"
            @offline="handleDeviceOffline"
            @online="handleDeviceOnline"
        />
    </el-container>
</template>

<script>
import wellChart from './wellChart.vue';
import wellTools from './wellTools.vue';
import wellInfo from "./wellInfo.vue";
import DeviceOnlineMonitor from '../DeviceOnlineMonitor.vue';
import { chineseLayer } from './chartUtils';
import {ElMessage} from "element-plus";
import logger from '@/utils/logger';

export default {
    name: 'wellDetail',
    components: {
        wellChart,
        wellTools,
        wellInfo,
        DeviceOnlineMonitor
    },
    data() {
        return {
            // 每个图表的loading状态
            loadingStates: {},
            // 当前油井信息
            wellInfo: {
                id: 0,
                name: '',
                level: 0
            },
            // 当前油井层
            layer: null
        };
    },
    created() {},
    async mounted() {
        // 根据URL中的油井层参数
        this.layer = this.$route.params.layer;

        // 初始化油井信息
        await this.fetchWellInfo();
    },
    watch: {},
    beforeUnmount() {
        console.log('beforeUnmount-layer::', this.wellId);
    },
    methods: {
        /**
         * 返回油井页
         */
        backWell() {
            //this.$router.push(`/well-main/?id=${this.wellId}`);
            this.$router.back();
        },
        /**
         * 将对应的层级转换成中文
         */
        chineseLayer(index) {
            return chineseLayer(index);
        },
        /**
         * 获取油井数据
         * @returns {Promise<void>}
         */
        async fetchWellInfo() {
            try {
                // 从路由参数获取设备ID
                const deviceId = this.$route.params.id;

                // 调用API获取设备信息
                const result = await this.$API.tenant.device.info.get({
                    id: deviceId,
                    layer: this.layer
                });

                // 检查返回状态是否为1
                if (result.status !== 1) {
                    throw new Error(result.message || '获取油井数据失败');
                }

                // 从返回结果中获取data字段作为油井信息
                this.wellInfo = result.data;

                // 获取设备在线状态
                if (result.data.online !== undefined) {
                    logger.info(`设备 ${deviceId} 后端返回的在线状态: ${result.data.online ? '在线' : '离线'}`);

                    // 如果设备离线，显示离线通知
                    if (!result.data.online) {
                        this.$nextTick(() => {
                            this.$refs.deviceMonitor?.showOfflineNotification();
                        });
                    }
                }

                logger.info('油井数据加载成功:', this.wellInfo);
            } catch (error) {
                ElMessage.error(error.message || '获取油井数据出错');
                console.error('获取油井数据出错:', error);
            }

            this.loading = false;
        },
        /**
         * 处理图表初始化完成事件
         * @param {number} layerIndex - 图表对应的层级索引
         */
        handleChartInitComplete(layerIndex) {
            this.loadingStates[layerIndex] = false;
        },

        /**
         * 处理设备离线事件
         * @param {Object} data 离线事件数据
         */
        handleDeviceOffline(data) {
            logger.info('设备离线:', data);

            // 可以在这里添加额外的离线处理逻辑
            // 例如停止数据刷新、禁用操作按钮等
            if (this.$refs.wellChart) {
                // 通知图表组件设备已离线
                const status = { deviceId: this.$route.params.id, online: false, lastHeartbeat: data.lastHeartbeat };
                this.$refs.wellChart.handleStatusChange(status);
            }

            // 通知父组件设备离线
            this.$emit('device-offline', {
                deviceId: this.$route.params.id,
                deviceName: this.wellInfo.name,
                lastHeartbeat: data.lastHeartbeat
            });
        },

        /**
         * 处理设备上线事件
         * @param {Object} data 上线事件数据
         */
        handleDeviceOnline(data) {
            logger.info('设备上线:', data);

            // 可以在这里添加额外的上线处理逻辑
            // 例如恢复数据刷新、启用操作按钮等
            if (this.$refs.wellChart) {
                // 通知图表组件设备已上线
                const status = { deviceId: this.$route.params.id, online: true, lastHeartbeat: data.lastHeartbeat };
                this.$refs.wellChart.handleStatusChange(status);
            }

            // 通知父组件设备上线
            this.$emit('device-online', {
                deviceId: this.$route.params.id,
                deviceName: this.wellInfo.name,
                lastHeartbeat: data.lastHeartbeat
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.el-header {
    height: 36px;
    padding: 0 15px;
}

.layer-container {
    flex-grow: 1; /* 自适应高度 */
    overflow: hidden;

    .layer-tools {
        width: 250px;
        border: 0;
        border-left: 1px solid #ededed;
        background-color: #fafdfd;
        overflow: hidden;
    }
}

.el-col {
    display: inline-flex;
    flex-direction: column;
    background-color: #fafafa;
}

html[data-theme='dark'] {
    .layer-tools {
        border-color: var(--el-border-color);
        background-color: var(--color-background);
    }
}
</style>
