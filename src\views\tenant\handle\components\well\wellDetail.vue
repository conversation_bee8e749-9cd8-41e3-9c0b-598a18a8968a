<template>
    <el-container class="layer-layout" v-loading="loadingStates[layer]">
        <el-header class="layer-header">
            <el-button type="primary" color="#662f8f" round size="small" @click="backWell()" icon="el-icon-d-arrow-left">返回</el-button>
            <block class="header-right">
                <wellInfo :wellInfo="wellInfo"></wellInfo>
            </block>
        </el-header>
        <el-container v-if="wellInfo.id" class="layer-container">
            <wellChart
                ref="wellChart"
                :wellInfo="wellInfo"
                :layerInfo="{
                    layer: layer,
                    name: chineseLayer(layer)
                }"
                :customOptions="{
                    // 设置图表标题
                    title: {
                        text: `第${this.chineseLayer(this.layer)}层`
                    },
                    toolbox: {
                        feature: {
                            myViewInfo: {
                                show: false
                            }
                        }
                    }
                }"
                @chartInitComplete="handleChartInitComplete"
            />
            <el-aside class="layer-tools">
                <wellTools
                    :wellInfo="wellInfo"
                    :layerInfo="{
                        layer: layer,
                        name: chinese<PERSON>ayer(layer)
                    }"
                />
            </el-aside>
        </el-container>
        <el-empty v-else class="layer-container" :image-size="180" image888="img/404.png">
            <template #description>
                <div class="error-message">
                    <span style="color: #909399; font-size: 14px">未找到有效的油井信息</span>
                </div>
            </template>
        </el-empty>
    </el-container>
</template>

<script>
import wellChart from './wellChart.vue';
import wellTools from './wellTools.vue';
import wellInfo from "./wellInfo.vue";
import { chineseLayer } from './chartUtils';
import {ElMessage} from "element-plus";
import logger from '@/utils/logger';
import deviceInfoService from '@/services/deviceInfoService';

export default {
    name: 'wellDetail',
    components: {
        wellChart,
        wellTools,
        wellInfo
    },
    data() {
        return {
            // 每个图表的loading状态
            loadingStates: {},
            // 当前油井信息
            wellInfo: {
                id: 0,
                name: '',
                level: 0
            },
            // 当前油井层
            layer: null
        };
    },
    created() {},
    async mounted() {
        // 根据URL中的油井层参数
        this.layer = this.$route.params.layer;

        // 初始化油井信息
        await this.fetchWellInfo();
    },
    watch: {},
    beforeUnmount() {
        console.log('beforeUnmount-layer::', this.wellId);
    },
    methods: {
        /**
         * 返回油井页
         */
        backWell() {
            //this.$router.push(`/well-main/?id=${this.wellId}`);
            this.$router.back();
        },
        /**
         * 将对应的层级转换成中文
         */
        chineseLayer(index) {
            return chineseLayer(index);
        },
        /**
         * 获取油井数据
         * @returns {Promise<void>}
         */
        async fetchWellInfo() {
            try {
                // 从路由参数获取设备ID
                const deviceId = this.$route.params.id;

                // 使用统一的设备信息服务获取设备信息
                const deviceInfo = await deviceInfoService.getDeviceInfo(deviceId, {
                    layer: this.layer
                });

                if (!deviceInfo) {
                    // 错误信息已在服务中处理
                    return;
                }

                // 设置设备信息
                this.wellInfo = deviceInfo;

                logger.info('油井数据加载成功:', this.wellInfo);
            } catch (error) {
                ElMessage.error(error.message || '获取油井数据出错');
                logger.error('获取油井数据出错:', error);
            }

            this.loading = false;
        },
        /**
         * 处理图表初始化完成事件
         * @param {number} layerIndex - 图表对应的层级索引
         */
        handleChartInitComplete(layerIndex) {
            this.loadingStates[layerIndex] = false;
        }
    }
};
</script>

<style lang="scss" scoped>
.el-header {
    height: 36px;
    padding: 0 15px;
}

.layer-container {
    flex-grow: 1; /* 自适应高度 */
    overflow: hidden;

    .layer-tools {
        width: 250px;
        border: 0;
        border-left: 1px solid #ededed;
        background-color: #fafdfd;
        overflow: hidden;
    }
}

.el-col {
    display: inline-flex;
    flex-direction: column;
    background-color: #fafafa;
}

html[data-theme='dark'] {
    .layer-tools {
        border-color: var(--el-border-color);
        background-color: var(--color-background);
    }
}
</style>
