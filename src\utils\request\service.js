import axios from 'axios';
import sysConfig from '@/config';
import { requestInterceptor, responseInterceptor, errorInterceptor } from './interceptors';

// 创建 axios 实例
const service = axios.create({
    baseURL: '',
    timeout: sysConfig.TIMEOUT
});

// 添加请求拦截器
service.interceptors.request.use(requestInterceptor, error => Promise.reject(error));

// 添加响应拦截器，传入 service 实例
service.interceptors.response.use(response => responseInterceptor(response, service), errorInterceptor);

export default service;
