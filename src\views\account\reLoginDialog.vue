<template>
    <!--用户登录对话框-->
    <el-dialog
        v-model="dialogVisible"
        :width="dialogWidth"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :draggable="true"
        :show-close="false"
        :show-fullscreen="false"
        :append-to-body="true"
        @open="userLoginDialogOpenEvent"
        @close="userLoginDialogCloseEvent"
        custom-class="login_dialog"
        modal-class="login_modal"
    >
        <!--传入当前登录方式-->
        <loginForm loginMode="dialog"></loginForm>
    </el-dialog>
</template>

<script>
import { useGlobalStore } from '@/store/global';
import { useKeepAliveStore } from '@/store/keepAlive';
import loginForm from './login';
import account from '@/utils/account';
import tool from '@/utils/tool';
import storageConfig from '@/config/storage';

const globalStore = useGlobalStore();
const keepAliveStore = useKeepAliveStore();

export default {
    name: 'reLoginDialog',
    components: {
        loginForm
    },
    data() {
        return {
            dialogWidth: '500px'
        };
    },
    computed: {
        // 将visible改为计算属性，与store进行双向绑定
        dialogVisible: {
            get() {
                return useGlobalStore().userLoginDialogVisible;
            },
            set(value) {
                useGlobalStore().SET_userLoginDialogVisible(value);
            }
        },
        // 判断token是否过期
        tokenWasExpired() {
            return globalStore.userTokenExpired;
        }
    },
    watch: {
        /**
         * 监听全局token是否过期
         * @param _expired 是否已过期
         * @param oldVal 之前状态
         */
        async tokenWasExpired(_expired) {
            // 如果token已过期
            if (_expired === true) {
                // 检查是否有refresh_token
                const _refresh_token = tool.data.get(storageConfig.vars.refreshToken);

                // 如果没有refresh_token（可能是被强制踢出导致被清除），直接显示登录窗口
                if (!_refresh_token) {
                    this.dialogVisible = true;
                    return;
                }

                // 刷新并更新本地token
                const res = await account.refreshToken();

                keepAliveStore.clearKeepLive();

                // 如果刷新成功
                if (res) {
                    this.$message.success({ message: '刷新了token', grouping: true });
                } else {
                    // 显示窗口
                    this.dialogVisible = true;
                }
            } else {
                // 关闭窗口
                this.dialogVisible = false;
            }
        }
    },
    created() {},
    mounted() {},
    methods: {
        /**
         * 用户登录对话框打开事件回调
         */
        async userLoginDialogOpenEvent() {
            // 标记当前窗口状态为已打开
            globalStore.SET_userLoginDialogVisible(true);
        },
        /**
         * 用户登录对话框关闭事件回调
         */
        userLoginDialogCloseEvent() {
            // 用户token标记为非过期，也用于关闭登录框
            globalStore.SET_userTokenExpired(false);

            // 标记当前窗口状态为已关闭
            globalStore.SET_userLoginDialogVisible(false);
        }
    }
};
</script>

<style lang="scss" scoped>
.login_dialog {
    .el-dialog__body {
        padding: 0;
    }
}

:global(.login_modal) {
    backdrop-filter: blur(10px);
    background-color: rgba(0, 0, 0, 0.6) !important;
}
</style>
