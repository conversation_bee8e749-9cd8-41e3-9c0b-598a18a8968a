/*
 * @Description: 工具集
 * @version: 1.2
 * @LastEditors: <PERSON><PERSON><PERSON>@live.com
 * @LastEditTime: 2022/07/27 14:41
 */

import CryptoJS from 'crypto-js';
import sysConfig from '@/config';
import { ElMessage } from 'element-plus';

const tool = {};

/**
 * localStorage
 * @type {{set(*, *, *=): void, get(*): (null|any|undefined), clear(): void, remove(*): void}}
 */
tool.data = {
    set(key, data, datetime = 0) {
        //加密
        if (sysConfig.LS_ENCRYPTION === 'AES') {
            data = tool.crypto.AES.encrypt(JSON.stringify(data), sysConfig.LS_ENCRYPTION_key);
        }
        let cacheValue = {
            content: data,
            datetime: parseInt(datetime) === 0 ? 0 : new Date().getTime() + parseInt(datetime) * 1000
        };
        return localStorage.setItem(key, JSON.stringify(cacheValue));
    },
    get(key) {
        try {
            const value = JSON.parse(localStorage.getItem(key));
            if (value) {
                let nowTime = new Date().getTime();
                if (nowTime > value.datetime && value.datetime !== 0) {
                    localStorage.removeItem(key);
                    return null;
                }
                //解密
                if (sysConfig.LS_ENCRYPTION === 'AES') {
                    value.content = JSON.parse(tool.crypto.AES.decrypt(value.content, sysConfig.LS_ENCRYPTION_key));
                }
                return value.content;
            }
            return null;
        } catch (err) {
            return null;
        }
    },
    remove(key) {
        return localStorage.removeItem(key);
    },
    clear() {
        return localStorage.clear();
    }
};

/**
 * sessionStorage
 * @type {{set(*, *): void, get(*): (null|any), clear(): void, remove(*): void}}
 */
tool.session = {
    set(table, settings) {
        var _set = JSON.stringify(settings);
        return sessionStorage.setItem(table, _set);
    },
    get(table) {
        var data = sessionStorage.getItem(table);
        try {
            data = JSON.parse(data);
        } catch (err) {
            return null;
        }
        return data;
    },
    remove(table) {
        return sessionStorage.removeItem(table);
    },
    clear() {
        return sessionStorage.clear();
    }
};

/**
 * cookie
 * @type {{set(*, *, *=): void, get(*): (string|null), remove(*): void}}
 */
tool.cookie = {
    set(name, value, config = {}) {
        var cfg = {
            expires: null,
            path: null,
            domain: null,
            secure: false,
            httpOnly: false,
            ...config
        };
        var cookieStr = `${name}=${escape(value)}`;
        if (cfg.expires) {
            var exp = new Date();
            exp.setTime(exp.getTime() + parseInt(cfg.expires) * 1000);
            cookieStr += `;expires=${exp.toGMTString()}`;
        }
        if (cfg.path) {
            cookieStr += `;path=${cfg.path}`;
        }
        if (cfg.domain) {
            cookieStr += `;domain=${cfg.domain}`;
        }
        document.cookie = cookieStr;
    },
    get(name) {
        const pattern = new RegExp(`(^| )${name}=([^;]*)(;|$)`);
        const match = document.cookie.match(pattern);
        return match ? decodeURIComponent(match[2]) : null;
    },
    remove(name) {
        var exp = new Date();
        exp.setTime(exp.getTime() - 1);
        document.cookie = `${name}=;expires=${exp.toGMTString()}`;
    }
};

/**
 * Fullscreen
 * @param element
 */
tool.fullscreen = function (element) {
    var isFull = !!(
        document.webkitIsFullScreen ||
        document.mozFullScreen ||
        document.msFullscreenElement ||
        document.fullscreenElement
    );
    if (isFull) {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        }
    } else {
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen();
        } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen();
        }
    }
};

// 对象
tool.object = {
    copy: function (obj) {
        return JSON.parse(JSON.stringify(obj));
    },
    clone: function (source) {
        if (!source && typeof source !== 'object') {
            throw new Error('error arguments', 'deepClone');
        }
        const targetObj = source.constructor === Array ? [] : {};
        Object.keys(source).forEach(keys => {
            if (source[keys] && typeof source[keys] === 'object') {
                targetObj[keys] = tool.object.clone(source[keys]);
            } else {
                targetObj[keys] = source[keys];
            }
        });
        return targetObj;
    }
};

// 验证器
tool.validator = {
    /**
     * 校验是否以为外链
     * @param {string} path
     * @returns {Boolean}
     */
    validExternal: function (path) {
        return /^(https?:|mailto:|tel:)/.test(path);
    },
    /**
     * 校验是否为URL
     * @param {string} url
     * @returns {Boolean}
     */
    validURL: function (url) {
        const reg =
            /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
        return reg.test(url);
    },
    /**
     * 校验是否小写
     * @param {string} str
     * @returns {Boolean}
     */
    validLowerCase: function (str) {
        const reg = /^[a-z]+$/;
        return reg.test(str);
    },
    /**
     * 校验是否大写
     * @param {string} str
     * @returns {Boolean}
     */
    validUpperCase: function (str) {
        const reg = /^[A-Z]+$/;
        return reg.test(str);
    },
    /**
     * 校验是否为字母
     * @param {string} str
     * @returns {Boolean}
     */
    validAlphabets: function (str) {
        const reg = /^[A-Za-z]+$/;
        return reg.test(str);
    },
    /**
     * 校验是否为邮箱
     * @param {string} email
     * @returns {Boolean}
     */
    validEmail: function (email) {
        const reg =
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return reg.test(email);
    }
};

/**
 * ip转整型
 * @param ip
 * @returns {number}
 */
tool.ip2long = function (ip) {
    let num = 0;
    ip = ip.split('.');
    num = Number(ip[0]) * 256 * 256 * 256 + Number(ip[1]) * 256 * 256 + Number(ip[2]) * 256 + Number(ip[3]);
    num = num >>> 0;
    return num;
};

/**
 * 整型转ip
 * @param num
 * @returns {string}
 */
tool.long2ip = function (num) {
    let str;
    const tt = [];
    tt[0] = (num >>> 24) >>> 0;
    tt[1] = ((num << 8) >>> 24) >>> 0;
    tt[2] = (num << 16) >>> 24;
    tt[3] = (num << 24) >>> 24;
    str = String(tt[0]) + '.' + String(tt[1]) + '.' + String(tt[2]) + '.' + String(tt[3]);
    return str;
};

/**
 * 日期时间戳格式化
 * @param date
 * @param fmt
 * @returns {string}
 */
tool.dateFormat = function (date, fmt = 'yyyy-MM-dd hh:mm:ss') {
    if (!date) {
        return '';
    }
    date = new Date(date * 1000);
    var o = {
        'M+': date.getMonth() + 1, //月份
        'd+': date.getDate(), //日
        'h+': date.getHours(), //小时
        'm+': date.getMinutes(), //分
        's+': date.getSeconds(), //秒
        'q+': Math.floor((date.getMonth() + 3) / 3), //季度
        S: date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
        }
    }
    return fmt;
};

/**
 * 千分符
 * @param num
 * @returns {string}
 */
tool.groupSeparator = function (num) {
    num = num + '';
    if (!num.includes('.')) {
        num += '.';
    }
    return num
        .replace(/(\d)(?=(\d{3})+\.)/g, function ($0, $1) {
            return $1 + ',';
        })
        .replace(/\.$/, '');
};

/**
 * 常用加解密
 * @type {{BASE64: {encrypt(*): *, decrypt(*): *}, MD5(*): *, AES: {encrypt(*, *, *=): *, decrypt(*, *, *=): *}}}
 */
tool.crypto = {
    //MD5加密
    MD5(data) {
        return CryptoJS.MD5(data).toString();
    },
    //BASE64加解密
    BASE64: {
        encrypt(data) {
            return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(data));
        },
        decrypt(cipher) {
            return CryptoJS.enc.Base64.parse(cipher).toString(CryptoJS.enc.Utf8);
        }
    },
    //AES加解密
    AES: {
        encrypt(data, secretKey, config = {}) {
            if (secretKey.length % 8 != 0) {
                console.warn('[FRAME error]: 秘钥长度需为8的倍数，否则解密将会失败。');
            }
            const result = CryptoJS.AES.encrypt(data, CryptoJS.enc.Utf8.parse(secretKey), {
                iv: CryptoJS.enc.Utf8.parse(config.iv || ''),
                mode: CryptoJS.mode[config.mode || 'ECB'],
                padding: CryptoJS.pad[config.padding || 'Pkcs7']
            });
            return result.toString();
        },
        decrypt(cipher, secretKey, config = {}) {
            const result = CryptoJS.AES.decrypt(cipher, CryptoJS.enc.Utf8.parse(secretKey), {
                iv: CryptoJS.enc.Utf8.parse(config.iv || ''),
                mode: CryptoJS.mode[config.mode || 'ECB'],
                padding: CryptoJS.pad[config.padding || 'Pkcs7']
            });
            return CryptoJS.enc.Utf8.stringify(result);
        }
    }
};

/**
 * 重新组合新的筛选条件（适用于filterBar筛选出来的条件）
 * @param data
 * @returns {{}}
 */
tool.formatFilterParam = function (data) {
    let dataObj = {};
    for (let key in data) {
        let item = data[key].split('|');
        if (item[0]) {
            dataObj[key] = item[0]; // 第一个为值
        }
    }
    return dataObj;
};

/**
 * 根据传过来的字段进行排序
 * res.data.sort(this.sortByField('create_time'))
 * @param field
 * @returns {*}
 */
tool.sortByField = function (field) {
    return (x, y) => {
        return x[field] - y[field];
    };
};

/**
 * 将字符串放入剪切板
 * @returns {*}
 * @param _string
 */
tool.toCopyClipboard = function (_string) {
    const textarea = document.createElement('textarea');
    textarea.readOnly = true;
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    textarea.value = _string;
    document.body.appendChild(textarea);
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    const result = document.execCommand('Copy');
    if (result) {
        ElMessage.success('复制成功');
        //this.$message.success('复制成功');
    }
    document.body.removeChild(textarea);
};

/**
 * 生成随机密码
 * @param length
 * @param passwordArray
 * @returns {string}
 */
tool.generatePassword = function (length, passwordArray) {
    length = length || 6;
    // 密码串----默认 大写字母 小写字母 数字
    passwordArray = passwordArray || ['ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz', '1234567890'];
    let i;
    let password = '';
    // 随机生成开始字符串
    const startIndex = parseInt(Math.random() * length);
    const randIndex = [];
    for (i = 0; i < length; i++) {
        // 创建数组，用于取随机位置  [0,1,2,3,4,5,....]
        randIndex.push(i);
    }
    for (i = 0; i < length; i++) {
        // 根据随机数组生成随机位置
        const r = parseInt(Math.random() * randIndex.length);
        const num = randIndex[r] + startIndex;
        // 根据随机值取余数
        const randRemainder = num % passwordArray.length;
        // 当前密码串【大写字母，小写字母，数字等】
        const currentPassword = passwordArray[randRemainder];
        // 根据当前密码串长度取随机数
        const index = parseInt(Math.random() * currentPassword.length);
        // 获取随机字符串
        const str = currentPassword.substr(index, 1);
        // 删除随机数组中已经使用的值
        randIndex.splice(r, 1);
        password += str;
    }
    return password;
};

/**
 * 判断是否是json
 * @param str
 * @returns {*}
 */
tool.isJson = function (str) {
    if (typeof str == 'string') {
        try {
            JSON.parse(str);
            return true;
        } catch (e) {
            return false;
        }
    }
};

/**
 * json数据格式化
 * @param str
 * @returns {*}
 */
tool.jsonEncode = function (str) {
    return str ? JSON.stringify(str, null, 4) : '';
};

/**
 * json数据反格式化
 * @param str
 * @returns {*}
 */
tool.jsonDecode = function (str) {
    return tool.isJson(str) ? JSON.stringify(JSON.parse(str), null, 4) : str;
};

/**
 * 字节数转换
 * @param bytes
 * @returns {string}
 */
tool.formatBytes = function (bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
};

/**
 * url参数补全
 * @param url
 * @param paramKey
 * @param paramValue
 * @returns {string}
 */
tool.setUrlParam = function (url, paramKey, paramValue) {
    const regex = new RegExp(`([?&])${paramKey}=.*?(&|$)`, 'i');
    const separator = url.includes('?') ? '&' : '?';
    if (url.match(regex)) {
        return url.replace(regex, `$1${paramKey}=${paramValue}$2`);
    } else {
        return `${url}${separator}${paramKey}=${paramValue}`;
    }
};

tool.timeAgo = function (date) {
    const diff = new Date() - new Date(date);
    const seconds = Math.round(diff / 1000);
    if (seconds < 60) {
        return '刚刚';
    } else if (seconds < 60 * 60) {
        return `${Math.floor(seconds / 60)}分钟前`;
    } else if (seconds < 60 * 60 * 24) {
        return `${Math.floor(seconds / (60 * 60))}小时前`;
    } else if (seconds < 60 * 60 * 24 * 30) {
        return `${Math.floor(seconds / (60 * 60 * 24))}天前`;
    } else if (seconds < 60 * 60 * 24 * 365) {
        return `${Math.floor(seconds / (60 * 60 * 24 * 30))}个月前`;
    } else {
        return `${Math.floor(seconds / (60 * 60 * 24 * 365))}年前`;
    }
};

tool.array = {
    /**
     * 将资源对象中的key名与目标对象一致的key值，赋值进去，不改标目标对象的对象结构
     * @param target 目标对象
     * @param source 资源对象
     */
    cloneExistKey(target, source) {
        Object.keys(target).forEach(key => {
            if (source[key]) {
                target[key] = source[key];
            }
        });
        return target;
    }
};

export default tool;
