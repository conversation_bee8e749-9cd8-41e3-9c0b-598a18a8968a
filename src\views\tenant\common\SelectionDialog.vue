<template>
    <el-dialog
        v-model="visible"
        :title="title"
        :width="width"
        :draggable="draggable"
        :destroy-on-close="destroyOnClose"
        :close-on-click-modal="closeOnClickModal"
        @closed="handleClosed"
        :modal-append-to-body="modalAppendToBody"
        :append-to-body="appendToBody"
    >
        <div class="dialog-content">
            <slot></slot>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleCancel">{{ cancelText }}</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="loading">{{ submitText }}</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script>
export default {
    name: 'SelectionDialog',
    props: {
        // 弹窗是否可见
        modelValue: {
            type: Boolean,
            default: false
        },
        // 弹窗标题
        title: {
            type: String,
            default: '选择'
        },
        // 弹窗宽度
        width: {
            type: String,
            default: '700px'
        },
        // 是否可拖拽
        draggable: {
            type: Boolean,
            default: true
        },
        // 关闭时销毁内容
        destroyOnClose: {
            type: Boolean,
            default: true
        },
        // 点击遮罩层是否关闭
        closeOnClickModal: {
            type: Boolean,
            default: false
        },
        // 是否将modal追加到body
        modalAppendToBody: {
            type: Boolean,
            default: true
        },
        // 是否将弹窗追加到body
        appendToBody: {
            type: Boolean,
            default: true
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 取消按钮文本
        cancelText: {
            type: String,
            default: '取 消'
        },
        // 确定按钮文本
        submitText: {
            type: String,
            default: '确 定'
        }
    },
    computed: {
        visible: {
            get() {
                return this.modelValue;
            },
            set(val) {
                this.$emit('update:modelValue', val);
            }
        }
    },
    methods: {
        /**
         * 处理取消操作
         */
        handleCancel() {
            this.visible = false;
            this.$emit('cancel');
        },

        /**
         * 处理确定操作
         */
        handleSubmit() {
            this.$emit('submit');
        },

        /**
         * 处理弹窗关闭
         */
        handleClosed() {
            this.$emit('closed');
        }
    }
};
</script>

<style lang="scss" scoped>
.dialog-content {
    display: flex;
    flex-direction: column;
    min-height: 200px;
}

.dialog-footer {
    padding-top: 15px;
    text-align: right;
}
</style> 