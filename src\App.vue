<template>
    <startup-loading v-if="loading"></startup-loading>
    <el-config-provider v-else :locale="locale" :size="config.size" :zIndex="config.zIndex" :button="config.button">
        <router-view></router-view>
    </el-config-provider>
</template>

<script>
import account from '@/utils/account';
import { checkBrowserVersion } from '@/utils/browser';
import storageConfig from '@/config/storage';
import theme from '@/utils/theme.js';
import StartupLoading from '@/components/StartupLoading.vue';

export default {
    name: 'App',
    components: {
        StartupLoading
    },
    data() {
        return {
            loading: true,
            isFirstRoute: true,
            config: {
                size: 'default',
                zIndex: 2000,
                button: {
                    autoInsertSpace: false
                }
            }
        };
    },
    computed: {
        locale() {
            return this.$i18n.messages[this.$i18n.locale].el;
        }
    },
    async created() {
        // 检查浏览器版本
        if (!checkBrowserVersion()) {
            // 显示浏览器版本提示...
        }

        // 给容器添加一个layout标识
        document.getElementById('app').classList.add('layout');

        // 初始化主题
        theme.initTheme();
    },
    mounted() {
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
    },
    unmounted() {
        // 清理监听器
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    },
    methods: {
        /**
         * 监听页面可见性变化
         */
        handleVisibilityChange() {
            // 页面可见性变化处理
            if (!this.$TOOL.cookie.get(this.$CONFIG.TOKEN_NAME)) {
                return;
            }
        },

        /**
         * 处理页面加载完成
         */
        async handlePageLoaded() {
            // 确保只处理首次加载
            if (this.isFirstRoute) {
                await this.$nextTick();
                this.isFirstRoute = false;
                this.loading = false;

                // 检查是否有token，如果有则尝试重新连接MQTT
                const accessToken = this.$TOOL.cookie.get(storageConfig.vars.accessToken);
                if (accessToken) {
                    account.connectMqtt();
                }
            }
        }
    },
    watch: {
        // 监听路由变化
        $route: {
            async handler(to, from) {
                // 首次加载时等待页面挂载完成
                if (this.isFirstRoute) {
                    await this.$nextTick();
                    // 给一个较长延时确保页面和组件完全渲染
                    setTimeout(() => {
                        this.handlePageLoaded();
                    }, 300);
                }
            },
            immediate: true
        }
    }
};
</script>

<style lang="scss">
@use '@/assets/scss/init.scss';
</style>
