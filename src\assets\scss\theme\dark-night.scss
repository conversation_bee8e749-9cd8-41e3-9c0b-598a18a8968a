@charset "utf-8";

// 文件描述：暗夜主题
// <AUTHOR> <<PERSON>yu<PERSON>@live.com>
// @Datetime 2022/12/29 16:48

@use 'sass:color';

// 定义基础颜色变量
$primaryColor: #213351;
$primary_fontColor: color.adjust($primaryColor, $lightness: 70%);
$theme_mainColor: color.adjust($primaryColor, $saturation: -5%, $lightness: 5%);
$theme_borderColor: color.adjust($theme_mainColor, $saturation: -12%, $lightness: 10%);

// 暗色主题混入
@mixin dark-theme {
    // 背景色
    --color-background: #{$theme_mainColor};
    // 文字色
    --color-text: #{$primary_fontColor};

    // 深色模式下反色rgb色调
    --inverse-color-rgb-red: var(--light-color-rgb-red);
    --inverse-color-rgb-green: var(--light-color-rgb-green);
    --inverse-color-rgb-blue: var(--light-color-rgb-blue);

    // 只需要覆盖基础颜色，其他会自动按照比例生成
    --el-mask-color: #{$primaryColor};
    --el-color-primary: #{$primaryColor};
    --el-color-primary-light-3: #{color.adjust($theme_mainColor, $lightness: -13%)};
    --el-color-primary-light-5: #{color.adjust($theme_mainColor, $lightness: 2%)};
    --el-color-primary-light-7: #{color.adjust($theme_mainColor, $lightness: 5%)};
    --el-color-primary-light-8: #{color.adjust($theme_mainColor, $lightness: 8%)};
    --el-color-primary-light-9: #{color.adjust($theme_mainColor, $lightness: 9%)};

    // bg & fill
    --el-bg-color: #{color.adjust($theme_mainColor, $lightness: 0%)};
    --el-bg-color-overlay: #{color.adjust($theme_mainColor, $lightness: -4%)};
    --el-fill-color-light: #{color.adjust($theme_mainColor, $lightness: 1%)};
    --el-fill-color-lighter: #{color.adjust($theme_mainColor, $lightness: 2%)};
    --el-fill-color-blank: #{color.adjust($theme_mainColor, $lightness: 4%)};

    // border
    --el-border-color: #{$theme_borderColor};
    --el-border-color-hover: #{color.adjust($theme_borderColor, $lightness: 10%)};
    --el-border-color-light: #{color.adjust($theme_borderColor, $lightness: 1%)};
    --el-border-color-lighter: #{color.adjust($theme_borderColor, $lightness: 2%)};
    --el-border-color-dark: #{color.adjust($theme_mainColor, $lightness: -8%)};
    --el-border-color-darker: #{color.adjust($theme_mainColor, $lightness: -12%)};

    // text
    --el-text-color-regular: #{color.adjust($primary_fontColor, $lightness: -1%)};
    --el-text-color-primary: #{color.adjust($primary_fontColor, $lightness: -5%)};
    --el-text-color-secondary: #{color.adjust($primary_fontColor, $lightness: -15%)};
    --el-text-color-placeholder: #{color.adjust($primary_fontColor, $lightness: -20%, $alpha: -0.5)};
    --el-disabled-text-color: #{color.adjust($primary_fontColor, $lightness: 0%, $alpha: -0.7)};

    // button
    .el-button {
        --el-button-hover-text-color: #{color.adjust($primary_fontColor, $lightness: 0%, $alpha: -0.25)};
        --el-button-hover-bg-color: #{color.adjust($theme_mainColor, $lightness: 15%)};
        --el-button-hover-border-color: #{color.adjust($theme_borderColor, $lightness: 10%)};
        --el-button-active-border-color: #{color.adjust($theme_borderColor, $lightness: 20%)};
    }

    // menu
    --el-menu-text-color: #{color.adjust($primary_fontColor, $lightness: -15%)};
    --el-menu-active-color: #{color.adjust($primary_fontColor, $lightness: 5%)};

    .el-alert,
    .el-empty {
        opacity: 0.5;
    }

    .el-switch__label {
        &.is-active {
            color: var(--el-text-color-secondary);
        }
    }

    .el-form,
    .el-dialog,
    .el-select-dropdown__wrap {
        --el-color-primary: var(--el-disabled-text-color);
    }

    // 下拉菜单悬停样式
    .el-dropdown-menu {
        --el-dropdown-menuItem-hover-fill: var(--el-fill-color-light);
        --el-dropdown-menuItem-hover-color: var(--el-text-color-regular);
    }

    // 分页
    .el-pagination {
        --el-pagination-button-bg-color: var(--el-fill-color-blank);
    }

    .layout-main {
        --el-color-primary: var(--el-border-color-dark);
    }
}

// 系统主题样式 - 仅在auto模式下生效
html[data-theme='auto'] {
    @media (prefers-color-scheme: dark) {
        @include dark-theme;
    }
}

// 自定义深色主题 - 优先级更高
html[data-theme='dark'] {
    @include dark-theme;
}
