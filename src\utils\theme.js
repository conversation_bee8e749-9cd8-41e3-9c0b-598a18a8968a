import colorTool from '@/utils/color';
import tool from '@/utils/tool';
import storageConfig from '@/config/storage';
import config from '@/config';

class Theme {
    /**
     * 构造函数
     */
    constructor() {
        // 获取用户保存的主题色或默认值
        const savedColor = tool.data.get(storageConfig.vars.appPrimaryColor);
        this.appPrimaryColor = savedColor || config.COLOR || config.COLOR_LIST[0];
        this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        // 优先使用用户设置的主题模式，如果没有则使用配置的默认主题模式
        this.appThemeMode = tool.data.get(storageConfig.vars.appThemeMode) || config.DEFAULT_THEME_MODE;
        this.initSystemThemeListener();
    }

    /**
     * 初始化主题
     */
    initTheme() {
        // 优先使用用户设置的主题模式，如果没有则使用配置的默认主题模式
        const themeMode = tool.data.get(storageConfig.vars.appThemeMode) || config.DEFAULT_THEME_MODE;
        document.documentElement.setAttribute('data-theme', themeMode);
        if (themeMode === 'auto') {
            this.setColorForMode('auto');
        } else {
            this.setColorForMode(themeMode);
        }
    }

    /**
     * 初始化系统主题监听
     */
    initSystemThemeListener() {
        // 当处于auto模式时，系统主题变化触发主题颜色更新
        this.mediaQuery.addEventListener('change', e => {
            if (this.appThemeMode === 'auto') {
                this.setColorForMode('auto');
            }
        });
    }

    /**
     * 根据模式设置颜色{深色或（跟随系统时系统为深色）则禁用主题色，否则恢复主题色}
     * @param mode
     */
    setColorForMode(mode) {
        let dark = false;
        if (mode === 'auto') {
            dark = this.mediaQuery.matches;
        } else {
            dark = mode === 'dark';
        }
        if (dark) {
            this.removeThemeColor();
            tool.data.remove(storageConfig.vars.appPrimaryColor);
            // 增加事件通知主题色已清空
            window.dispatchEvent(new CustomEvent('theme-color-change', { detail: { color: null } }));
        } else {
            const savedColor = tool.data.get(storageConfig.vars.appPrimaryColor);
            const colorToUse = savedColor || config.COLOR || config.COLOR_LIST[0];
            this.updateThemeColor(colorToUse, false);
            // 通知外部当前可用主题色
            window.dispatchEvent(new CustomEvent('theme-color-change', { detail: { color: colorToUse } }));
        }
    }

    /**
     * 更新主题颜色
     * @param color
     * @param isUserSet
     */
    updateThemeColor(color, isUserSet = true) {
        if (!color) return;
        document.documentElement.style.setProperty('--el-color-primary', color);
        for (let i = 1; i <= 9; i++) {
            document.documentElement.style.setProperty(
                `--el-color-primary-light-${i}`,
                colorTool.lighten(color, i / 10)
            );
        }
        this.appPrimaryColor = color;
        if (isUserSet && color !== config.COLOR && color !== config.COLOR_LIST[0]) {
            tool.data.set(storageConfig.vars.appPrimaryColor, color);
        }
    }

    /**
     * 移除主题颜色
     */
    removeThemeColor() {
        document.documentElement.style.removeProperty('--el-color-primary');
        for (let i = 1; i <= 9; i++) {
            document.documentElement.style.removeProperty(`--el-color-primary-light-${i}`);
        }
    }

    /**
     * 设置主题模式
     * @param mode
     */
    setAppThemeMode(mode) {
        this.appThemeMode = mode;
        // 当模式为auto时不存缓存
        if (mode !== 'auto') {
            tool.data.set(storageConfig.vars.appThemeMode, mode);
        } else {
            tool.data.remove(storageConfig.vars.appThemeMode);
        }
        document.documentElement.setAttribute('data-theme', mode);
        this.setColorForMode(mode);
    }

    /**
     * 判断是否为系统默认主题模式
     * @param mode
     * @returns {boolean}
     */
    isDefaultThemeMode(mode) {
        return mode === config.DEFAULT_THEME_MODE;
    }

    /**
     * 获取主题模式显示文本
     * @param mode 模式
     * @param label 显示文本
     * @returns {string}
     */
    getThemeModeLabel(mode, label) {
        return `${label}${this.isDefaultThemeMode(mode) ? ' (默认)' : ''}`;
    }
}

export default new Theme();
