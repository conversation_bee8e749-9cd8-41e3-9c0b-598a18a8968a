// 主布局header样式
// <AUTHOR> <<PERSON>yu<PERSON>@live.com>
// @Datetime 2024-12-13

@use '../global/mixins' as *;

/*布局设置*/
.layout-setting {
    @include flex-center;
    position: fixed;
    width: 40px;
    height: 40px;
    border-radius: 3px 0 0 3px;
    bottom: 100px;
    right: 0;
    z-index: 100;
    background: #409eff;
    flex-direction: column;
    cursor: pointer;

    i {
        font-size: 18px;
        color: #ffffff;
    }
}

/*页面最大化、页面全屏*/
.layout.main-maximize,
.layout.main-fullscreen {
    .main-maximize-exit {
        display: block;
    }

    .layout-side-split,
    .layout-side,
    .layout-header,
    .layout-topbar,
    .layout-tabs {
        display: none;
    }
}

.main-maximize-exit {
    display: none;
    position: fixed;
    z-index: 3000;
    top: -20px;
    left: 50%;
    margin-left: -20px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.2);
    text-align: center;

    &:hover {
        background: rgba(0, 0, 0, 0.4);
    }

    i {
        font-size: 14px;
        margin-top: 22px;
        color: #ffffff;
    }
}
