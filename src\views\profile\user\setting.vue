<template>
    <el-card header="语言主题" shadow="never">
        <el-form ref="form" label-width="120px" style="margin-top: 20px">
            <el-form-item label="主题模式">
                <el-select v-model="currentThemeMode">
                    <el-option :label="themeModeLabel('auto', '跟随系统')" value="auto"> </el-option>
                    <el-option
                        :label="themeModeLabel('light', '浅色模式')"
                        value="light"
                        :disabled="isCurrentMode('light')"
                    >
                    </el-option>
                    <el-option
                        :label="themeModeLabel('dark', '深色模式')"
                        value="dark"
                        :disabled="isCurrentMode('dark')"
                    >
                    </el-option>
                </el-select>
                <div class="el-form-item-msg">{{ $t('user.theme_mode_msg') }}</div>
            </el-form-item>
            <el-form-item label="主题颜色">
                <el-color-picker
                    v-model="appPrimaryColor"
                    :predefine="colorList"
                    :disabled="isColorPickerDisabled"
                ></el-color-picker>
            </el-form-item>
            <el-form-item :label="$t('user.language')">
                <el-select v-model="appLang">
                    <el-option label="简体中文" value="zh-cn"></el-option>
                    <el-option label="English" value="en"></el-option>
                </el-select>
                <div class="el-form-item-msg">{{ $t('user.language_msg') }}</div>
            </el-form-item>
        </el-form>
    </el-card>
</template>

<script>
import { useLayoutStore } from '@/store/layout';
import systemConfig from '@/config/index';
import storageConfig from '@/config/storage';
import theme from '@/utils/theme';

export default {
    data() {
        return {
            layoutStore: useLayoutStore(),
            systemDarkMode: window.matchMedia('(prefers-color-scheme: dark)').matches,
            colorList: systemConfig.COLOR_LIST,
            mediaQuery: null,
            themeChangeHandler: null,
            themeColorHandler: null
        };
    },
    computed: {
        /**
         * 当前语言
         */
        appLang: {
            get() {
                return this.$i18n.locale;
            },
            set(val) {
                this.$i18n.locale = val;
                this.$TOOL.data.set(storageConfig.vars.appLang, val);
            }
        },
        /**
         * 当前主题颜色
         */
        appPrimaryColor: {
            get() {
                return this.layoutStore.appPrimaryColor;
            },
            set(val) {
                this.layoutStore.setAppPrimaryColor(val);
            }
        },
        /**
         * 当前主题模式
         */
        currentThemeMode: {
            get() {
                return this.layoutStore.appThemeMode;
            },
            set(value) {
                this.layoutStore.setAppThemeMode(value);
            }
        },
        /**
         * 是否禁用颜色选择器
         * @returns {boolean|boolean}
         */
        isColorPickerDisabled() {
            const mode = this.layoutStore.appThemeMode;
            return mode === 'dark' || (mode === 'auto' && this.systemDarkMode);
        }
    },
    methods: {
        /**
         * 判断当前模式是否为指定模式
         * @param mode
         * @returns {boolean}
         */
        isCurrentMode(mode) {
            if (this.currentThemeMode !== 'auto') return false;
            return this.systemDarkMode ? mode === 'dark' : mode === 'light';
        },
        /**
         * 获取主题模式显示文本
         * @param mode
         * @param label
         * @returns {string}
         */
        themeModeLabel(mode, label) {
            return theme.getThemeModeLabel(mode, label);
        }
    },
    mounted() {
        this.mediaQuery = theme.mediaQuery;
        this.themeChangeHandler = e => {
            this.systemDarkMode = e.matches;
        };
        this.mediaQuery.addEventListener('change', this.themeChangeHandler);
        this.themeColorHandler = e => {
            this.appPrimaryColor = e.detail.color;
            this.layoutStore.appPrimaryColor = e.detail.color;
        };
        window.addEventListener('theme-color-change', this.themeColorHandler);
    },
    beforeDestroy() {
        if (this.mediaQuery && this.themeChangeHandler) {
            this.mediaQuery.removeEventListener('change', this.themeChangeHandler);
        }
        if (this.themeColorHandler) {
            window.removeEventListener('theme-color-change', this.themeColorHandler);
        }
    }
};
</script>

<style></style>
