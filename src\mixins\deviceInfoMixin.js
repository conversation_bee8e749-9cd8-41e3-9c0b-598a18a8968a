/**
 * 设备信息管理 Mixin
 * 统一管理设备信息获取、验证和缓存逻辑
 */
import deviceOnlineService from '@/services/deviceOnlineService';
import logger from '@/utils/logger';
import { ElMessage } from 'element-plus';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';

export default {
    setup() {
        // 统一的 Pinia store 初始化
        const deviceOnlineStore = useDeviceOnlineStore();
        return {
            deviceOnlineStore
        };
    },

    data() {
        return {
            // 设备信息获取状态
            deviceInfoLoading: false,
            // 设备信息获取错误
            deviceInfoError: null
        };
    },

    computed: {
        /**
         * 当前设备ID（从路由或props获取）
         */
        currentDeviceId() {
            return this.deviceId || this.$route.params?.id;
        },

        /**
         * 设备在线状态
         */
        deviceOnline() {
            const deviceId = this.currentDeviceId;
            return deviceId ? this.deviceOnlineStore.isDeviceOnline(deviceId) : false;
        }
    },

    methods: {
        /**
         * 获取设备信息的统一方法
         * @param {Object} options 选项
         * @param {string|number} options.deviceId 设备ID（可选，默认使用当前设备ID）
         * @param {number} options.layer 层级（可选）
         * @param {boolean} options.silent 是否静默模式
         * @param {boolean} options.forceRefresh 是否强制刷新
         * @param {boolean} options.useParentData 是否优先使用父组件数据
         * @returns {Promise<Object|null>} 设备信息
         */
        async fetchDeviceInfo(options = {}) {
            const {
                deviceId = this.currentDeviceId,
                layer,
                silent = false,
                forceRefresh = false,
                useParentData = true
            } = options;

            if (!deviceId) {
                const error = '设备ID不能为空';
                logger.error(error);
                if (!silent) {
                    ElMessage.error(error);
                }
                return null;
            }

            this.deviceInfoLoading = true;
            this.deviceInfoError = null;

            try {
                // 1. 优先使用父组件数据（如果启用）
                if (useParentData) {
                    const parentData = this.getParentDeviceInfo(deviceId);
                    if (parentData) {
                        logger.info('使用父组件设备信息:', parentData);
                        return parentData;
                    }
                }

                // 2. 使用设备在线服务获取信息
                logger.info('开始获取设备信息:', { deviceId, layer });
                
                let deviceInfo = await deviceOnlineService.getDeviceInfo(deviceId, {
                    layer,
                    silent,
                    forceRefresh
                });

                // 3. 如果获取失败且不是强制刷新，尝试强制刷新
                if (!deviceInfo && !forceRefresh) {
                    logger.warn('设备信息获取失败，尝试强制刷新');
                    deviceInfo = await deviceOnlineService.getDeviceInfo(deviceId, {
                        layer,
                        silent,
                        forceRefresh: true
                    });
                }

                // 4. 验证设备信息
                if (deviceInfo) {
                    const validationResult = this.validateDeviceInfo(deviceInfo);
                    if (!validationResult.valid) {
                        this.deviceInfoError = validationResult.error;
                        if (!silent) {
                            ElMessage.error(validationResult.error);
                        }
                        return null;
                    }
                    
                    logger.info('设备信息获取成功:', deviceInfo);
                    return deviceInfo;
                } else {
                    const error = layer ? 
                        '未找到有效的设备层级信息，请检查设备ID和层级是否正确' : 
                        '未找到有效的设备信息，请检查设备ID是否正确';
                    
                    this.deviceInfoError = error;
                    if (!silent) {
                        ElMessage.error(error);
                    }
                    return null;
                }
            } catch (error) {
                const errorMsg = error.message || '获取设备信息时发生错误';
                this.deviceInfoError = errorMsg;
                logger.error('获取设备信息异常:', error);
                
                if (!silent) {
                    ElMessage.error(errorMsg);
                }
                return null;
            } finally {
                this.deviceInfoLoading = false;
            }
        },

        /**
         * 获取父组件的设备信息
         * @param {string|number} deviceId 设备ID
         * @returns {Object|null} 父组件的设备信息
         */
        getParentDeviceInfo(deviceId) {
            // 检查直接父组件
            if (this.$parent?.currentDeviceInfo && this.$parent.currentDeviceId == deviceId) {
                return this.$parent.currentDeviceInfo;
            }

            // 检查祖父组件（用于 wellDetail.vue）
            if (this.$parent?.$parent?.currentDeviceInfo && this.$parent.$parent.currentDeviceId == deviceId) {
                return this.$parent.$parent.currentDeviceInfo;
            }

            return null;
        },

        /**
         * 验证设备信息的有效性
         * @param {Object} deviceInfo 设备信息
         * @returns {Object} 验证结果 {valid: boolean, error?: string}
         */
        validateDeviceInfo(deviceInfo) {
            if (!deviceInfo) {
                return {
                    valid: false,
                    error: '设备信息为空'
                };
            }

            if (typeof deviceInfo !== 'object') {
                return {
                    valid: false,
                    error: `设备信息类型错误，期望 object，实际 ${typeof deviceInfo}`
                };
            }

            if (!deviceInfo.id) {
                return {
                    valid: false,
                    error: '设备信息缺少有效的ID'
                };
            }

            return { valid: true };
        },

        /**
         * 计算分页信息
         * @param {number} level 设备层级数
         * @param {number} pageShow 每页显示数量
         * @returns {number} 总页数
         */
        calculatePageTotal(level, pageShow = 4) {
            return level ? Math.ceil(level / pageShow) : 1;
        },

        /**
         * 获取设备状态类型（用于标签颜色）
         * @param {string|number} deviceId 设备ID（可选，默认使用当前设备ID）
         * @returns {string} 状态类型
         */
        getDeviceStatusType(deviceId = this.currentDeviceId) {
            if (!deviceId) return 'info';
            const isOnline = this.deviceOnlineStore.isDeviceOnline(deviceId);
            return isOnline ? 'success' : 'warning';
        },

        /**
         * 获取设备状态文本
         * @param {string|number} deviceId 设备ID（可选，默认使用当前设备ID）
         * @returns {string} 状态文本
         */
        getDeviceStatusText(deviceId = this.currentDeviceId) {
            if (!deviceId) return '未知';
            const isOnline = this.deviceOnlineStore.isDeviceOnline(deviceId);
            return isOnline ? '在线' : '离线';
        }
    }
};
