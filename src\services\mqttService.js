import mqtt from 'mqtt';
import mqttConfig from '@/config/mqtt';
import { ElMessage, ElNotification } from 'element-plus';
import tool from '@/utils/tool';
import storageConfig from '@/config/storage';
import eventManager from '@/utils/eventManager';
import { MQTT_EVENTS } from '@/events/mqttEvents';
// 导入消息服务，确保全局消息持久化
import mqttMessageService from '@/services/mqttMessageService';
// 导入日志工具
import loggerFactory from '@/utils/logger';
// 创建自定义前缀的MQTT日志记录器
const logger = loggerFactory.createLogger('MqttService');

/**
 * MQTT服务类
 * 负责管理MQTT连接、订阅主题和消息处理
 */
class MqttService {
    constructor() {
        // 核心属性
        this.client = null;
        this._connected = false; // 连接状态
        this._retrying = false; // 重试状态
        this.subscriptions = new Map(); // 存储订阅的主题和回调函数

        // 重连相关
        this.reconnectTimer = null; // 重连定时器
        this.reconnectCount = 0;    // 重连次数
        this.maxReconnectAttempts = 3;  // 最大重连次数

        // 状态监听
        this.listeners = new Set(); // 存储所有状态监听器

        // 连接选项
        this.options = {
            keepalive: 30, // 心跳间隔时间
            reconnectPeriod: 5000, // 重连间隔时间
            connectTimeout: 30 * 1000, // 连接超时时间
            clean: true, // 是否清除会话
            protocolVersion: 4, // MQTT协议版本
            rejectUnauthorized: false, // 是否验证证书
            resubscribe: true, // 是否自动重新订阅
        };

        // 确保消息服务初始化，即使用户未打开mqtt-test组件
        // 这样所有MQTT消息都会被持久化存储
        if (mqttMessageService) {
            logger.info('初始化MQTT消息持久化服务');
        }
    }

    // 已移除自定义日志方法，使用logger工具替代

    get isConnected() {
        return this._connected;
    }

    set isConnected(value) {
        if (this._connected !== value) {
            this._connected = value;
            this._notifyStateChange(value);
        }
    }

    get isRetrying() {
        return this._retrying;
    }

    set isRetrying(value) {
        if (this._retrying !== value) {
            this._retrying = value;
            this._notifyStateChange(this._connected);
        }
    }

    // 添加状态变更监听器
    addStateListener(listener) {
        this.listeners.add(listener);
        // 立即通知当前状态
        listener(this._connected, this._retrying);
    }

    // 移除状态变更监听器
    removeStateListener(listener) {
        this.listeners.delete(listener);
    }

    // 通知所有监听器状态变更
    _notifyStateChange(state) {
        this.listeners.forEach(listener => {
            try {
                listener(state, this._retrying);
            } catch (error) {
                console.error('状态监听器执行错误:', error);
            }
        });
    }

    /**
     * 连接MQTT服务器
     * @param {string} username - 用户名
     * @param {string} password - 连接密码
     * @returns {Promise<boolean>} - 连接是否成功
     */
    async connect(username, password) {
        try {
            if (!username || !password) {
                logger.warn('连接失败：未找到用户信息或token');
                ElMessage.warning('MQTT连接失败：未找到用户信息');
                return false;
            }

            // 如果已经存在连接，先断开
            if (this.client) {
                logger.info('断开现有连接以重新连接');
                this.disconnect(true); // 静默断开
            }

            // 设置重试状态为true
            this.isRetrying = true;

            // 设置MQTT连接参数
            const connectionConfig = {
                url: mqttConfig.serverUrl,
                clientId: this.generateClientId(username),
                username: username,
                password: password
            };

            logger.info('开始连接MQTT服务器', { url: connectionConfig.url, clientId: connectionConfig.clientId });

            // 初始化连接
            const result = await this.init(connectionConfig);

            // 如果连接成功，重置重试状态
            if (result) {
                this.isRetrying = false;
            }

            return result;
        } catch (error) {
            logger.error('连接错误', error);
            this.isRetrying = false;
            ElNotification.error(`MQTT连接错误：${error.message}`);
            return false;
        }
    }

    /**
     * 初始化MQTT客户端连接
     * @param {Object} options - 连接选项
     * @param {string} options.url - MQTT服务器WebSocket地址，例如：ws://localhost:8083/mqtt
     * @param {string} options.clientId - 客户端ID，默认随机生成
     * @param {string} options.username - 用户名
     * @param {string} options.password - 密码
     * @returns {Promise<boolean>}
     */
    async init(options = {}) {
        try {
            // 如果已经存在连接，先断开
            if (this.client) {
                logger.info('断开现有连接以重新初始化');
                this.disconnect();
            }

            // 准备连接选项
            const clientId = options.clientId || this.generateClientId(options.username);
            const connectOptions = {
                ...mqttConfig.options,
                ...this.options,
                clientId: clientId,
                username: options.username || '',
                password: options.password || ''
            };

            // 如果启用了遗嘱消息，添加遗嘱消息配置
            if (mqttConfig.clientStatus.enabled && mqttConfig.clientStatus.willEnabled) {
                logger.info('开启了遗嘱消息，正在配置：');

                // 构建遗嘱消息主题和载荷
                const statusTopic = this._buildStatusTopicForUser(options.username);
                const statusPayload = this._buildStatusPayloadForUser(options.username, 'offline', '异常断开连接');

                // 添加遗嘱消息配置
                connectOptions.will = {
                    topic: statusTopic,
                    payload: JSON.stringify(statusPayload),
                    qos: mqttConfig.clientStatus.willQos || 0,
                    retain: mqttConfig.clientStatus.willRetain || false
                };

                logger.info('已配置遗嘱消息', {
                    topic: statusTopic,
                    payload: statusPayload
                });
            }

            const serverUrl = options.url || mqttConfig.serverUrl;

            logger.info('创建MQTT客户端', { url: serverUrl, clientId: connectOptions.clientId });

            // 创建MQTT客户端
            this.client = mqtt.connect(serverUrl, connectOptions);

            // 绑定事件处理函数到对应的事件
            this.client.on('connect', this._handleConnect.bind(this));
            this.client.on('reconnect', this._onReconnect.bind(this));
            this.client.on('error', this._onError.bind(this));
            this.client.on('message', this._onMessage.bind(this));
            this.client.on('close', this._handleClose.bind(this));

            return true;
        } catch (error) {
            logger.error('初始化错误', error);
            ElMessage.error('MQTT初始化失败');
            return false;
        }
    }

    /**
     * 处理连接成功事件
     * @private
     */
    _handleConnect() {
        // 使用分组记录连接成功过程
        logger.group('MQTT连接成功');
        logger.info('连接详情', {
            clientId: this.client.options.clientId,
            time: new Date().toLocaleTimeString(),
            serverUrl: this.client.options.hostname
        });

        ElMessage.success('消息服务连接成功');

        this.isConnected = true; // 连接成功
        this.isRetrying = false; // 重置重试状态
        this.reconnectCount = 0;

        // 分发连接成功事件
        eventManager.emit(MQTT_EVENTS.CONNECTED);

        logger.info('开始重新订阅和自动订阅主题');
        logger.groupEnd();

        // 连接成功后自动订阅
        this._resubscribeTopics();
        this.autoSubscribeTopics().then(r => {
            // 如果配置了遗嘱消息，发布一个在线状态消息
            this._publishOnlineStatus('online');
        });
    }

    /**
     * 处理连接关闭事件
     * @private
     */
    _handleClose() {
        // 使用分组记录连接断开过程
        logger.group('MQTT连接已断开');
        logger.info('连接详情', {
            clientId: this.client?.options?.clientId || '',
            time: new Date().toLocaleTimeString()
        });

        this.isConnected = false;

        // 分发连接断开事件
        eventManager.emit(MQTT_EVENTS.DISCONNECTED);

        logger.groupEnd();
    }

    /**
     * 订阅主题
     * @param {string} topic - 主题
     * @param {function} callback - 消息回调函数
     * @param {number} qos - 服务质量等级
     * @param {boolean} silent - 是否静默订阅（不显示错误提示）
     * @returns {Promise<boolean>} 订阅是否成功
     */
    subscribe(topic, callback, qos = 0, silent = false) {
        if (!this.isConnected || !this.client) {
            if (!silent) {
                ElMessage.error('MQTT客户端未连接，无法订阅主题');
            }
            logger.warn('客户端未连接，无法订阅主题', { topic });
            return Promise.resolve(false);
        }

        // 使用分组记录订阅过程
        logger.group(`订阅主题: ${topic}`, true); // 默认折叠
        logger.info('主题详情', { topic, qos });

        // 保存订阅信息
        this.subscriptions.set(topic, { topic, callback, qos });

        return new Promise((resolve) => {
            this.client.subscribe(topic, { qos }, (err) => {
                if (err) {
                    if (!silent) {
                        ElMessage.error(`订阅主题 ${topic} 失败`);
                    }
                    logger.error('订阅失败', { error: err.message || err });
                    this.subscriptions.delete(topic);
                    logger.groupEnd();
                    resolve(false);
                } else {
                    logger.info('订阅成功');
                    logger.groupEnd();
                    resolve(true);
                }
            });
        });
    }

    /**
     * 取消订阅主题
     * @param {string} topic - 主题
     * @param {boolean} silent - 是否静默取消订阅（不显示错误提示）
     * @returns {Promise<boolean>} 取消订阅是否成功
     */
    unsubscribe(topic, silent = false) {
        if (!this.isConnected || !this.client) {
            if (!silent) {
                ElMessage.error('MQTT客户端未连接，无法取消订阅主题');
            }
            logger.warn('客户端未连接，无法取消订阅主题', { topic });
            return Promise.resolve(false);
        }

        // 使用分组记录取消订阅过程
        logger.group(`取消订阅主题: ${topic}`, true); // 默认折叠
        logger.info('主题详情', { topic });

        // 移除订阅信息
        this.subscriptions.delete(topic);

        return new Promise((resolve) => {
            this.client.unsubscribe(topic, (err) => {
                if (err) {
                    if (!silent) {
                        ElMessage.error(`取消订阅主题 ${topic} 失败`);
                    }
                    logger.error('取消订阅失败', { error: err.message || err });
                    logger.groupEnd();
                    resolve(false);
                } else {
                    logger.info('取消订阅成功');
                    logger.groupEnd();
                    resolve(true);
                }
            });
        });
    }

    /**
     * 获取所有订阅的主题
     * @returns {Array} - 订阅信息数组
     */
    getSubscriptions() {
        const subscriptionsArray = [];
        for (const [topic, data] of this.subscriptions.entries()) {
            subscriptionsArray.push({
                topic: topic,
                qos: data.qos || 0,
                callback: data.callback
            });
        }
        return subscriptionsArray;
    }

    /**
     * 发布消息到指定主题
     * @param {string} topic - 主题
     * @param {string|Object} payload - 消息内容，对象会被自动转为JSON字符串
     * @param {Object} options - 发布选项
     * @param {boolean} silent - 是否静默发布（不显示错误提示）
     * @returns {Promise<boolean>} 发布是否成功
     */
    publish(topic, payload, options = { qos: 0, retain: false }, silent = false) {
        logger.group(`发布消息 → ${topic}`, true);

        if (!this.client || !this.isConnected) {
            if (!silent) {
                ElMessage.error('MQTT客户端未连接，无法发布消息');
            }
            logger.warn('客户端未连接，无法发布消息', { topic });
            return Promise.resolve(false);
        }

        // 如果payload是对象，转换为JSON字符串
        const message = typeof payload === 'object' ? JSON.stringify(payload) : payload;

        logger.info(`发布 → ${topic} [${message.length}字节] ${message}`);

        return new Promise((resolve) => {
            this.client.publish(topic, message, options, (err) => {
                if (err) {
                    if (!silent) {
                        ElMessage.error(`发布消息到主题 ${topic} 失败`);
                    }
                    // 错误日志总是记录，不管是什么主题
                    logger.error(`发布失败: ${topic} - ${err.message || err}`);
                    logger.groupEnd();
                    resolve(false);
                } else {
                    // 使用事件分发已发送的消息
                    eventManager.emit(MQTT_EVENTS.MESSAGE_SENT, {
                        topic,
                        content: typeof payload === 'object' ? payload : message,
                        qos: options.qos,
                        time: Date.now()
                    });
                    logger.groupEnd();
                    resolve(true);
                }
            });
        });
    }

    /**
     * 断开MQTT连接
     * @param {boolean} silent - 是否静默断开（不显示提示）
     */
    disconnect(silent = false) {
        // 记录断开连接的开始时间
        this._disconnectStartTime = Date.now();

        // 使用简洁的日志格式
        const time = new Date().toLocaleTimeString();
        logger.info(`断开连接 ↓ [${this.client?.options?.clientId || 'unknown'}] ${time}`);

        if (this.client) {
            // 在主动断开连接前，发布一个离线状态消息
            this._publishOnlineStatus('offline');

            this.client.end(true, () => {
                // 只有在非重试且非静默模式下才显示提示
                if (this.reconnectCount === 0 && !silent) {
                    ElMessage.info('MQTT客户端已断开连接');
                }

                logger.info(`连接已断开 ✓ [${Date.now() - this._disconnectStartTime}ms]`);
                this.isConnected = false;
                this.isRetrying = false; // 重置重试状态
                this.subscriptions.clear();
            });
            this.client = null;
        } else {
            logger.info('客户端不存在，无需断开');
        }

        // 清除重连定时器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
            logger.info('已清除重连定时器');
        }

        // 清除心跳定时器
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
            logger.info('已清除心跳定时器');
        }
    }

    /**
     * 重连回调
     * @private
     */
    _onReconnect() {
        this.reconnectCount++;
        this.isRetrying = true; // 设置重试状态

        // 使用分组记录重连过程
        logger.group(`MQTT重连尝试 (${this.reconnectCount}/${this.maxReconnectAttempts})`);
        logger.warn('重连信息', {
            attempt: this.reconnectCount,
            maxAttempts: this.maxReconnectAttempts,
            time: new Date().toLocaleTimeString()
        });

        if (this.reconnectCount <= this.maxReconnectAttempts) {
            ElMessage.warning(`MQTT客户端正在尝试第${this.reconnectCount}次重连...`);
        } else {
            logger.error('重连失败，已达到最大重试次数');
            ElMessage.error(`MQTT连接失败，已达到最大重试次数`);
            this.isRetrying = false; // 重置重试状态
            this.reconnectCount = 0; // 重置重连次数
            this.disconnect(); // 停止重连
        }

        this.isConnected = false;
        logger.groupEnd();
    }

    /**
     * 错误回调
     * @private
     * @param {Error} error - 错误对象
     */
    _onError(error) {
        // 使用分组记录错误信息
        logger.group('MQTT客户端错误', false); // 不折叠错误信息
        logger.error('错误详情', {
            message: error.message,
            stack: error.stack,
            time: new Date().toLocaleTimeString()
        });

        // 非重试时显示连接错误，避免在重连过程中显示过多错误提示
        if (this.reconnectCount === 0) {
            ElNotification.error(`MQTT连接错误：${error.message}`);
        }

        logger.groupEnd();
    }

    /**
     * 消息接收回调
     * @private
     * @param {string} topic - 消息主题
     * @param {Buffer} message - 消息内容
     * @param _packet
     */
    _onMessage(topic, message, _packet) {
        const payload = message.toString();

        // 解析消息内容
        const parsedData = this._parseMessagePayload(payload);

        // 其他消息使用debug级别记录
        logger.info(`收到 ← ${topic} 消息 [${payload.length}字节]`, parsedData);

        // 处理消息
        this._dispatchMessageToSubscribers(topic, parsedData);

        // 使用事件分发接收到的消息
        eventManager.emit(MQTT_EVENTS.MESSAGE_RECEIVED, {
            topic,
            content: parsedData,
            time: Date.now()
        });
    }

    /**
     * 构建状态消息主题
     * @returns {string} 状态主题
     * @private
     */
    _buildStatusTopic() {
        return this._buildStatusTopicForUser(this.client.options.username);
    }

    /**
     * 为指定用户构建状态消息主题
     * @param {string} username - 用户名
     * @returns {string} 状态主题
     * @private
     */
    _buildStatusTopicForUser(username) {
        const usernameParts = username ? username.split('@') : ['', ''];
        const tenantCode = usernameParts[0] || '';
        const userId = usernameParts[1] || '';

        return mqttConfig.clientStatus.topic
            .replace('{$tenantCode}', tenantCode)
            .replace('{$userId}', userId);
    }

    /**
     * 构建状态消息载荷
     * @param {string} type - 状态类型（online/offline/alive）
     * @param {string} reason - 状态变更原因（可选）
     * @returns {Object} 状态载荷
     * @private
     */
    _buildStatusPayload(type, reason = null) {
        return this._buildStatusPayloadForUser(this.client.options.username, type, reason);
    }

    /**
     * 为指定用户构建状态消息载荷
     * @param {string} username - 用户名
     * @param {string} type - 状态类型（online/offline/alive）
     * @param {string} reason - 状态变更原因（可选）
     * @returns {Object} 状态载荷
     * @private
     */
    _buildStatusPayloadForUser(username, type, reason = null) {
        const usernameParts = username ? username.split('@') : ['', ''];
        const tenantCode = usernameParts[0] || '';
        const userId = usernameParts[1] || '';

        const statusPayload = { ...mqttConfig.clientStatus.payload };
        statusPayload.status = type;
        statusPayload.timestamp = Date.now();
        statusPayload.tenantCode = tenantCode;
        statusPayload.userId = userId;

        if (reason) {
            statusPayload.reason = reason;
        } else if (type === 'offline') {
            statusPayload.reason = '正常断开连接';
        }

        return statusPayload;
    }

    /**
     * 发布在线状态消息并启动心跳
     * 与遗嘱消息对应，当客户端正常连接时发布在线状态，并定期发送心跳
     * type为offline时为主动断开连接时发布，与遗嘱消息不同，遗嘱消息是在意外断开时由服务器发布
     * @param {string} type - 状态类型（online/offline）
     * @private
     */
    _publishOnlineStatus(type = 'online') {
        const typeText = type === 'online' ? '在线' : '离线';

        if (mqttConfig.clientStatus.enabled && this.client && this.isConnected) {
            try {
                const statusTopic = this._buildStatusTopic();
                const statusPayload = this._buildStatusPayload(type);

                // 发布在线状态消息
                this.publish(statusTopic, statusPayload, {
                    qos: mqttConfig.clientStatus.qos,
                    retain: mqttConfig.clientStatus.retain
                }, true); // 使用静默模式，避免显示提示

                logger.info(`已发布${typeText}状态消息`, { topic: statusTopic });

                // 如果启用了心跳消息，启动心跳定时器（仅在初次发布在线状态的时候发起）
                if (type === 'online' && mqttConfig.clientStatus.heartbeatEnabled) {
                    this._startHeartbeat(statusTopic, statusPayload);
                }
            } catch (error) {
                logger.error(`发布${typeText}状态消息失败`, error);
            }
        }
    }

    /**
     * 启动心跳定时器
     * @param {string} topic - 心跳主题
     * @param {Object} payload - 心跳消息内容
     * @private
     */
    _startHeartbeat(topic, payload) {
        if (!this.client || !this.isConnected) {
            logger.warn('客户端未连接，无法启动心跳定时器');
            return;
        }

        if (!mqttConfig.clientStatus.enabled || !mqttConfig.clientStatus.heartbeatEnabled) {
            logger.warn('心跳消息未启用，无法启动心跳定时器');
            return;
        }

        // 清除之前的心跳定时器
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
        }

        // 设置心跳间隔（默认30秒）
        const heartbeatInterval = mqttConfig.clientStatus.heartbeatInterval || 30 * 1000;

        // 启动心跳定时器
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected && this.client) {
                try {
                    // 设置状态为心跳
                    payload.status = 'alive';
                    // 更新时间戳
                    payload.timestamp = Date.now();

                    // 发布心跳消息
                    this.publish(topic, payload, { qos: 0, retain: false }, true); // 心跳消息不需要retain

                    // 使用debug级别记录日志，避免日志过多
                    logger.debug(`已发送心跳消息 → ${topic}`);
                } catch (error) {
                    logger.error('发送心跳消息失败', error);
                }
            } else {
                // 如果连接已断开，清除心跳定时器
                clearInterval(this.heartbeatTimer);
                this.heartbeatTimer = null;
            }
        }, heartbeatInterval);

        logger.info(`已启动心跳定时器，间隔: ${heartbeatInterval}ms`);
    }

    /**
     * 解析消息载荷
     * @private
     * @param {string} payload - 消息载荷字符串
     * @returns {any} 解析后的数据
     */
    _parseMessagePayload(payload) {
        try {
            return JSON.parse(payload);
        } catch (e) {
            // 解析失败，返回原始字符串
            return payload;
        }
    }

    /**
     * 将消息分发给订阅者
     * @private
     * @param {string} topic - 消息主题
     * @param {any} data - 解析后的消息数据
     */
    _dispatchMessageToSubscribers(topic, data) {
        // 添加一个集合来跟踪已处理的消息回调
        const processedCallbacks = new Set();

        // 遍历所有订阅，检查是否匹配（包括通配符匹配）
        for (const [subscribedTopic, { callback }] of this.subscriptions.entries()) {
            // 如果回调函数已经被调用过，则跳过
            if (processedCallbacks.has(callback)) {
                continue;
            }

            if (this._topicMatches(subscribedTopic, topic)) {
                try {
                    callback(data, topic);
                    // 记录已处理的回调函数
                    processedCallbacks.add(callback);
                } catch (error) {
                    logger.error(`处理主题 ${topic} 的消息时出错:`, error);
                }
            }
        }
    }

    /**
     * 检查主题是否匹配（支持通配符）
     * @private
     * @param {string} subscribedTopic - 订阅的主题（可能包含通配符）
     * @param {string} publishedTopic - 发布的主题
     * @returns {boolean} 是否匹配
     */
    _topicMatches(subscribedTopic, publishedTopic) {
        // 优化：缓存正则表达式以提高性能
        if (!this._topicRegexCache) {
            this._topicRegexCache = new Map();
        }

        // 检查缓存中是否已有该主题的正则表达式
        if (!this._topicRegexCache.has(subscribedTopic)) {
            // 将主题转换为正则表达式模式
            const pattern = subscribedTopic
                .replace(/\+/g, '[^/]+')
                .replace(/#/g, '.*');
            this._topicRegexCache.set(subscribedTopic, new RegExp(`^${pattern}$`));
        }

        // 使用缓存的正则表达式进行匹配
        return this._topicRegexCache.get(subscribedTopic).test(publishedTopic);
    }

    /**
     * 重新订阅之前的主题
     * @private
     */
    _resubscribeTopics() {
        if (this.subscriptions.size > 0) {
            // 使用debug级别记录日志
            logger.info(`重新订阅主题 (${this.subscriptions.size}个)`);

            // 创建订阅操作的Promise数组
            const subscribePromises = [];

            for (const [topic, { qos, callback }] of this.subscriptions.entries()) {
                // 使用静默模式重新订阅，避免显示过多提示
                subscribePromises.push(
                    this.subscribe(topic, callback, qos, true)
                        .then(success => ({ topic, success }))
                );
            }

            // 等待所有订阅完成
            Promise.all(subscribePromises).then(results => {
                const successCount = results.filter(r => r.success).length;
                const failedTopics = results.filter(r => !r.success).map(r => r.topic);

                // 使用debug级别记录日志
                logger.info(`重新订阅结果: ${successCount}/${results.length} 个主题成功`);

                if (failedTopics.length > 0) {
                    logger.warn(`重新订阅失败: ${failedTopics.length}个主题`);
                }
            }).catch(error => {
                logger.error('重新订阅过程出错', error);
            });
        }
    }

    /**
     * 主题变量替换函数
     * 根据当前用户信息替换主题中的变量
     * 仅支持 {$key} 格式的变量
     * @param {string} topic - 包含变量的主题模板
     * @param {Object} variables - 变量值对象
     * @returns {string} 替换变量后的主题
     */
    replaceTopicVariables(topic, variables) {
        if (!topic || typeof topic !== 'string') return topic;

        let result = topic;

        // 替换所有变量
        if (variables) {
            // 直接使用正则表达式匹配 {$key} 格式的变量
            result = result.replace(/{\$(\w+)}/g, (match, key) => {
                // 检查变量对象中是否有对应的键（不区分大小写）
                const variableKey = Object.keys(variables).find(k =>
                    k.toLowerCase() === key.toLowerCase());

                if (variableKey) {
                    return variables[variableKey];
                }

                return match;
            });
        }

        return result;
    }

    /**
     * 自动订阅配置的主题列表
     * 根据mqttConfig.topics配置中的设置，自动订阅主题
     * @returns {Promise<Array>} 订阅结果数组
     */
    async autoSubscribeTopics() {
        // 检查是否启用自动订阅功能
        if (!mqttConfig.topics || !mqttConfig.topics.enableAutoSubscribe || !this.isConnected) {
            logger.info('自动订阅未启用或客户端未连接');
            return Promise.resolve([]);
        }

        // 获取用户信息，用于替换主题中的变量
        const userInfo = tool.data.get(storageConfig.vars.userInfo) || {};

        // 变量替换对象
        const variables = {
            tenantCode: userInfo.tenant_code || '',
            userId: userInfo.id || ''
        };

        // 如果没有租户编码，则不进行订阅
        if (!variables.tenantCode) {
            logger.warn('自动订阅主题失败：未找到租户编码');
            return Promise.resolve([]);
        }

        // 使用debug级别记录日志
        logger.info(`自动订阅主题 (${mqttConfig.topics.subscribeTopics.length}个)`);

        // 创建订阅Promise数组
        const subscribePromises = mqttConfig.topics.subscribeTopics.map(async topicTemplate => {
            // 替换主题中的变量
            const topic = this.replaceTopicVariables(topicTemplate, variables);

            // 默认消息处理函数
            const defaultHandler = (message, receivedTopic) => {
                // 其他消息使用debug级别记录
                logger.debug(`收到订阅消息 ← ${receivedTopic} [${JSON.stringify(message).length}字节]`);

                // 分发自定义事件，通知UI组件有新的自动订阅消息
                eventManager.emit(MQTT_EVENTS.AUTO_MESSAGE, {
                    topic: receivedTopic,
                    message: message
                });
            };

            // 订阅主题（使用静默模式，避免多个错误提示）
            return this.subscribe(topic, defaultHandler, 0, true)
                .then(success => {
                    return { topic, success };
                });
        });

        // 等待所有订阅完成
        return Promise.all(subscribePromises).then(results => {
            const successCount = results.filter(r => r.success).length;
            const failedTopics = results.filter(r => !r.success).map(r => r.topic);

            // 使用debug级别记录日志
            logger.info(`订阅结果: ${successCount}/${results.length} 个主题成功`);

            if (failedTopics.length > 0) {
                logger.warn(`订阅失败: ${failedTopics.length}个主题`);
            }
            return results;
        }).catch(error => {
            logger.error('自动订阅过程出错', error);
            return [];
        });
    }

    // 在固定clientId基础上添加窗口标识
    generateClientId(username) {
        const windowId = `${Date.now()}_${Math.random().toString(16).substring(2, 8)}`;
        return `${mqttConfig.options.clientIdPrefix}${username}_${windowId}`;
    }
}

// 创建单例
const mqttService = new MqttService();
export default mqttService;
