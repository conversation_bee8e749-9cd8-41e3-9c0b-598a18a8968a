<template>
    <el-tabs type="border-card" v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="登录日志" name="login">
            <user-log-list v-if="activeTab === 'login'" ref="loginLogListRef" :logType="'login'"></user-log-list>
        </el-tab-pane>
        <el-tab-pane label="操作日志" name="operation">
            <user-log-list v-if="activeTab === 'operation'" ref="operationLogListRef" :logType="'operation'"></user-log-list>
        </el-tab-pane>
    </el-tabs>
</template>

<script>
import userLogList from '../logs/list';

export default {
    name: 'user-logs',
    components: {
        userLogList
    },
    data() {
        return {
            activeTab: 'login' // 默认显示登录日志
        };
    },
    mounted() {
        // 页面加载时不需要手动调用getLogList
        // 组件会自动加载数据
    },
    methods: {
        /**
         * 处理标签页切换
         */
        handleTabClick() {
            // Tab切换时，组件会因v-if重新渲染并自动加载数据
        }
    }
};
</script>

<style></style>
