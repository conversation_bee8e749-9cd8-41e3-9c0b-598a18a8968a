import http from '@/utils/request';

export default {
    /**
     * 验证码发送接口
     */
    sendYzm: {
        url: `/tenant/account/sendyzm`,
        name: '发送验证码',
        post: async function (data = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 账号登录接口
     */
    login: {
        url: `/tenant/account/login`,
        name: '登录获取ACCESS_TOKEN',
        post: async function (data = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 退出登录接口
     */
    logout: {
        url: `/tenant/account/logout`,
        name: '退出登录',
        post: async function (data = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取用户菜单接口
     */
    menu: {
        url: `/tenant/account/menu`,
        name: '获取用户菜单',
        get: async function (data = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取用户基本信息接口
     */
    info: {
        url: `/tenant/account/info`,
        name: '验证用户是否登录（也可用作获取用户基本信息）',
        get: async function (data = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 更新用户基本信息接口
     */
    edit: {
        url: `/tenant/account/edit`,
        name: '更新用户基本信息',
        post: async function (data = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 获取用户操作日志接口
     */
    logs: {
        url: `/tenant/account/logs`,
        name: '操作日志',
        get: async function (data = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 强制修改密码是不需要传入原密码
     * 主动修改密码需要验证原密码
     */
    changePassword: {
        url: `/tenant/account/changePassword`,
        name: '重置密码',
        post: async function (data = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 刷新用户token接口
     */
    refreshToken: {
        url: `/tenant/account/refreshToken`,
        name: '刷新用户token',
        post: async function (data = {}) {
            return await http.post(this.url, data);
        }
    },
    /**
     * 验证token合法性接口
     */
    verifyToken: {
        url: `/tenant/account/verifyToken`,
        name: '验证token合法性',
        post: async function (data = {}) {
            return await http.post(this.url, data);
        }
    }
};
