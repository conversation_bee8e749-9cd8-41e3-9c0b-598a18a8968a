<template>
    <el-container>
        <el-row
            v-if="wellInfo.id && pageInfo.currentPage <= pageInfo.pageTotal"
            v-for="page in pageInfo.pageTotal"
            :key="page"
            :class="{ 'well-container': page > 0, show: pageInfo.currentPage === page }"
        >
            <el-col
                v-for="index in pageInfo.pageShow"
                :key="index"
                :span="12"
                v-loading="loadingStates[(page - 1) * pageInfo.pageShow + index]"
            >
                <template v-if="isLayerValid(page, index)">
                    <wellChart
                        v-if="pageInfo.currentPage === page"
                        ref="wellChart"
                        :wellInfo="wellInfo"
                        :layerInfo="{
                            layer: (page - 1) * pageInfo.pageShow + index,
                            name: chineseLayer((page - 1) * pageInfo.pageShow + index)
                        }"
                        :customOptions="{
                            title: {
                                text: `第${chineseLayer((page - 1) * pageInfo.pageShow + index)}层`
                            }
                        }"
                        @chartInitComplete="handleChartInitComplete"
                    />
                </template>
                <el-empty v-else :image-size="100">
                    <template #description>
                        <span style="color: #909399; font-size: 14px">暂无分层数据</span>
                    </template>
                </el-empty>
            </el-col>
        </el-row>
        <el-empty v-else style="width: 100%" :image-size="180" image888="img/404.png">
            <template #description>
                <span style="color: #909399; font-size: 14px">当前设备数据为空或错误</span>
            </template>
        </el-empty>
    </el-container>
</template>

<script>
import wellChart from './wellChart.vue';
import { chineseLayer } from './chartUtils';
import deviceOnlineService from '@/services/deviceOnlineService';

export default {
    name: 'wellMain',
    components: {
        wellChart
    },
    props: {
        // 当前油井信息
        wellInfo: {
            type: Object,
            required: true,
            default: () => ({
                id: 0,
                name: '',
                level: 0
            })
        },
        // 当前油井屏幕分页信息
        pageInfo: {
            type: Object,
            required: true,
            default: () => ({
                currentPage: 1,
                pageShow: 4,
                pageTotal: 1
            })
        }
    },
    data() {
        return {
            loadingStates: {},
            deviceOnline: true, // 设备在线状态
            statusListener: null // 状态监听器引用
        };
    },
    mounted() {
        // 添加设备状态监听
        if (this.wellInfo.id) {
            this.statusListener = deviceOnlineService.addStatusListener(
                this.wellInfo.id,
                this.handleStatusChange
            );
        }
    },
    beforeUnmount() {
        // 移除设备状态监听
        if (this.statusListener && this.wellInfo.id) {
            deviceOnlineService.removeStatusListener(
                this.wellInfo.id,
                this.handleStatusChange,
                this
            );
            this.statusListener = null;
        }
    },
    methods: {
        handleChartInitComplete(layerIndex) {
            this.loadingStates[layerIndex] = false;
        },
        isLayerValid(page, index) {
            const layerIndex = (page - 1) * this.pageInfo.pageShow + index;
            return this.wellInfo.level >= layerIndex && layerIndex > 0;
        },
        chineseLayer(index) {
            return chineseLayer(index);
        },
        /**
         * 处理设备状态变更
         * @param {Object} status 设备状态对象
         */
        handleStatusChange(status) {
            console.log(`wellMain组件接收到设备 ${this.wellInfo.id} 状态变更:`, status.online ? '在线' : '离线');
            this.deviceOnline = status.online;

            // 通知所有图表组件设备状态变更
            if (this.$refs.wellChart) {
                const charts = Array.isArray(this.$refs.wellChart) ? this.$refs.wellChart : [this.$refs.wellChart];
                charts.forEach(chart => {
                    if (chart && chart.handleStatusChange) {
                        chart.handleStatusChange(status);
                    }
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.well-container {
    display: none;
    // 自适应高度
    flex-grow: 1;
    overflow: hidden;

    &.show {
        display: flex;
    }
}

.el-col {
    width: 100%;
    height: 50%;
    display: inline-flex;
    flex-direction: column;
    border-style: solid;
    border-color: var(--el-border-color);

    &:nth-child(1) {
        border-width: 0 1px 1px 0;
    }

    &:nth-child(2) {
        border-width: 0 0 1px 0;
    }

    &:nth-child(3) {
        border-width: 0 1px 0 0;
    }

    &:nth-child(4) {
        border-width: 0;
    }

    .el-empty {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}
</style>
