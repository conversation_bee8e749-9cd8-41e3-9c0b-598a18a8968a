export function getBrowserInfo() {
    const userAgent = window.navigator.userAgent;
    const browserInfo = {
        type: 'unknown',
        version: 'unknown',
        userAgent
    };

    if (document.documentMode) {
        browserInfo.type = 'IE';
        browserInfo.version = document.documentMode + '';
    } else if (userAgent.includes('Firefox')) {
        browserInfo.type = 'Firefox';
        browserInfo.version = userAgent.match(/Firefox\/([\d.]+)/)[1];
    } else if (userAgent.includes('Opera')) {
        browserInfo.type = 'Opera';
        browserInfo.version = userAgent.match(/Opera\/([\d.]+)/)[1];
    } else if (userAgent.includes('Edg')) {
        browserInfo.type = 'Edg';
        browserInfo.version = userAgent.match(/Edg\/([\d.]+)/)[1];
    } else if (userAgent.includes('Chrome')) {
        browserInfo.type = 'Chrome';
        browserInfo.version = userAgent.match(/Chrome\/([\d.]+)/)[1];
    } else if (userAgent.includes('Safari')) {
        browserInfo.type = 'Safari';
        browserInfo.version = userAgent.match(/Safari\/([\d.]+)/)[1];
    }

    return browserInfo;
}

export function checkBrowserVersion() {
    const minVer = {
        Chrome: 71,
        Firefox: 65,
        Safari: 12,
        Edg: 97,
        IE: 999
    };

    const browserInfo = getBrowserInfo();
    const majorVer = parseInt(browserInfo.version);

    return majorVer >= (minVer[browserInfo.type] || 999);
}
