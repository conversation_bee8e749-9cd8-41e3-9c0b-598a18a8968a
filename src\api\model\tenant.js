import http from '@/utils/request';

export default {
    user: {
        list: {
            url: `/tenant/user/list`,
            name: '获取租户用户列表',
            get: async function (params) {
                return await http.post(this.url, params);
            }
        },
        add: {
            url: `/tenant/user/add`,
            name: '添加租户用户',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/tenant/user/edit`,
            name: '编辑租户用户',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/tenant/user/delete`,
            name: '租户用户软删除',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        enable: {
            url: `/tenant/user/enable`,
            name: '用户启用',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        disable: {
            url: `/tenant/user/disable`,
            name: '用户禁用',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        assignRoles: {
            url: `/tenant/user/assignRoles`,
            name: '用户分配角色',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        getDeviceIds: {
            url: `/tenant/user/getDeviceIds`,
            name: '获取用户已分配的设备IDs',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        assignDevices: {
            url: `/tenant/user/assignDevices`,
            name: '用户分配设备',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        }
    },
    role: {
        list: {
            url: `/tenant/role/list`,
            name: '获取角色列表',
            get: async function (params) {
                return await http.post(this.url, params);
            }
        },
        add: {
            url: `/tenant/role/add`,
            name: '添加角色',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/tenant/role/edit`,
            name: '编辑角色',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        delete: {
            url: `/tenant/role/delete`,
            name: '角色软删除',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/tenant/role/status`,
            name: '设置角色状态（禁用启用）',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        setRoles: {
            url: `/tenant/user/setRoles`,
            name: '批量分配角色',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        resetPassword: {
            url: `/tenant/user/resetPassword`,
            name: '批量重置密码',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        getDeviceIds: {
            url: `/tenant/role/getDeviceIds`,
            name: '获取角色已分配的设备IDs',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        assignDevices: {
            url: `/tenant/role/assignDevices`,
            name: '角色分配设备',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        }
    },
    device: {
        myList: {
            url: `/tenant/device/myList`,
            name: '获取当前用户有权控制的设备列表',
            get: async function (data = {}) {
                return await http.post(this.url, data);
            }
        },
        list: {
            url: `/tenant/device/list`,
            name: '设备列表',
            get: async function (data = {}) {
                return await http.post(this.url, data);
            }
        },
        info: {
            url: `/tenant/device/info`,
            name: '获取当前设备信息',
            get: async function (data = {}) {
                return await http.post(this.url, data);
            }
        },
        add: {
            url: `/tenant/device/add`,
            name: '添加设备',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        edit: {
            url: `/tenant/device/edit`,
            name: '编辑设备',
            put: async function (params) {
                return await http.put(this.url, params);
            }
        },
        delete: {
            url: `/tenant/device/delete`,
            name: '删除设备',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        status: {
            url: `/tenant/device/status`,
            name: '设置设备状态',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        batchDelete: {
            url: `/tenant/device/batchDelete`,
            name: '批量删除设备',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        },
        batchStatus: {
            url: `/tenant/device/batchStatus`,
            name: '批量设置设备状态',
            post: async function (params) {
                return await http.post(this.url, params);
            }
        }
    },

    dic: {
        tree: {
            url: `/tenant/dic/tree`,
            name: '获取字典树',
            get: async function () {
                return await http.get(this.url);
            }
        },
        list: {
            url: `/tenant/dic/list`,
            name: '字典明细',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        },
        get: {
            url: `/tenant/dic/get`,
            name: '获取字典数据',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    }
};
