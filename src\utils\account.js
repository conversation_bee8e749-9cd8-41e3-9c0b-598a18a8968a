/**
 * 系统用户常用函数封装
 */
import { useGlobalStore } from '@/store/global';
import { useKeepAliveStore } from '@/store/keepAlive';
import { useIframeStore } from '@/store/iframe';
import { useViewTabsStore } from '@/store/viewTabs';
import { permission } from '@/utils/permission';
import { ElMessage, ElMessageBox } from 'element-plus';
import account from '@/api/model/account';
import router from '@/router';
import storageConfig from '@/config/storage';
import tool from '@/utils/tool';
import mqttService from '@/services/mqttService';

export default {
    /**
     * 验证是否为邮箱
     * @returns {*}
     */
    isEmail(str) {
        return /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(str);
    },
    /**
     * 验证是否为手机号
     * @returns {*}
     */
    isMobile(number) {
        const reg = /^1[3456789]\d{9}$/;
        if (number === '' || number === undefined || number == null || number.length === 0) {
            return false;
        } else return reg.test(number);
    },
    /**
     * 任意类型登录后置操作封装
     * @param res
     * @param redirect 是否重定向
     * @returns {Promise<boolean>}
     */
    async loginSuccess(res, redirect) {
        redirect = redirect || false;

        // 用户token放入cookie
        tool.cookie.set(storageConfig.vars.accessToken, res.data.access_token, {
            //expires: res.data.access_expires_in
            expires: 365 * 24 * 60 * 60
        });

        if (res.data.refresh_token) {
            // 用户refresh_token放入本地缓存
            //expires: res.data.refresh_expires_in
            tool.data.set(storageConfig.vars.refreshToken, res.data.refresh_token);
        }

        if (res.data.userInfo) {
            // 用户信息放入本地缓存
            this.userInfo.set(res.data.userInfo);
        }

        if (res.data.menu_data) {
            // 菜单数据放入本地缓存
            tool.data.set(storageConfig.vars.userMenu, res.data.menu_data || []);
        }

        if (res.data.permission_data) {
            // 权限节点的别名数据集放入本地缓存
            tool.data.set(storageConfig.vars.userPermission, res.data.permission_data || []);
        }

        const globalStore = useGlobalStore();

        // 用户token标记为非过期，也用于关闭登录框
        globalStore.SET_userTokenExpired(false);

        // 登录对话框标记为已关闭
        globalStore.SET_userLoginDialogVisible(false);

        // 自动连接MQTT服务器
        this.connectMqtt();

        // 如果是登录对话框模式，就关闭当前对话框，否则跳转到首页
        if (redirect) {
            await router.replace({
                path: '/dashboard'
            });
        }

        return true;
    },
    /**
     * 清除用户本地缓存
     */
    async clearUserCaches() {
        // 断开MQTT连接
        if (mqttService.isConnected) {
            mqttService.disconnect();
        }

        tool.cookie.remove(storageConfig.vars.accessToken);
        tool.data.clear();

        const globalStore = useGlobalStore();
        const keepAliveStore = useKeepAliveStore();
        const iframeStore = useIframeStore();
        const viewTabsStore = useViewTabsStore();

        viewTabsStore.clearViewTabs();
        keepAliveStore.clearKeepLive();
        iframeStore.clearIframeList();

        globalStore.SET_userTokenExpired(false);
        globalStore.SET_userLoginDialogVisible(false);

        await router.replace({ path: '/login' });
    },

    /**
     * 刷新token方法
     * @returns {Promise<boolean>}
     */
    async refreshToken() {
        // 获取本地缓存中的refresh_token
        const _refresh_token = tool.data.get(storageConfig.vars.refreshToken);

        if (_refresh_token) {
            const res = await account.refreshToken.post({ refresh_token: _refresh_token });

            if (res.status === 1) {
                // 任意类型登录后置操作封装
                await this.loginSuccess(res, false);

                return true;
            } else {
                ElMessage.warning(res.message);
                return false;
            }
        }

        ElMessage.warning('刷新token时缺少refresh_token');
        return false;
    },
    /**
     * 用户信息本地缓存处理
     */
    userInfo: {
        // 获取用户信息缓存
        get() {
            let _info = tool.data.get(storageConfig.vars.userInfo);
            if (_info && _info.gender !== undefined) {
                // 性别转换为字符串
                _info.gender = _info.gender.toString();
                // 添加性别文本属性
                const gender = parseInt(_info.gender) || 0;
                _info.gender_text = gender === 0 ? '保密' : gender === 1 ? '男' : gender === 2 ? '女' : '未知';
            }
            return _info;
        },
        // 设置用户信息缓存
        set(info) {
            let _data = this.get() || {};

            // 空对象时不设置
            if (Object.keys(info).length === 0 || typeof info != 'object') {
                return _data;
            }

            _data = Object.assign({}, _data, info);

            tool.data.set(storageConfig.vars.userInfo, _data);

            return this.get();
        },
        // 删除用户信息缓存
        remove() {
            return tool.data.remove(storageConfig.vars.userInfo);
        }
    },
    /**
     * 退出登录系统方式
     */
    async logoutSystem(reload) {
        ElMessageBox.confirm('确认是否退出当前用户？', '提示', {
            type: 'warning',
            confirmButtonText: '退出',
            confirmButtonClass: 'el-button--danger'
        })
            .then(async () => {
                const res = await account.logout.post();

                ElMessage.success(res.message);

                // 清除本地缓存
                await this.clearUserCaches();

                if (reload) {
                    window.location.reload();
                }
            })
            .catch(() => {
                // 取消退出
            });
    },
    /**
     * 连接MQTT服务器
     * 在用户登录成功后自动调用
     */
    connectMqtt() {
        // 获取用户信息和token
        const userInfo = this.userInfo.get();
        const password = tool.cookie.get(storageConfig.vars.accessToken);

        // 如果没有用户信息或token，则不进行连接
        if (!userInfo || !password) {
            console.warn('MQTT连接失败：未找到用户信息或token');
            return false;
        }

        // 设置连接状态变更回调
        /*mqttService.onConnectionChange = (status) => {
            if (!status) {
                console.warn('MQTT连接已断开');
            } else {
                console.log('MQTT连接已建立');
                // 连接成功后自动订阅配置的主题
                mqttService.autoSubscribeTopics();
            }
        };*/

        // 连接时的用户名
        const username = `${userInfo.tenant_code}@${userInfo.id}`;

        // 连接MQTT服务器
        mqttService.connect(username, password);

        return true;
    },

    /**
     * 根据性别获取默认头像信息
     * @param gender
     */
    getDefaultAvatarInfo(gender) {
        const _gender = parseInt(gender) || 0;
        return {
            // 头像地址
            url: _gender === 2 ? 'img/avatar-w.svg' : _gender === 1 ? 'img/avatar-m.svg' : 'img/avatar-a.svg',
            // 性别class
            class: _gender === 2 ? 'woman' : _gender === 1 ? 'man' : 'unknown'
        };
    },
    /**
     * 获取性别文本
     * @param gender
     * @returns {string}
     */
    getGenderText(gender) {
        const _gender = parseInt(gender) || 0;
        const genderMap = {
            0: '保密',
            1: '男',
            2: '女'
        };
        return genderMap[_gender] || '保密';
    },
    /**
     * 用户权限集检测封装（data支持数组，且可以不用v-auth，在其它地方判断）
     * @param data
     */
    checkPermission(data) {
        let _has = false;
        if (Array.isArray(data)) {
            data.forEach(item => {
                if (permission(item)) {
                    _has = true;
                }
            });
        } else {
            _has = permission(data);
        }
        return _has;
    }
};
