# 事件管理系统

本项目使用集中式事件管理系统，用于处理各种事件的注册、触发和监听。事件管理系统由两部分组成：

1. **事件管理器**：位于 `src/utils/eventManager.js`，提供通用的事件管理功能
2. **事件模块**：位于 `src/events` 目录，包含各种特定类型的事件定义和注册

事件系统采用懒加载机制，只有在实际使用事件时才会初始化相关事件模块，无需在应用启动时全局初始化。

## 目录结构

```
src/
├── events/                  # 事件模块目录
│   ├── index.js             # 事件初始化入口
│   ├── mqttEvents.js        # MQTT相关事件
│   └── README.md            # 本文档
└── utils/
    └── eventManager.js      # 事件管理器
```

## 事件管理器 (eventManager.js)

事件管理器提供以下核心功能：

### 事件监听

```javascript
// 添加事件监听器
eventManager.addEventListener('event-name', handler, context);

// 移除事件监听器
eventManager.removeEventListener('event-name', handler, context);

// 移除所有事件监听器
eventManager.removeAllEventListeners();
// 或移除特定事件的所有监听器
eventManager.removeAllEventListeners('event-name');
```

### 事件触发

```javascript
// 触发事件
eventManager.emit('event-name', data);
```

### 事件注册

```javascript
// 注册事件（提供描述）
eventManager.registerEvent('event-name', '事件描述');

// 获取所有注册的事件
const events = eventManager.getRegisteredEvents();
```

### 定时器管理

```javascript
// 设置定时器
const timerId = eventManager.setInterval(callback, delay);

// 清除定时器
eventManager.clearInterval(timerId);

// 清除所有定时器
eventManager.clearAllIntervals();
```

## 事件模块

事件模块用于定义和注册特定类型的事件，并提供便捷的事件监听方法。

### MQTT事件 (mqttEvents.js)

MQTT事件模块定义了与MQTT相关的事件，并提供了便捷的事件监听方法：

```javascript
import mqttEvents from '@/events/mqttEvents';

// 事件常量
const { EVENTS } = mqttEvents;
// 或直接使用
// import { MQTT_EVENTS } from '@/events/mqttEvents';

// 监听MQTT连接成功事件
mqttEvents.onConnected(() => {
    console.log('MQTT已连接');
}, this);

// 监听MQTT断开连接事件
mqttEvents.onDisconnected(() => {
    console.log('MQTT已断开连接');
}, this);

// 监听MQTT自动订阅消息事件
mqttEvents.onAutoMessage((data) => {
    console.log('收到MQTT自动订阅消息', data);
}, this);

// 监听MQTT消息接收事件
mqttEvents.onMessageReceived((data) => {
    console.log('收到MQTT消息', data);
}, this);

// 监听MQTT消息发送事件
mqttEvents.onMessageSent((data) => {
    console.log('MQTT消息已发送', data);
}, this);
```

## 如何添加新的事件模块

1. 在 `src/events` 目录下创建新的事件模块文件，例如 `userEvents.js`
2. 定义事件常量和初始化函数
3. 提供便捷的事件监听方法
4. 在 `src/events/index.js` 中导入并初始化新的事件模块

示例：

```javascript
// src/events/userEvents.js
import eventManager from '@/utils/eventManager';

// 用户事件名称常量
export const USER_EVENTS = {
    LOGIN: 'user-login',
    LOGOUT: 'user-logout',
    PROFILE_UPDATED: 'user-profile-updated'
};

// 初始化函数
const initialize = () => {
    eventManager.registerEvent(USER_EVENTS.LOGIN, '用户登录事件');
    eventManager.registerEvent(USER_EVENTS.LOGOUT, '用户登出事件');
    eventManager.registerEvent(USER_EVENTS.PROFILE_UPDATED, '用户资料更新事件');

    console.log('用户事件注册完成');
};

// 便捷的事件监听方法
const onLogin = (handler, context) => {
    return eventManager.addEventListener(USER_EVENTS.LOGIN, handler, context);
};

const onLogout = (handler, context) => {
    return eventManager.addEventListener(USER_EVENTS.LOGOUT, handler, context);
};

const onProfileUpdated = (handler, context) => {
    return eventManager.addEventListener(USER_EVENTS.PROFILE_UPDATED, handler, context);
};

// 导出
export default {
    initialize,
    onLogin,
    onLogout,
    onProfileUpdated,
    EVENTS: USER_EVENTS
};

// src/events/index.js
import eventManager from '@/utils/eventManager';
import mqttEvents from './mqttEvents';
import userEvents from './userEvents'; // 导入新的事件模块

const initializeEvents = () => {
    mqttEvents.initialize();
    userEvents.initialize(); // 初始化新的事件模块

    console.log('所有事件已注册完成');
};

export default {
    initialize: initializeEvents
};
```

## 最佳实践

1. **使用常量定义事件名称**：避免硬编码事件名称，减少拼写错误
2. **提供事件描述**：使用 `registerEvent` 方法注册事件时提供描述，便于理解事件的用途
3. **使用便捷的事件监听方法**：为常用事件提供便捷的监听方法，如 `onConnected`、`onLogin` 等
4. **在组件销毁时移除事件监听器**：避免内存泄漏
5. **使用上下文绑定**：传递 `this` 作为上下文，确保事件处理函数中的 `this` 指向正确的对象

## 懒加载机制

事件系统采用懒加载机制，具有以下特点：

1. **按需初始化**：只有在实际使用事件时才会初始化相关事件模块
2. **自动初始化**：无需手动调用初始化方法，系统会在首次使用时自动初始化
3. **避免重复初始化**：系统会检查是否已初始化，避免重复初始化
4. **低侵入性**：不需要在应用启动时全局初始化，降低框架侵入性

如果需要手动初始化事件系统，可以调用：

```javascript
import events from '@/events';

// 手动初始化事件系统
events.initialize();

// 检查事件系统是否已初始化
const isInitialized = events.isInitialized();
```

## 示例：在组件中使用事件

```javascript
// 在组件中使用事件
import mqttEvents from '@/events/mqttEvents';
import eventManager from '@/utils/eventManager';

export default {
    mounted() {
        // 添加事件监听器（会自动初始化MQTT事件模块）
        mqttEvents.onConnected(() => {
            console.log('MQTT已连接');
            this.refreshData();
        }, this);
    },
    beforeDestroy() {
        // 移除事件监听器
        eventManager.removeAllEventListeners(mqttEvents.EVENTS.CONNECTED);
    },
    methods: {
        refreshData() {
            // 刷新数据
        }
    }
};
```
