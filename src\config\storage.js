/**
 * 本地存储（cookie、localStorage、sessionStorage等）常用配置
 */
export default {
    /**
     * 本地数据变量名（cookie &localstorage）
     */
    vars: {
        accessToken: 'accessToken',
        refreshToken: 'refreshToken',
        userInfo: 'userInfo',
        userMenu: 'userMenu',
        userPermission: 'userPermission',
        myMods: 'my-mods',
        searchHistory: 'search_history',
        // 布局相关
        appPrimaryColor: 'app_color',
        appLang: 'app_lang',
        appThemeMode: 'app_theme',
        appLayout: 'app_layout',
        appMenuCollapse: 'app_menu_collapse',
        appShowTabs: 'app_show_tabs'
    },
    /**
     * indexedDB存储对象配置
     */
    indexedDB: {
        // 数据库名称
        dbName: 'systemDB',
        // 数据库版本
        dbVersion: 1,
        // 存储对象详细配置
        storeConfigs: {
            // 在线设备状态存储对象
            onlineDevice: {
                name: 'online_device',
                keyPath: 'deviceId',
                indexes: [
                    { name: 'lastHeartbeat', keyPath: 'lastHeartbeat' }
                ]
            },
            // 快捷菜单存储对象
            quickMenu: {
                name: 'quick_menu',
                keyPath: 'id',
                autoIncrement: true
            },
            // MQTT接收消息存储对象
            mqttReceived: {
                name: 'mqtt_msg_received',
                keyPath: 'id',
                autoIncrement: true,
                indexes: [
                    { name: 'time', keyPath: 'time' }
                ]
            },
            // MQTT发送消息存储对象
            mqttSent: {
                name: 'mqtt_msg_sent',
                keyPath: 'id',
                autoIncrement: true,
                indexes: [
                    { name: 'time', keyPath: 'time' }
                ]
            }
        }
    }
};
