/**
 * 设备相关工具函数
 */
import deviceOnlineService from '@/services/deviceOnlineService';
import logger from '@/utils/logger';

/**
 * 获取父组件的设备信息
 * @param {Object} component Vue组件实例
 * @param {string|number} deviceId 设备ID
 * @returns {Object|null} 父组件的设备信息
 */
export function getParentDeviceInfo(component, deviceId) {
    // 检查直接父组件
    if (component.$parent?.currentDeviceInfo && component.$parent.currentDeviceId == deviceId) {
        return component.$parent.currentDeviceInfo;
    }

    // 检查祖父组件（用于 wellDetail.vue）
    if (component.$parent?.$parent?.currentDeviceInfo && component.$parent.$parent.currentDeviceId == deviceId) {
        return component.$parent.$parent.currentDeviceInfo;
    }

    return null;
}

/**
 * 智能获取设备信息（优先使用父组件数据）
 * @param {Object} component Vue组件实例
 * @param {string|number} deviceId 设备ID
 * @param {Object} options 选项
 * @returns {Promise<Object|null>} 设备信息
 */
export async function smartFetchDeviceInfo(component, deviceId, options = {}) {
    // 1. 优先使用父组件数据
    const parentData = getParentDeviceInfo(component, deviceId);
    if (parentData) {
        logger.info('使用父组件设备信息:', parentData);
        return parentData;
    }

    // 2. 使用服务获取
    return await deviceOnlineService.getDeviceInfo(deviceId, options);
}

/**
 * 计算分页总数
 * @param {number} level 设备层级数
 * @param {number} pageShow 每页显示数量
 * @returns {number} 总页数
 */
export function calculatePageTotal(level, pageShow = 4) {
    return level ? Math.ceil(level / pageShow) : 1;
}
