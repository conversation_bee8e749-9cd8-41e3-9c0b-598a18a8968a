## 合力云运维管理平台前端项目

## 介绍
基于devMix是一个中后台前端解决方案，基于VUE3和elementPlus实现。
使用最新的前端技术栈（vite+vue3+elementUI+sass+JavaScript+pinia等），提供各类实用的组件方便在业务开发时的调用，并且持续性的提供丰富的业务模板帮助你快速搭建企业级中后台前端任务。

devMix的宗旨是：让一切复杂的东西傻瓜化。


## 特点

- **组件** 多个独家组件、业务模板
- **权限** 完整的鉴权体系和高精度权限控制
- **布局** 提供多套布局模式，满足各种视觉需求
- **API** 完善的API管理，使用真实网络MOCK
- **配置** 统一的全局配置和组件配置，支持build后配置热更新
- **性能** 在减少带宽请求和前端算力上多次优化，并且持续着
- **其他** 多功能视图标签、动态权限菜单、控制台组态化、统一异常处理等等

## 安装教程
``` sh
# 克隆项目
git clone https://gitee.com/teng315/helioCloud-devops-frontend.git

# 进入项目目录
cd devmix

# 安装依赖
npm i

# 启动项目(开发模式)
npm run serve
```

**开发环境基于：**

- node版本>=18.8.0
- npm版本>=9.8.1

启动完成后浏览器访问 http://localhost:2800
