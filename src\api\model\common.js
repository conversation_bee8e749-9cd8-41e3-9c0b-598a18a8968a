import http from '@/utils/request';

export default {
    system: {
        info: {
            url: `/tenant/common/info`,
            name: '获取应用常用配置信息接口',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    },
    sentences: {
        url: `https://v1.hitokoto.cn/`,
        name: '<PERSON><PERSON><PERSON>',
        /**
         * config.external参数为true时请求外部域名
         * @param data
         * @param config
         * @returns {Promise<unknown>}
         */
        get: async function (data, config = { external: true }) {
            const _url = data.custom ? this.url + data.custom : this.url;
            const _data = data;
            if (_data.custom) {
                delete _data.custom;
            }
            return await http.get(_url, _data, config);
        }
    },
    upload: {
        url: `/upload`,
        name: '文件上传',
        post: async function (data, config = {}) {
            return await http.post(this.url, data, config);
        }
    },
    uploadFile: {
        url: `/uploadFile`,
        name: '附件上传',
        post: async function (data, config = {}) {
            return await http.post(this.url, data, config);
        }
    },
    exportFile: {
        url: `/fileExport`,
        name: '导出附件',
        get: async function (data, config = {}) {
            return await http.get(this.url, data, config);
        }
    },
    importFile: {
        url: `/fileImport`,
        name: '导入附件',
        post: async function (data, config = {}) {
            return await http.post(this.url, data, config);
        }
    },
    file: {
        menu: {
            url: `/file/menu`,
            name: '获取文件分类',
            get: async function () {
                return await http.get(this.url);
            }
        },
        list: {
            url: `/file/list`,
            name: '获取文件列表',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    },
    docs: {
        tree: {
            url: `/common/docs/tree`,
            name: '获取文档目录结构',
            get: async function () {
                return await http.get(this.url);
            }
        },
        content: {
            url: `/common/docs/content`,
            name: '获取文档内容',
            get: async function (params) {
                return await http.get(this.url, params);
            }
        }
    }
};
