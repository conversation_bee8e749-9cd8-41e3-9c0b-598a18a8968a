<template>
    <div class="topic-management">
        <div class="topic-header">
            <h4>主题管理</h4>
            <el-tag type="info" size="small" class="topic-count">
                {{ subscribedTopics.length }} 个主题
            </el-tag>
        </div>
        <div class="topic-add">
            <el-autocomplete
                v-model="newTopic"
                :fetch-suggestions="queryHistoryTopics"
                placeholder="请输入要订阅的主题"
                :disabled="!isConnected"
                @keyup.enter="addTopic"
                class="topic-input"
                :trigger-on-focus="true"
            >
                <template #prepend>
                    <el-select v-model="qosLevel" class="qos-select" :disabled="!isConnected">
                        <el-option label="QoS 0（最多一次）" :value="0" />
                        <el-option label="QoS 1（至少一次）" :value="1" />
                        <el-option label="QoS 2（恰好一次）" :value="2" />
                    </el-select>
                </template>
                <template #append>
                    <el-button
                        type="primary"
                        @click="addTopic"
                        :disabled="!isConnected || !newTopic"
                        :loading="subscribing"
                        class="subscribe-btn"
                    >
                        订阅
                    </el-button>
                </template>
                <template #default="{ item }">
                    <div class="topic-suggestion">
                        <span>{{ item.value }}</span>
                        <el-tag size="small" class="qos-tag" :type="qosTagType(item.qos)">QoS {{ item.qos }}</el-tag>
                    </div>
                </template>
            </el-autocomplete>
        </div>
        <div class="topic-list">
            <el-scrollbar>
                <div v-if="subscribedTopics.length === 0" class="no-topics">
                    <el-empty description="暂无订阅主题" />
                </div>
                <el-card v-for="topic in subscribedTopics" :key="topic.topic" class="topic-item">
                    <div class="topic-content">
                        <div class="topic-info">
                            <el-tooltip
                                :content="topic.topic"
                                placement="top"
                                :show-after="500"
                                :hide-after="0"
                            >
                                <span class="topic-text" @click="copyTopic(topic.topic)" style="cursor: pointer;">
                                    {{ topic.topic }}
                                </span>
                            </el-tooltip>
                            <el-tag size="small" class="qos-tag" :type="qosTagType(topic.qos)">
                                QoS {{ topic.qos }}
                            </el-tag>
                        </div>
                        <el-tooltip
                            content="自动订阅的主题无法手动取消"
                            :disabled="!topic.isAutoSubscribed"
                            placement="top"
                        >
                            <el-button
                                type="danger"
                                size="small"
                                @click="removeTopic(topic.topic)"
                                :disabled="!isConnected || topic.isAutoSubscribed"
                                :loading="unsubscribing === topic.topic"
                                class="unsubscribe-btn"
                            >
                                取消订阅
                            </el-button>
                        </el-tooltip>
                    </div>
                </el-card>
            </el-scrollbar>
        </div>
    </div>
</template>

<script>
import mqttService from '@/services/mqttService';
import { ElMessage } from 'element-plus';

export default {
    name: 'TopicManagement',
    props: {
        isConnected: {
            type: Boolean,
            required: true
        }
    },
    data() {
        return {
            newTopic: '',
            qosLevel: 0,
            subscribedTopics: [],
            subscribing: false,
            unsubscribing: null,
            topicHistory: [],
            autoSubscribeCompleted: false
        };
    },
    created() {
        this.loadTopicHistory();
        // 从 mqttService 加载已订阅的主题
        this.loadExistingSubscriptions();
        // 添加订阅状态监听
        mqttService.addStateListener(this.handleConnectionChange);
    },

    beforeDestroy() {
        // 移除订阅状态监听
        mqttService.removeStateListener(this.handleConnectionChange);
    },
    methods: {
        loadTopicHistory() {
            const history = localStorage.getItem('mqtt_topic_history');
            this.topicHistory = history ? JSON.parse(history) : [];
        },

        saveTopicHistory() {
            const maxHistoryItems = 10;
            const existingIndex = this.topicHistory.findIndex(item =>
                item.topic === this.newTopic && item.qos === this.qosLevel
            );

            if (existingIndex !== -1) {
                this.topicHistory.splice(existingIndex, 1);
            }

            this.topicHistory.unshift({
                topic: this.newTopic,
                qos: this.qosLevel
            });

            if (this.topicHistory.length > maxHistoryItems) {
                this.topicHistory = this.topicHistory.slice(0, maxHistoryItems);
            }

            localStorage.setItem('mqtt_topic_history', JSON.stringify(this.topicHistory));
        },

        queryHistoryTopics(queryString, cb) {
            const results = queryString
                ? this.topicHistory.filter(item =>
                    item.topic.toLowerCase().includes(queryString.toLowerCase())
                  )
                : this.topicHistory;

            cb(results.map(item => ({
                value: item.topic,
                label: `${item.topic} (QoS ${item.qos})`,
                qos: item.qos
            })));
        },

        handleSelect(item) {
            if (item) {
                this.qosLevel = item.qos;
            }
        },

        loadExistingSubscriptions() {
            const existingSubscriptions = mqttService.getSubscriptions() || [];
            this.subscribedTopics = existingSubscriptions.map(sub => ({
                topic: sub.topic,
                qos: sub.qos || 0,
                isAutoSubscribed: !this.autoSubscribeCompleted // 在自动订阅完成前添加的主题标记为自动订阅
            }));
            // 通知父组件主题变化
            this.$emit('topics-change', this.subscribedTopics);
        },

        handleConnectionChange(status) {
            if (status) {
                // 连接成功后等待自动订阅完成
                this.waitForAutoSubscribe();
            }
        },

        async waitForAutoSubscribe() {
            // 等待一段时间，确保自动订阅完成
            await new Promise(resolve => setTimeout(resolve, 100));
            // 加载订阅列表（包括自动订阅的主题）
            this.loadExistingSubscriptions();
            this.autoSubscribeCompleted = true;
        },

        async addTopic() {
            if (!this.newTopic.trim()) return;
            if (this.subscribedTopics.some(t => t.topic === this.newTopic)) {
                ElMessage.warning('该主题已订阅');
                return;
            }

            this.subscribing = true;
            try {
                const success = await mqttService.subscribe(this.newTopic, this.handleMessage, this.qosLevel);
                if (success) {
                    this.subscribedTopics.push({
                        topic: this.newTopic,
                        qos: this.qosLevel,
                        isAutoSubscribed: false
                    });
                    this.$emit('topics-change', this.subscribedTopics);
                    this.saveTopicHistory();
                    ElMessage.success(`已订阅主题: ${this.newTopic} (QoS ${this.qosLevel})`);
                    this.newTopic = '';
                    this.qosLevel = 0;
                }
            } finally {
                this.subscribing = false;
            }
        },

        async removeTopic(topic) {
            this.unsubscribing = topic;
            try {
                const success = mqttService.unsubscribe(topic);
                if (success) {
                    this.subscribedTopics = this.subscribedTopics.filter(t => t.topic !== topic);
                    this.$emit('topics-change', this.subscribedTopics);
                    ElMessage.success(`已取消订阅主题: ${topic}`);
                }
            } finally {
                this.unsubscribing = null;
            }
        },

        handleMessage(payload, topic) {
            console.log("message-received333:", { payload, topic });
            this.$emit('message-received', { payload, topic });
        },

        qosTagType(qos) {
            const types = ['info', 'success', 'warning'];
            return types[qos] || 'info';
        },

        copyTopic(topic) {
            navigator.clipboard.writeText(topic)
                .then(() => {
                    ElMessage.success('主题已复制到剪贴板');
                })
                .catch(() => {
                    ElMessage.error('复制失败，请手动复制');
                });
        }
    }
};
</script>

<style scoped lang="scss">
.topic-management {
    .topic-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--el-border-color-light);

        h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
            color: var(--el-text-color-primary);
        }

        .topic-count {
            font-size: 12px;
        }
    }

    .topic-add {
        margin-bottom: 15px;

        .topic-input {
            width: 100%;

            .qos-select {
                width: 100px;
            }

            :deep(.el-input-group__append) {
                padding: 0;
                overflow: hidden;

                .subscribe-btn {
                    border: none;
                    border-radius: 0;
                    margin: 0;
                    height: 100%;
                    padding: 0 15px;
                    transition: all 0.3s ease;

                    &:not(:disabled) {
                        color: #fff;
                        background-color: var(--el-color-primary);

                        &:hover {
                            background-color: var(--el-color-primary-light-3);
                        }
                    }
                }
            }
        }
    }

    .topic-list {
        .no-topics {
            padding: 20px 0;
        }

        .topic-item {
            margin-bottom: 10px;
            border: 1px solid var(--el-border-color-light);
            border-radius: 4px;
            box-shadow: none;
            transition: all 0.3s ease;

            &:last-child {
                margin-bottom: 0;
            }

            &:hover {
                //transform: translateY(-1px);
                //box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                border-color: var(--el-color-primary-light-5);
            }

            .topic-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 10px;

                .topic-info {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    min-width: 0; // 确保文本可以正确截断

                    .topic-text {
                        word-break: break-all;
                        font-family: monospace;
                        font-size: 13px;
                        color: var(--el-text-color-primary);
                        display: inline-block;
                        max-width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        cursor: default;
                    }

                    .qos-tag {
                        flex-shrink: 0;
                        margin-left: 10px;
                    }
                }

                .unsubscribe-btn {
                    opacity: 0.8;
                    transition: opacity 0.3s ease;

                    &:hover {
                        opacity: 1;
                    }
                }
            }
        }
    }
}

.topic-suggestion {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 0;

    .qos-tag {
        margin-left: 10px;
    }
}
</style>
