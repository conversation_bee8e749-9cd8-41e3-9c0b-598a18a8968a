/**
 * MQTT工具类
 * 提供MQTT相关的常用工具方法
 */
import mqttService from '@/services/mqttService';
import mqttConfig from '@/config/mqtt';
import tool from '@/utils/tool';
import storageConfig from '@/config/storage';

/**
 * MQTT工具类
 */
export const mqttUtils = {
    /**
     * 检查MQTT是否已连接
     * @returns {boolean} 是否已连接
     */
    isConnected() {
        return mqttService.isConnected;
    },

    /**
     * 获取MQTT连接状态文本
     * @returns {string} 状态文本
     */
    getConnectionStatusText() {
        if (mqttService.isConnected) {
            return 'MQTT已连接';
        } else if (mqttService.isRetrying) {
            return 'MQTT重试连接中...';
        } else {
            return 'MQTT未连接';
        }
    },

    /**
     * 获取MQTT连接状态类型（用于UI样式）
     * @returns {string} 状态类型
     */
    getConnectionStatusType() {
        if (mqttService.isConnected) {
            return 'success';
        } else if (mqttService.isRetrying) {
            return 'warning';
        } else {
            return 'danger';
        }
    },

    /**
     * 构建主题（替换变量）
     * @param {string} topicTemplate - 主题模板
     * @param {Object} customVariables - 自定义变量（可选）
     * @returns {string} 构建后的主题
     */
    buildTopic(topicTemplate, customVariables = {}) {
        // 获取用户信息
        const userInfo = tool.data.get(storageConfig.vars.userInfo) || {};
        
        // 默认变量
        const defaultVariables = {
            tenantCode: userInfo.tenant_code || '',
            userId: userInfo.id || ''
        };

        // 合并变量
        const variables = { ...defaultVariables, ...customVariables };

        return mqttService.replaceTopicVariables(topicTemplate, variables);
    },

    /**
     * 获取预定义主题
     * @param {string} topicKey - 主题键名
     * @param {Object} customVariables - 自定义变量（可选）
     * @returns {string} 构建后的主题
     */
    getPredefinedTopic(topicKey, customVariables = {}) {
        const topicTemplate = mqttConfig.topics.topicRoute[topicKey];
        if (!topicTemplate) {
            throw new Error(`未找到主题模板: ${topicKey}`);
        }
        return this.buildTopic(topicTemplate, customVariables);
    },

    /**
     * 订阅预定义主题
     * @param {string} topicKey - 主题键名
     * @param {Function} callback - 消息回调函数
     * @param {number} qos - QoS级别（可选）
     * @param {Object} customVariables - 自定义变量（可选）
     * @returns {Promise<boolean>} 是否订阅成功
     */
    async subscribePredefinedTopic(topicKey, callback, qos = 0, customVariables = {}) {
        const topic = this.getPredefinedTopic(topicKey, customVariables);
        return await mqttService.subscribe(topic, callback, qos);
    },

    /**
     * 发布消息到预定义主题
     * @param {string} topicKey - 主题键名
     * @param {any} message - 消息内容
     * @param {Object} options - 发布选项（可选）
     * @param {Object} customVariables - 自定义变量（可选）
     * @returns {Promise<boolean>} 是否发布成功
     */
    async publishToPredefinedTopic(topicKey, message, options = {}, customVariables = {}) {
        const topic = this.getPredefinedTopic(topicKey, customVariables);
        return await mqttService.publish(topic, message, options);
    },

    /**
     * 格式化MQTT消息
     * @param {any} message - 原始消息
     * @returns {string} 格式化后的消息
     */
    formatMessage(message) {
        if (typeof message === 'string') {
            try {
                // 尝试解析JSON并格式化
                const parsed = JSON.parse(message);
                return JSON.stringify(parsed, null, 2);
            } catch {
                return message;
            }
        } else if (typeof message === 'object') {
            return JSON.stringify(message, null, 2);
        } else {
            return String(message);
        }
    },

    /**
     * 检查主题是否匹配（支持通配符）
     * @param {string} pattern - 主题模式（可包含通配符）
     * @param {string} topic - 要检查的主题
     * @returns {boolean} 是否匹配
     */
    topicMatches(pattern, topic) {
        // 将主题转换为正则表达式模式
        const regexPattern = pattern
            .replace(/\+/g, '[^/]+')
            .replace(/#/g, '.*');
        
        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(topic);
    },

    /**
     * 解析设备心跳消息
     * @param {any} message - 心跳消息
     * @returns {Object|null} 解析后的心跳信息
     */
    parseHeartbeatMessage(message) {
        try {
            let heartbeatData;
            
            if (typeof message === 'string') {
                heartbeatData = JSON.parse(message);
            } else if (typeof message === 'object') {
                heartbeatData = message;
            } else {
                return null;
            }

            // 验证心跳消息格式
            if (heartbeatData && typeof heartbeatData.timestamp === 'number') {
                return {
                    deviceId: heartbeatData.deviceId || heartbeatData.device_id,
                    timestamp: heartbeatData.timestamp,
                    status: heartbeatData.status || 'online',
                    data: heartbeatData.data || heartbeatData
                };
            }

            return null;
        } catch (error) {
            console.warn('解析心跳消息失败:', error);
            return null;
        }
    },

    /**
     * 获取MQTT配置信息
     * @returns {Object} 配置信息
     */
    getConfig() {
        return {
            serverUrl: mqttConfig.serverUrl,
            clientIdPrefix: mqttConfig.options.clientIdPrefix,
            keepalive: mqttConfig.options.keepalive,
            reconnectPeriod: mqttConfig.options.reconnectPeriod,
            autoSubscribeEnabled: mqttConfig.topics.enableAutoSubscribe,
            clientStatusEnabled: mqttConfig.clientStatus.enabled,
            heartbeatEnabled: mqttConfig.clientStatus.heartbeatEnabled
        };
    },

    /**
     * 添加MQTT状态监听器
     * @param {Function} listener - 状态监听器
     */
    addStatusListener(listener) {
        mqttService.addStateListener(listener);
    },

    /**
     * 移除MQTT状态监听器
     * @param {Function} listener - 状态监听器
     */
    removeStatusListener(listener) {
        mqttService.removeStateListener(listener);
    }
};

export default mqttUtils;
