<template>
    <selection-dialog
        v-model="dialogVisible"
        title="用户设备授权"
        :loading="loading"
        @submit="submitForm"
        @closed="dialogClosed"
    >
        <device-selector
            ref="deviceSelector"
            :entityInfo="{ title: '用户', content: currentUser.realname }"
            :search-params="searchParams"
            :initial-selection="initialSelection"
            :show-device-status="false"
            @selection-change="handleSelectionChange"
        />
    </selection-dialog>
</template>

<script>
import tableConfig from '@/config/table.js';
import DeviceSelector from '../common/DeviceSelector.vue';
import SelectionDialog from '../common/SelectionDialog.vue';

export default {
    name: 'tenant.user.device',
    components: {
        DeviceSelector,
        SelectionDialog
    },
    data() {
        return {
            dialogVisible: false,
            loading: false,
            selection: [], // 当前选中的设备ID数组
            initialSelection: [], // 初始选中的设备ID数组
            currentUser: {
                id: '',
                realname: ''
            },
            searchParams: {
                keyword: '',
                user_id: '' // 添加用户ID参数
            }
        };
    },
    methods: {
        /**
         * 重置状态
         */
        resetState() {
            this.searchParams = {
                keyword: '',
                user_id: ''
            };
            this.selection = [];
            this.initialSelection = [];
            this.currentUser = {
                id: '',
                realname: ''
            };
        },

        /**
         * 显示对话框并加载数据
         * @param {Object} userData 用户数据
         */
        async show(userData) {
            if (!userData || !userData.id) {
                this.$message.error('用户数据不完整');
                return this;
            }

            // 设置用户信息
            this.currentUser = {
                id: userData.id,
                realname: userData.realname || userData.username
            };
            this.searchParams.user_id = userData.id;

            // 获取已分配的设备ID
            try {
                const res = await this.$API.tenant.user.getDeviceIds.post({
                    id: userData.id
                });

                if (res.code === tableConfig.successCode && res.data) {
                    this.initialSelection = res.data.device_ids ?
                        res.data.device_ids.map(id => Number(id)) : [];
                    this.selection = [...this.initialSelection];
                    console.log('已分配设备ID:', res.data, this.selection);
                }
            } catch (error) {
                console.error('获取用户设备错误:', error);
                this.$message.error('获取已分配设备失败: ' + (error.message || '未知错误'));
            }

            // 显示对话框
            this.dialogVisible = true;

            return this;
        },

        /**
         * 处理选择变更
         * @param {Array} selection 选中的设备ID数组
         */
        handleSelectionChange(selection) {
            this.selection = selection;
        },

        /**
         * 提交表单
         */
        async submitForm() {
            if (!this.currentUser.id) {
                this.$message.warning('用户信息不完整');
                return;
            }

            this.loading = true;
            try {
                const res = await this.$API.tenant.user.assignDevices.post({
                    id: this.currentUser.id,
                    device_ids: this.selection
                });

                if (res.code === tableConfig.successCode) {
                    this.$message.success('设备授权成功');

                    // 通知父组件更新数据
                    this.$emit('deviceUpdated', {
                        userId: this.currentUser.id,
                        device_ids: [...this.selection]
                    });

                    this.dialogVisible = false;
                } else {
                    this.$message.error(res.message || '设备授权失败');
                }
            } catch (error) {
                console.error('设备授权错误:', error);
                this.$message.error('操作失败: ' + (error.message || '未知错误'));
            } finally {
                this.loading = false;
            }
        },

        /**
         * 对话框关闭事件
         */
        dialogClosed() {
            // 重置所有状态
            this.resetState();
            // 发送关闭事件通知父组件
            this.$emit('deviceDialogClosedEmit');
        }
    }
};
</script>

<style lang="scss" scoped>
.dialog-content,
.device-selection-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.user-info {
    .el-form-item {
        margin-bottom: 0;
    }
}

.search-bar {
    display: flex;
    justify-content: flex-end;
}

.right-panel-search {
    display: flex;
    align-items: center;
}

.table-container {
    padding: 0;
    border: 1px solid #efefef;
    height: 300px;
}

.selected-row {
    background-color: var(--el-color-primary-light-9);
}

.el-table {
    cursor: pointer;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
}

.empty-data {
    padding: 30px 0;
    text-align: center;
    color: #909399;

    .empty-tip {
        font-size: 12px;
        margin-top: 8px;
        color: #c0c4cc;
    }
}
</style>
