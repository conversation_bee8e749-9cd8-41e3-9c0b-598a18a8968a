<template>
    <div class="history-data-demo">
        <h2>历史数据功能演示</h2>
        <p>这是一个演示页面，展示历史数据组件的功能。在实际环境中，需要后端API支持。</p>

        <!-- 历史数据组件 -->
        <HistoryData />

        <!-- 功能说明 -->
        <el-card class="demo-info" shadow="never">
            <template #header>
                <h3>功能说明</h3>
            </template>

            <el-descriptions :column="1" border>
                <el-descriptions-item label="设备选择">
                    支持搜索和选择设备，可以远程搜索设备列表
                </el-descriptions-item>
                <el-descriptions-item label="时间范围">
                    支持选择任意时间范围，最大支持30天的数据查询
                </el-descriptions-item>
                <el-descriptions-item label="分层选择">
                    根据选择的设备自动显示可用的分层选项
                </el-descriptions-item>
                <el-descriptions-item label="数据类型">
                    支持多选数据类型：流量、内压、外压、温度、水嘴开度
                </el-descriptions-item>
                <el-descriptions-item label="数据间隔">
                    支持不同的数据聚合间隔：1分钟到1天
                </el-descriptions-item>
                <el-descriptions-item label="图表展示">
                    使用ECharts展示多轴图表，支持缩放和数据筛选
                </el-descriptions-item>
                <el-descriptions-item label="表格展示">
                    支持分页的表格展示，可以查看详细数据
                </el-descriptions-item>
                <el-descriptions-item label="数据导出">
                    支持导出Excel格式的历史数据文件
                </el-descriptions-item>
            </el-descriptions>
        </el-card>

        <!-- API说明 -->
        <el-card class="api-info" shadow="never">
            <template #header>
                <h3>API接口说明</h3>
            </template>

            <h4>1. 查询历史数据</h4>
            <pre class="code-block">
POST /tenant/device/historyData
{
  "deviceId": "设备ID",
  "layer": "分层（可选）",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-01-01 23:59:59",
  "dataTypes": ["flow", "innerPressure", "outerPressure"],
  "interval": "10m",
  "page": 1,
  "pageSize": 50
}
            </pre>

            <h4>2. 导出历史数据</h4>
            <pre class="code-block">
POST /tenant/device/historyDataExport
{
  "deviceId": "设备ID",
  "layer": "分层（可选）",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-01-01 23:59:59",
  "dataTypes": ["flow", "innerPressure", "outerPressure"],
  "interval": "10m"
}
            </pre>

            <h4>3. 返回数据格式</h4>
            <pre class="code-block">
{
  "status": 1,
  "message": "success",
  "data": {
    "total": 1000,
    "chartData": [
      {
        "time": "2024-01-01 00:00:00",
        "flow": 125.5,
        "innerPressure": 22.5,
        "outerPressure": 18.2,
        "temperature": 49.1,
        "valveOpening": 20.0,
        "layer": 1
      }
    ],
    "tableData": [
      // 同chartData格式，用于表格分页显示
    ]
  }
}
            </pre>
        </el-card>
    </div>
</template>

<script>
import HistoryData from './HistoryData.vue';

export default {
    name: 'HistoryDataDemo',
    components: {
        HistoryData
    }
};
</script>

<style lang="scss" scoped>
.history-data-demo {
    padding: 20px;

    h2 {
        color: #303133;
        margin-bottom: 10px;
    }

    p {
        color: #606266;
        margin-bottom: 20px;
    }

    .demo-info {
        margin-top: 30px;

        h3 {
            color: #303133;
            margin: 0;
        }
    }

    .api-info {
        margin-top: 20px;

        h3 {
            color: #303133;
            margin: 0;
        }

        h4 {
            color: #409eff;
            margin: 20px 0 10px 0;
        }

        .code-block {
            background-color: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    }
}
</style>
