<template>
    <div class="header-wrapper">
        <el-menu mode="horizontal" :ellipsis="false" :default-active="activeIndex" @select="handleMenuSelect">
            <el-menu-item class="logo" index="0">
                <img src="@/assets/img/logo-w.svg" alt="智能分注系统" />
                <div class="logo-info">
                    <span class="name">智能分注系统</span>
                    <span class="sub-name">Sl-WF Control System</span>
                </div>
            </el-menu-item>
            <el-menu-item index="1" :disabled="isMonitorDisabled">
                数据监控
                <el-tag v-if="isMonitorDisabled" type="danger" size="small" style="margin-left: 8px;">离线</el-tag>
            </el-menu-item>
            <el-menu-item index="2">历史数据</el-menu-item>
            <el-menu-item index="3">返回列表</el-menu-item>
        </el-menu>
    </div>
</template>

<script>
import { ElMessage } from 'element-plus';
import { useDeviceOnlineStore } from '@/stores/deviceOnline';

export default {
    name: 'mainHeader',
    props: {
        activeTab: {
            type: String,
            default: 'monitor'
        },
        deviceId: {
            type: [String, Number],
            default: null
        }
    },
    setup() {
        // 获取设备在线状态 store
        const deviceOnlineStore = useDeviceOnlineStore();

        return {
            deviceOnlineStore
        };
    },
    computed: {
        activeIndex() {
            return this.activeTab === 'history' ? '2' : '1';
        },
        // 实时获取设备在线状态
        deviceOnline() {
            if (!this.deviceId) return true; // 没有设备ID时默认为在线
            return this.deviceOnlineStore.isDeviceOnline(this.deviceId);
        },
        isMonitorDisabled() {
            // 只有在当前不是监控页面且设备离线时才禁用
            return this.activeTab !== 'monitor' && !this.deviceOnline;
        }
    },
    methods: {
        handleMenuSelect(index) {
            switch (index) {
                case '1':
                    // 检查是否可以切换到监控模式
                    if (this.isMonitorDisabled) {
                        ElMessage.warning('设备离线，无法进入数据监控模式');
                        return;
                    }
                    this.$emit('tab-change', 'monitor');
                    break;
                case '2':
                    this.$emit('tab-change', 'history');
                    break;
                case '3':
                    this.$router.push('/tenant/handle/list');
                    break;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.header-wrapper {
    width: 100%;

    .logo {
        padding-left: 10px;
        border-bottom: 0;
        margin-right: auto;

        img {
            width: 60px;
        }

        .logo-info {
            display: flex;
            flex-direction: column;
            margin-left: 10px;
            line-height: 1.65;

            .name {
                color: rgba(255, 255, 255, 0.75);
                font-weight: bold;
                font-size: 15px;
            }

            .sub-name {
                font-size: 10px;
                color: rgba(255, 255, 255, 0.5);
            }
        }
    }
}

.el-menu--horizontal{
    height: 70px;
    .el-menu-item {
        &.is-active {
            background-color: rgba(255,255,255,0.1);
            border: none;
        }
    }
}
</style>
