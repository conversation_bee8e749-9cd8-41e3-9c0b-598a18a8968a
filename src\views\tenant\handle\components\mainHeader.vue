<template>
    <div class="header-wrapper">
        <el-menu mode="horizontal" :ellipsis="false" :default-active="activeIndex" @select="handleMenuSelect">
            <el-menu-item class="logo" index="0">
                <img src="@/assets/img/logo-w.svg" alt="智能分注系统" />
                <div class="logo-info">
                    <span class="name">智能分注系统</span>
                    <span class="sub-name">Sl-WF Control System</span>
                </div>
            </el-menu-item>
            <el-menu-item index="1">数据监控</el-menu-item>
            <el-menu-item index="2">历史数据</el-menu-item>
            <el-menu-item index="3">返回列表</el-menu-item>
        </el-menu>
    </div>
</template>

<script>
export default {
    name: 'mainHeader',
    props: {
        activeTab: {
            type: String,
            default: 'monitor'
        }
    },
    computed: {
        activeIndex() {
            return this.activeTab === 'history' ? '2' : '1';
        }
    },
    methods: {
        handleMenuSelect(index) {
            switch (index) {
                case '1':
                    this.$emit('tab-change', 'monitor');
                    break;
                case '2':
                    this.$emit('tab-change', 'history');
                    break;
                case '3':
                    this.$router.push('/tenant/handle/list');
                    break;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.header-wrapper {
    width: 100%;

    .logo {
        padding-left: 10px;
        border-bottom: 0;
        margin-right: auto;

        img {
            width: 60px;
        }

        .logo-info {
            display: flex;
            flex-direction: column;
            margin-left: 10px;
            line-height: 1.65;

            .name {
                color: rgba(255, 255, 255, 0.75);
                font-weight: bold;
                font-size: 15px;
            }

            .sub-name {
                font-size: 10px;
                color: rgba(255, 255, 255, 0.5);
            }
        }
    }
}

.el-menu--horizontal{
    height: 70px;
    .el-menu-item {
        &.is-active {
            background-color: rgba(255,255,255,0.1);
            border: none;
        }
    }
}
</style>
